# pathlib用法

## 学习状态: 已学会

### 简介
pathlib是Python 3.4+标准库中的模块，提供面向对象的路径操作方式。

### 路径创建与基本操作

- 使用Path()创建路径对象
  ```python
  from pathlib import Path
  p = Path('my_folder/my_file.txt')
  ```
- 使用Path.cwd()获取当前工作目录
  ```python
  current_dir = Path.cwd()
  ```
- 使用Path.home()获取用户主目录
  ```python
  home_dir = Path.home()
  ```
- 获取路径的各个部分
  ```python
  p.name      # 文件名（含扩展名）
  p.stem      # 文件名（不含扩展名）
  p.suffix    # 文件扩展名
  p.parent    # 父目录
  p.parents   # 所有父目录的序列
  ```

### 文件系统操作

- 创建目录
  ```python
  new_dir = Path('my_new_dir')
  new_dir.mkdir(exist_ok=True)  # exist_ok=True表示如果目录已存在不会报错
  ```
- 创建文件
  ```python
  new_file = Path('my_new_file.txt')
  new_file.touch()  # 创建空文件
  ```
- 检查文件/目录是否存在
  ```python
  if new_file.exists():
      print(f'{new_file} exists')
  ```
- 删除文件
  ```python
  new_file.unlink()  # 删除文件
  ```
- 删除目录
  ```python
  new_dir.rmdir()  # 注意：目录必须为空才能删除
  ```

### 文件读写操作

- 写入文件
  ```python
  file_path = Path('example.txt')
  file_path.write_text('Hello, pathlib!')  # 写入文本内容
  file_path.write_bytes(b'Binary data')   # 写入二进制内容
  ```
- 读取文件
  ```python
  content = file_path.read_text()        # 读取文本内容
  binary_content = file_path.read_bytes() # 读取二进制内容
  ```
- 使用open方法读写文件（与传统方式相同）
  ```python
  with file_path.open('r') as f:
      for line in f:
          print(line.strip())
  ```

### 目录遍历

- 获取目录内容
  ```python
  current_dir = Path('.')
  for item in current_dir.iterdir():
      if item.is_file():
          print(f'File: {item}')
      elif item.is_dir():
          print(f'Directory: {item}')
  ```
- 递归搜索文件
  ```python
  # 查找所有.py文件
  python_files = list(current_dir.glob('**/*.py'))
  print("Python files:", python_files)
  
  # 查找特定模式的文件
  txt_files = list(current_dir.glob('*.txt'))
  print("Text files:", txt_files)
  ```

### 高级用法和实用技巧

- 获取绝对路径
  ```python
  abs_path = file_path.resolve()
  print(f'Absolute path: {abs_path}')
  ```
- 获取文件属性
  ```python
  info = file_path.stat()
  print(f'Size: {info.st_size} bytes')
  print(f'Last modified: {info.st_mtime}')
  ```
- 处理不同操作系统的路径
  ```python
  # Windows路径风格
  windows_path = Path('C:/Program Files/MyApp')
  
  # Linux/macOS路径风格
  linux_path = Path('/usr/local/bin')
  
  # 路径拼接（自动处理不同系统）
  config_path = Path.home() / '.config' / 'myapp'
  ```

### 总结

pathlib提供了现代化的文件路径操作方式，相比传统的os.path模块有以下优势：

1. 面向对象的设计，更直观易懂
2. 自动处理不同操作系统的路径分隔符
3. 更简洁的API，减少样板代码
4. 更强大的功能，如模式匹配、递归遍历等
5. 更安全的操作，如使用exist_ok参数避免目录创建错误

建议练习项目：

1. 编写一个程序，查找特定目录及其子目录中的最大文件
2. 创建一个脚本，清理指定目录中超过一定年龄的临时文件
3. 实现一个简单的文件同步工具，比较两个目录并复制差异文件