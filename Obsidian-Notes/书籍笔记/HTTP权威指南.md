
# 第二章

## URL

- url的语法
	- 大多数url都建立在由9个组件组成的通用格式上
	- 几乎没有哪个url包含了所有的组件
```
大多数URL方案的URL语法都建立在这个由9部分构成的通用格式上：
<scheme>://<user>:<password>@<host>:<port>/<path>; <params>? <query>#<frag>
```

### 组件介绍

- 方案
	- 就是最开头的几个字母。
	- 大小写无关，即http和HTTP等效
	- : 将方案组件与其余部分分隔开
- 主机和端口
	- 不会有人不知道这个吧
- 用户名和密码
	- 说实话 少见。树种提到的例子是ftp
	- 如果某个url 方案要求输入用户名和密码，但是用户访问的时候没有输入，那么浏览器就会自动使用其默认的用户名(默认的用户名是anonymous)和默认的密码(不同浏览器的默认密码不一样)。
	- @ 将密码组件与其余部分分隔开
- 路径
	- 这个就不用我说了。比如，http://www.joes-hardware.com:80/seasonal/index-fall.html这个URL中的路径为/seasonal/index-fall.html。
	- 有一个很陌生的知识，没听说过：每个路径段都有自己的参数（param）组件。
		- `http://www.joes-hardware.com/hammers; sale=false/index.html; graphics=true`这就是一个例子。
- 参数
	- 碎碎念：感觉这个组件用的很少
	- 确实有这个东西，就是 **每个路径组件的每一部分**都可以有一个参数组件
	- 如果没有路径组件，那么绝大多数情况下不会有独立于路径存在的参数组件
	- 一个路径组件可以有多个参数组件，用 ; 分隔开
		- `/path;param1=value1;param2=value2`
	- 实际中确实用的很少，很多时候都被下面的那个查询组件替代了
- 查询组件
	- 老朋友了，就是用？隔开的后面的部分。没啥格式要求，就键值对key=value。除了不合法的字符需要特殊处理（中文？
- 片段组件
	- url的最后，以#隔开。有的时候一个资源的url指向一个大的文档。使用片段组件可以让他指向某个具体的章节。
	- http服务处理的依然是整个对象，并不会说你请求的是某个片段于是就只发送这个片段。片段通常由浏览器收到资源后再处理，定位你要的位置。
### 相对url

这个感觉不是很重要...

### url编码

url由于种种原因（smtp协议对字符的自动处理，早期只支持ascii字符，等等，说实话原因这里他讲的不是很明白，感觉让人看不懂，只是了解了有这几个原因。）需要对字母（A-Za-z）、数字（0-9）以及- _ . ~ 这些字符之外的字符进行编码。

url的9个组件：
Scheme，port，不会含有非法字符
Username, passwd,path,params,query,fragment中的非法字符用％加utf-8编码进行转义
Hostname对非法字符是另一套转义规则(punycode)

这个小节看的不太明白。虽然知道编码方法，但是对于为什么要编码，没搞懂，问了会gpt，感觉说的也不清晰。