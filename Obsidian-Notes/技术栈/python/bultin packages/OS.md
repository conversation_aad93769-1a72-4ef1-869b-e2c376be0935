
### path相关
- os.getcwd()
	- get current working directory
- os.path.join()
	- join paths, handle different OS 分隔符
- os.path.abspath()
	- 返回你给出的path的绝对路径。如果已经是绝对路径了就不操作，相对路径的话就和cwd拼接
- os.path.dirname() os.path.basename()
	- 返回你给出的path的目录和文件名。
	- 目录的父目录们都会带着。目录的最后没有slash
	- **dirname这里有一个坑。如果你传入一个不带目录的路径，也就是直接传入文件的话，像这样：‘xxxx’，你可能以为他会返回cwd,不，他会返回空字符串。你这样传'./xxxx'，他才会返回一个目录前缀‘.’ 感觉dirname的操作逻辑很低级，就是找分隔符，然后把最后一个分隔符前面的都给你**
	- basename就是文件名。如果你给的字符串最后是/结尾，也就是说是一个目录，那么他返回空字符串 ''
	- **像这种路径操作函数，一般都不会验证路径是否存在。就是用来让你处理路径的。**
- os.path.exits()
	- 好用，爱用。判断路径是否存在
- os.path.splitext()
	- 从拓展名切开。字面意思。判断你的传入的字符串的文件的拓展名，然后切开。前半部分会保留父目录，后面是扩展名。没有扩展名的话，扩展名返回为''
- os.path.isabs()
	- 简单，判断你这个是不是绝对路径
- os.path.panduser()
	- 把你这带着～的路径展开，～替换为/home/<USER>
	- 什么意思呢？你要想处理带着～的路径，就要展开。不然他会认为这是一个名字叫~的路径。
- os.path.normpath()
	- 这个也好说，就是把你的路径中出现很多次的多余的. .. 给你去掉
- os.path.split()
	- 路径分割，目录部分带着父目录，不带slash
- os.path.relpath(p1,p2)
	- 计算p1到p2的相对路径。
- os.path.expandvars()
	- 把带有\$xx的路径展开，\$xx替换为对应环境变量。
	- 如果没有那个环境变量，则不替换，即保持原样 \$xxx->\$xxx
	- 如果环境变量不是一个路径也会替换。
### open,read,write三大件

#### os.open()

open就是用来打开文件的函数，这都是废话了。使用open的时候离不开两个东西：文件路径，用什么模式打开。

看函数签名：

```python
(function) def open(  
path: StrOrBytesPath,  
flags: int,  
mode: int = 511,  
*,  
dir_fd: int | None = None  
) -> int
```

path：文件路径，没什么好说的
flags：打开方式，下面会详细说
mode：用的不多，用于创建文件的时候指定文件的权限，读文件会忽略
dir_fd: 用的更少了。我们使用相对路径的时候，默认从当前目录解析，但是也可以改。这个参数用于改变相对路径的解析的目录。用的太少，就不说了。

#### flags

path就不说了，相对路径或者绝对路径。flags有以下几种（复杂就不介绍了，就说说常用的）：

- `os.O_RDONLY`: 以只读方式打开文件。
- `os.O_WRONLY`: 以只写方式打开文件。
- `os.O_RDWR`: 以读写方式打开文件。
- `os.O_CREAT`: 如果文件不存在，则创建文件。
- `os.O_EXCL`: 如果文件已存在，打开文件会失败。常与 `O_CREAT` 一起使用，确保文件只在不存在时创建。
- `os.O_TRUNC`: 如果文件已存在并成功打开，且是以写入模式打开（`O_WRONLY` 或 `O_RDWR`），则文件内容会被截断为零长度。
- `os.O_APPEND`: 文件打开后，所有写操作都会追加到文件末尾。
- `os.O_NONBLOCK`: 以非阻塞模式打开文件。用于对文件进行非阻塞I/O操作，常用于处理设备、管道等。
- `os.O_SYNC`: 打开文件时，要求所有写操作立即同步到磁盘（即同步I/O）。

flags可以配合使用。类似这样：

```python
fd = open('test.txt',os.O_CREAT | os.O_WRONLY)
```

flags如何理解？他们可以粗鲁地分成两部分。一部分是指定到底是用读还是写还是又读又写打开，另一部分是指定其他buff。

- `O_RDONLY`,`O_WRONLY`,`O_RDWR` 这仨兄弟就是指定以什么方式打开。
- `O_CREAT`,`O_EXCL`,`O_TRUNC`,`O_APPEND` 表示一些附加操作。

比如：

`O_CREAT`
- 和`O_WRONLY`一起表示只写打开，并且没有就创建这个文件
- 和`O_RDWR`一起表示可读可写打开，并且没有就创建这个文件
- 和`O_RDONLY`~~一起用表示只读打开，并且没有就创建..~~不是，应该没有人这样用吧？应该没有想读一个文件，找不到就创建一个这种场景吧？

其他的也就很好理解了。

这些flags和我们常用的`with open(filename,'r') as f:` 这种用法，也是有对应关系的。


|     | readbale | writeable | 自动创建 | 写入位置 | 对应flags                          |
| --- | -------- | --------- | ---- | ---- | -------------------------------- |
| r   | 对        | 错         | 错    | -    | O_RDONLY                         |
| w   | 错        | 对         | 对    | 覆盖   | O_WRONLY \| O_CREATE \| O_TRUNC  |
| a   | 错        | 对         | 对    | 末尾   | O_WRONLY \| O_CREATE \| O_APPEND |

