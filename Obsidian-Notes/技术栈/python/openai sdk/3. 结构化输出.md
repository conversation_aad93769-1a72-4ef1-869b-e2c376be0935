
结构化输出,表面上很简单,但是就openai的sdk而言,掺了很多细节进去,一不小心就会报错.

---

在openai的api ref中[^1],在`client.chat.completions.create`的参数中设定`output_format`参数即可设置为结构化输出.

而`output_format`的设置又有多种选择,我们将其分为x类,包括: 模糊设定为`json_object`, 显式制定json_schema,以及使用pydantic设定.

### 模糊设定

这个很简单,只需要:

```python
from openai import OpenAI
client = OpenAI()

completion = client.chat.completions.create(
  model="gpt-4.1",
  messages=[
    {"role": "developer", "content": "You are a helpful assistant.你的输出应该符合json格式,像这样:{\"content\":\"hello\"}"},
    {"role": "user", "content": "Hello!"}
  ],
  output_format={ "type": "json_object" }
)

print(completion.choices[0].message)

```

单纯把output_format设定为`{ "type": "json_object" }`即可,这样llm就知道,应该输出一个json对象.但是json对象具体长什么样,还是需要在prompt中显示指定.

### 显式指定json_schema

如果你不知道json_schema是什么,参考[理解 JSON Schema \| JSON Schema 官方文档中文版](https://json-schema.xiniushu.com/)

要显式指定json_schema,只需要这样设置`output_format = { "type": "json_schema", "json_schema": {...} }` 但是这里有点坑

我们知道,json_schema本身就是一个json.但是在openai的sdk中,这里的json schema却是这样的:

0. 最大的区别是,他这里的json_schema要求是一个python dict,而不是一个string
1. 其次,对于``{ "type": "json_schema", "json_schema": {...} }``的"json_schema"的value,他的格式应该符合:`{"name":"<name your schema>","schema":<your json schema>}`
2. 其次,他强制要求你的schema中所有的object必须要有: "additional_properties"和"required"两个key.

一个有效的写法可以是:

```python
json_schema = {
    "name": "chat_output",
    "schema": {
        "type": "object",
        "properties": {
            "thought_before_content": {"type": "string"},
            "content": {"type": "string"},
            "user_portrait": {"type": "string"},
            "my_whole_plan": {"type": "string"},
            "system_timer": {"type": "string"},
        },
        "additionalProperties": False,
        "required": [
            "thought_before_content",
            "content",
            "user_portrait",
            "my_whole_plan",
            "system_timer",
        ],
    },
}

response = client.chat.completions.create(
model="gpt-4o-mini",
messages=[{
"role": "user",
"content": "Please provide a list of 5 fruits with their colors in JSON format.",
}],
response_format={"type": "json_schema", "json_schema": json_schema},
)
```

**后记 (踩坑记**

- 2025-05-27 哎呀妈的, 用ai帮我生成了一下json schema结果不返回结果了, shit 看了半天发现是schema写成json_schema了. 怎么也没有报错信息, 只有超时 !


[^1]: https://platform.openai.com/docs/api-reference/chat/create
