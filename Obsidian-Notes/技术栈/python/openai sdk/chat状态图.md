---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'



# 1

# concurrnet.futures多线程常见用法

```python
import concurrent.futures
import time

def task(n):
    time.sleep(n)
    return n
```

```python
executor = concurrent.futures.ThreadPoolExecutor(max_workers=5)
```

### 1. map方式调用

```python
results = executor.map(task,[i for i in range(5)])
for i in results:print(i)
```

    0
    1
    2
    3
    4

map并不会阻塞线程，因为map方法会生成一个Iterator,通过迭代Iterator我们可以得到各个线程的结果,而迭代Iterator的时候会阻塞。

### 2. submit方式调用

#### 2.1 submit配合future.result()

```python
futures = [executor.submit(task,i) for i in range(5)]
for future in futures:print(future.result())

```

    0
    1
    2
    3
    4

这样看着没什么问题，实际上不太推荐。因为这种迭代futures的方式会一个个迭代 `future.result()`,挨个等待future的结果,这个时候会阻塞。实际实战中，很多时候列表中任务并不是按照顺序完成，我们希望等到有任意一个任务完成就做下一步动作，类似select操作。这种时候我们就要使用 `concurrent.futures.wait()`方法或者 `concurrent.futures.as_completed()`。

#### 2.2 使用wait等待futures

```python
futures = [executor.submit(task, i) for i in range(5,0,-1)]


while futures:
    done,_= concurrent.futures.wait(futures,return_when=concurrent.futures.FIRST_COMPLETED)

    for i in done:
        print(i.result())
        futures.remove(i)
```

    1
    2
    3
    4
    5

Oh man, this is what I want! 太优雅了！这样的话，我们就可以只要有一个任务完成了，就做一些事情。实际上，我们还可以加上timeout参数，让他即使没有任务完成，也要每隔一段时间做一些事情。

#### 2.3 as_completed()

as_completed()会返回一个迭代器，按照完成的顺序返回值。这个也很优雅！并且也可以设置timeout。

```python
futures = [executor.submit(task,i) for i in range(5,0,-1)]
future_iterator = concurrent.futures.as_completed(futures)

print(f'wait, i am handling...')
for i in future_iterator:
    print(i.result())
    print(f'wait, i am handling...')
print('finished!')
```

    wait, i am handling...
    1
    wait, i am handling...
    2
    wait, i am handling...
    3
    wait, i am handling...
    4
    wait, i am handling...
    5
    wait, i am handling...
    finished!

### 总结一下

* [ ] 这样看来，大多数时候，我们都应该用 `executor.submit()`组织一个futures列表，然后，通过wait或者as_completed等待任务完成。map是独一档的存在。使用map省略了futures的细节，直接给你返回results的列表生成器。并且他是按照列表顺序返回，并不是按照完成顺序。

# Excalidraw Data

## Text Elements
finish_reason: ^mEhh2ifk

stop ^QEEmrCkT

tool_calls ^oweUZXAu

对话结束，输出 ^9YllHP95

获得chat completetions ^3i0u77LN

调用工具 ^RwBFN9F9

input ^mGF5aTgX

非流式： ^wnSMYLeo

流式： ^U3guUmOi

获得chat completetions ^InY04LYz

finish_reason: ^0bd76oYO

stop ^4ww6b1og

tool_calls ^WUrYQi5x

对话结束，输出 ^3Zc5qqFV

调用工具 ^shglycea

input ^ghAmwjWU

s1 组装content ^oP7KHf2q

s0 开始 ^WRq56p7V

chunk.choices[0].delta.content ^CCAi6sVf

chunk.choices[0].finish_reason=='stop' ^dIradOLC

chunk.choices[0].delta.tool_calls ^UY85yKFb

s2 根据index更新工具内容 ^P8YGRqLl

chunk.choices[0].delta.tool_calls ^e7WNd4Cb

chunk.choices[0].finish_reason == 'tool_calls' ^ZfNvmw0U

s3 更改finish_reason ^ImlDAp6z

chunk.choices == [] ^TelsV6eY

chunk.choices[0].delta.tool_calls ^9GK6U4Ia

s4 结束 ^uNS4NNps

chunk.choices[0].delta.content ^n4IV5Lzh

message,session ^dwgi79f1

chat_engine ^JjBMBAKb

tools ^xYpDxLIq

message,session ^dCr4KwWh

message,session ^2Of5mkml

4.17新增
chunk.choices[0].content==None ^T73oUQUv

%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQB2bQAWGjoghH0EDihmbgBtcDBQMBKIEm4IAAYAM2wcgEEACQBNHgA1ISEASQBWAH1cAE4YAGYKbFSSyFhECsC662VgydLM

bmceyp7+Uph1gEZ9yu0ANgAOeO3CyAoSdW59k8HtK6mpBEJlaW4z19LF5ZoSo7ARQUhsADWCAAwmx8GxSBUAMT7BCo1ErSCaXDYCHKcFCDjEWHwxESMHWZhwXCBbKYiDVQj4fAAZVggPQgg89OYYMhCAA6ndJNw+NcILzwVC2TAORK4eUQRACV8OOFcmh9kq2NTsGo9prKsDxfjhHAusQNag8gBdJXVciZC3cDhCZlKwhErAVXCVekEolq5hW4pv

GbiVAja4AXyVYQQxG4SR4PUGPDOg0eSsYLHYXE1PEG2aYrE4ADlOGIHiM00kLpUeMa3oRmAARdJQBPcaoEMJKzTCIkAUWCmWyVoKUyK11K5QkAtwAHkAGIAKz6jVXi4AUsQzmcAIqLzQCvrLjgQzGlcM+0jgqgz2Mz0Nh+AR6BYKBXyBz9AjQiVEI8TxAAMmWECPiC05Tj+iYSAAjj0ZYcM0+A9Jg8RQBQAAKkhsIuADikjxM4gxCN+0Bvre94QV

O0bXHa4pCHAxC4J2cGoPs8Q1ocIxPNxPQjEqRAXi6br4MJbC4l2aA9vgYSFE+JQvmUHEQP+gHAWB9I3uSn70msaDOHWJwvDWgwjDwKaVIM8QnD0JxKgaqCkSM2j7Bcfw3MK1ZxD08SDGcJxJJs+yDIJ+xim8kgfF8X5oJsSoAhGTalJK/IkgiyLomiSD9jieIBsScJZeS5AcFSNJZF+9pMqy7LvlyiriulUJCsQ9xoFFaV8tKDUVE1iZKiqkhBla

WrijqOL6g8RpKqazEWhOjFvA6uBOhxrruuKnrEN6Ei+m0/qDsQY1idtbzxhxHm/I25xJMWuacD8QnijmpYcBWHBVmgZxWU8hZ1h6bYdjJqByX24oDoSxAjhk1XLUqzGsexDzcZm+wjEkN0jBNbwiZeaBbRJ4rwtJHEQwgSqdpg8XoIA7cGAOn62CSGxAA630GD4CCdlAea5MNlAACr6RITMs2xqB6Po3O8/z9LVJwUAsoQRgRo29pK8u61Ms5Xkf

rT9REMo+boGI2RMPSOZ8+4RufKbED6CQxDLEqejZLgnpMM6Eg1HUzBNK0HTdP0QyjOM9IIp8noECLtMVOLrNQFLXPBHLnAC+KuBCFAbAAErhKrEZgkIVOk17jSxd8mraCmik7Cpv4QAuK7rpuO57oex6nuel7U1RB13mwVBKoZLkjLjLxOQcRzaJU+w9BmS+PCM8TpkqtwdSKmqDEkU/ijFnzV6gZz7Kc8SPPP89nHxPBr0lHBLClca9TCJVkugK

K5Ri+W4gtRKZQ/tAcqlVaQ1XFIyZkMo5SDR5K/dqnVeAvylAgaBjUFRDXFCNM6mptS6hmoaVKkAFrmktPkFapQ1obXOiTZsXox4QFwPsY6MMcGoBfNMAevAYxxgQGDE4JxGwpkXkQhgJY8wPA1m9cR5ZKzq3nnWIKIwgrA3bGnMGlN+wnThmOHI5CkYsTYmDLiSQ6zphvhmLM5dRJE3EpJcm3ZexlzeHANgno9FoEnFMLxUwiElEqDOChJQfElDC

nvLyJRnCn3PpfI4Hlb5r0CQxYSoQoCwn0E7GQCZsJuLpLYi6PUaRQAAEK7U9MobgHCMCjmqj7dAfsGgtHaJ0XoAxhhjAmFBBkUkhBWmcMcLYZxsYWUGCcEYlQJ49EEkkRyM4IDKFwHAJMXTGSYGybkumxMeHiiyMQUpRJyk0JfkU+oQ8KAxVwJtOxOyiSnPvBctSNIaJKiCAOCgGinH10KI3NS+ghySEkDwQg1Q+7il0ugGm4C3hj2cIWWZbxnKw

rsu5TyG8fKaixi8AKQUQrz3CrjbqkBD5xVFPrZK3BRGtTfqSbK388pQwKv/YqNKyqUmpGAhWdU0EDQwXAlBCDt5IJaq/blEhYHDWEKqdUDw8HTVgLNURJClr6IgY6BAdTiYenoT6EYLDAzSvybQtKfCOLYv2MmCyhKxFPVNjweFpR3p5i+j9TilQZnKMuEkURLY1E8w+fJZxpRobDhqeOFVbxkZGOuujXijYkiZniMJT0hNUCatJlJKEFMnHU1Fu

gRkHAWySD6IEUInBkD+mFrmhknpC3FpCIIDg5bNbZBVmrUUojFbZG1k7fAesc2G2Ng7YI1QoUOqYDbAgdsTYVFzkst2StPZqlIBq65bwo7+FjlW/NtaS0NqbVnHO+dC5trQCXQNkARIIErkfOmZ864lCUtBWcakDxDiHPoUg0IIRCx0lwg2o7IAwrvlaxFYVTIeUuGireSY96ZkGOFHgNYUzxFPimJUxLj5oazo/DklLX6ANpTlek2I/5FQI6yiq

7Lqqcqgf1MVvLkH8gFaKRjfVZToO5BKvwo0DWcVlXqeVhD5oElIYjVV611VXIKT+bVB0Th6tOrxtNl0TXcDGfuN169pE2u4Mox6H1nURkOHano8agpnFUaDLNAatEwx0QjcNpRI2o01DGzG/1ENWoJkc9NDjZLZrBVW3kOoK0UDjnTCUs6FZK1bfI5tUBu2624PrSFU6h0IBHVbcd5hJ2DpnSF+dHsvbLqk0ayA66Y74HCwNKLSVD0F1YCe1AZ6k

1qmvSSmu96wCPp+RUIw1RiD0B1CyLoedmDQiHBwFDB4QLLnsvgX9sxB7PPFDC3G4Tp5GWRRB/Wm9EFhTxqUDDdMNvYafhS1j1LSqfxyj/BlpGTrkYhSAqjdJaq0fYzyzjwr+XoqFZdEVdHOQMawZKnjwYZWTXwYJ11iqRPKs8UEhkaqV3SbKLJ9AuB4gKbYVU8FPBtkqbBo2AKgwJkOX0xIhKVrHWyO+kZgKKHDime9SDdR1nIZvGDbDUNHjrRI+

c8YtzllGyBUOxe5NPn8YZv9Zz0orj3EThnCEsAfjVeBKgirg7UEwDGReEkqYSP8CpPSZk9iOT3FS8KaQEpZTH5W8gLs/Zjh7eGuOTbu5w8HkO4wLcs53vlvD3pK84esuEBfOUjtNSoeACqAAtAAGvUci/clsQv0qPdYd8z761A5UM4KLIPij24K3eLxUxpkisFYKQyHoHyrnTLDbxyVAku09iAX8iO/0Ko99+M6XtVTexArlQP5TfYB796DXVLui

uB+P0o2DePi4gFNATzkjhw7NAj/n9oUela1XtBhQwcdKdXcasGaZ0yXyGZT56aBsa38+nIh4FxLITOxlan1VnHE2ahto3nYmEahiLmnEwudqNYXqrWKaympQZMmaP+cu0wVauccIfQ7g8koW1W5IbAqB6Bmcq0MWRc7a8WiWvayW/aUAaWFQw6AG1qE6+AVB2Bc64o7sUQxWqOZWK+pA0cHAm68c2BuBBAGBdWucDWRBp6pApcrWV6DeDwtcPQEe

T6sEFQbAQsJgxAXQi4Rg8EzQ+wpAygUAXQzg9QmApA9A+gi2748wUQOG9K0KBwd8+8CKWehYheu2f2C8RY9eN6amiaZ2uGbefeEgneuUxGjKZGQRz2bKg+tBkC9Un29G8+oIk+iCVqVKs+Y+zUbwi+EOuCUOcq6+c0Jo8OZCiOu+EmHBB++0mO0IJ+uR7Ccy4KUYUwj6AgqmaAwEdq+eN+2mH0PwoitOT+9O3AtkhwtYWmzYbOfqHO56EA3O9mYa

niz4M4nCae/6FETcgwqE+AjQ2E4UtELRySTEwBQuPEmMuMu8F8UBPucBYeihvWEgWxzIux+xqe74kKBkM8iQ8G8GMyqYyYyiExuwWe+ebhUG+2NY2gPxGYuM+4SQdkFO3hHWvAZKthF2P2GUkRHet2dhQa4RveLKURlGMRNG8RMCIOE+TGf2aRgOCRc+WRC+YObCy+q+BCsOwmW+pRO+4m1CbuO0GOjCrYdRVoMBbRYMp8fxJmNOMips9kj+hmoo

uM3ElQwUdekxvqIBmif+dmABjmkAgu0aZxE8KYZw881xfJ0ufm4MAWYYVagAn9qAC70YAMt+gA+uaAAw/4AMnxgAX4qYH2nOnunenRYtriG8Adpaw6xkEJQUGMFmzVSWyPT0ExmOzOyuwsELrsHR5qHMAaFaE6F6EGFGEmFmEWHajcEbpVZ+mumek+kiFHqNbFySGzGXrtbHx3oKEPoNxR4VBlj4CaBCz6CLg5IACyCexAPQjQLImY2ArYgw2EhA

lhcwCACwaJmeRkRwFm4oeeZ85wRebwJeDw5wThR2shCUBeqJ52reGJUI7eIRd2XO+JMM7eFIxJHK72ZJHGDJyRVJU+/2PUKCGR4qoO3GzJ/GbJG+HJi0XJto5RvJqaZ+Mmh+PoBEwplSjRXCzRHZLU7RqAAUDkou0pOmaA5w8pz+aAIwpmMyiGVi6p3+/mv+XO/+8Mix3JQBKMpxGMWMB2gi5pcFaOtxMx9xXZEgecFAxSy4ZYgwy4gwC5ek8cq5

Lkpmm2LkppYJxef2AUUJPxyioydYSQq8+sx2pKD8F5qAeGKCN5OJYRD2j5WJz5oC1Gb5AFFJf535qRM+o+gF2RTJS+oFMO4FxRnJgBlCe+Puu01RjCjQKFFp5+108J8Q+e2KAxMpL+JFwxv0wUVkyGn+UxmpNpQajFuiQV+pJxhpHFWMpp1FsBku0VF6MuMxFBFQgAwDGAAUroAKe6gA7oq+n8HoAtUdVBnKwhlSIEFdoRl9qBYDr2zUEZa0HWw5

YMF5ZMH0isGLrez76TRlmVZYE9VtWdW1liFNYtbWIyE+GdbtndadnNhqQsiYAcD0DNB9D4QYSYCSDVDQhGCrg7GEDLiYKvhrFPJB7yXOAeRHmQCIqNinCopqU/k1gbnRQnknzPDnkcimR3wqmjIpgCK2Q9CYyBGEnYl0pWU942V412WvaxEj50mZE/UuVtTUnuWU2eWMnAU+X5Fr4KoQWiZ6nI4VFrV0KIUHQJ5RUNFTiURp4E4tG8KnFhTxXY32

qQCDFJi2SpUuo8BxXpg8CRSWbs4IGzHzG6llHHFsWlW8SY2q2vT4zVW8WcH8U61KgK7MUhIq5q4BJThBJgAq5A1k464Zj66u2a4rHOAw067jK+2G5HH4wm4GBm7rKW41WRZFLO6HJx1O524VJx28gnL+4hC82lC7Ke7nLZ3UQA07K9mh4CWYWR6XUVCITISoToSYQ4R4SETESkQp5gp/r/UjyrbrAnAXwg0QCIoCIQ27mlD7magnA57obw054/Gz

1z27zm3/BomXmUnXlYm3m4lYgPkAK2UD6vnD4fbklJGRYpGCo0n/keXOWQA5HjS+WFGb6QVFXc2wWino782Y7NBC145cLi0V0xWiga2Yzv6w1jqEWoBK29FOqkWurup3QqI7Q5Vh62YhpMV87QWG1RpoymJ/T7jaWVUS42JW32LwF0WIEQD21oPK4rHO0a5UMwSPCT0B0z3z3z16UG4lBG6R0ZJqDm4bI+4Z026J2u5EM3J7Kp2oUi3pC6J1JVC1

CNJBwtKhztIRwrI9JWgDJWR/TkXwlk4JVjLi7EKLLkFzKrIx15JW2E7W6UFZ2XI+5502OPJnLB6l3vLl3nXfJCXoD6AETLg9C4BCzKCC1vEzoZ7d1GQnA2RKVA0NiqV7l/Z1juQV7JiCJPARRN7HknUonGUBFXlXZAIb2E1MpPl70OUH3vlfafkn2uVn301H2VM32Q5rrQ730c3b7oOrQhVx1hVH5x5C2v1XQ/CNhYznAROP4/DK0RjKIT2Lxpha

3TG23akoOFVc0GloxGnxW15+EW2EOv022kOzEfESCehwA5xdURbHOnPxaxbEEQLhk9pjW2kTXToSA0FZY25zVJm1ZplFZLqVHrU8F8HnMcAnO0HZyiHHoNlSFHUtm3ryGCVV0SAUAcAshDnNAgQIBsAyXp5yVhMuQBSiKgYOSxOj3qXPCz3aU156Uj1Erw3pOQAt6mW43Xb41d73ZE070k0lND6rQU11PU1fm00/nn38hOXH0NN5FNMFHs0BWP1c

1UKSahUCm4C4B9PwUSjYVpNBQpgEV9GuYTO6ZHACT55qmziIP1WLM86oNP2rOubrPjIhQmsEPQFqt7PWn0XXhVqAB66YAIK2gA8PqABY/2cxUD6wG/1dc11GGSNfc8Y485QQtegK8wmR8/G9AAVt82wb8zneVhtbwRWd1RACG4G3tRC9wIdds8dciW2fC8+hUDHiMMoEIDHgOfOcE7JbQTCsBMvs5LjKZASu4T+amJpfBqZlZHxJZKfFPZk3S4ws

vYy7kxZQTd3kU7vdEfvTy4fR+fy1U4K25bk6K/U95fUSyc09K28EqlBUjvK383zeFbgJoKq2jgMx0RfFxB5ENaA7q6gE8Pq/fvENg+FIiTRdrfs8g5a8swbaxZg7axxX+/uKmDxbs3VQs7GxUH60W1gpWvm2h2G4NZGwlqNTGx608+lplkm7bCm1828MtRmT7hVrm1tRANh8W/WaW42dITC3IV1j1p4xAGcNCPUPsCyGwM4HHlAPEEOdhCyI0ARG

qCMGWM4PgdeH+tYeSoDfGj0HPL8K/mvNxH+2MkpXfM8MmNfMohYlxIhuCYKo8IjZO8ib8Nk8/PO+vZZUuxEZy6u6U+u+U4kZU1SsxtPnu5fWK4e7fazWBUUWeyUU/Ve1m2/be50kBfqvUd/Wnhhe40TtdKjaaUaD0W8ArV1KvD+5xOMjZIJCInM7le61iAVQ5ksVOBwsoRIJgMwF0F0KQA5NgCBMoM0NCJUHHjALgB9fUEYL0zrqLe+J3QcX/ZXS

LeCusWN03F0ChG6iBM0EYFN+l1MA16pH1l0DHsuP8kkPgPEH0AKFACcJoOEBCJoD0PgKuBRHN5N4+OHU5iVWszB6Yrighy60h/s9W41+gEt80Ct2t1i/N7i8ZMioIiIqmChuRZrZuaKKMlCY2PErg6fGvIvd5D+e5lioFMFKFPigj3DZk4lP4Y56vXk4RqEa5wScy6TSSY5UF75/AnTYFwzVfcqCF406UKyX5RF6UOe9F50yIze0flu0VGwv09hR

BuMocAFHLdap+9rpA3Ti6nEqvL3XfIr1/sB262Q3rVays299B7xH+6YmT9s863xb9/rwc1WonOzJzDLOonzBnEG2LMzEnCnC736m7xVDh01u+5AJ2vh9G1GeNXG5NRIObJ2IiGR7ltH14ymZvRANR5mxUHxwJ0JyJ2JxJ1JzJwgHJwp5HDm4CwnF75LNLLLDzPLMxyGWW1VW1tPXC3/Q8egM161+1ycJ19171/14NwwSN2D5N4DZZDnlp7p3fKvO

6gZypTZFxEMmThraTo6xAGPZxGXgZfDdEr3bEiZ4IvfOT+iZTwu6y/edZRy/T1y+TRuxU1u352z5T/uxL9zxK7zye0JjK5zRB8FTzYq+/UYSp9JevGFLurEsZilTU5VN1HpUV75dIwPQHVlAzSq8BgImYeyLvAq5IMLWCxNBgLhN6gFsG5iPBor28xx1XWWpFxBsiVxTgnaOuF2obn9r0Mt+OuKJNuQvgRM4kN8Q/r/Q4YvcL0XDaOsQAtzmN+mU

QQRuIzQBVIpGtSNSFn0E7CdRO4nSTtJ1k7ydFOlCNRusAGSLxhkNYMZBMnIqmYtmZ7IxvfhWSEA1kwgvhoaggG+4xGByYRmIMzr3JC6ydP3K4NsaB4u6bwEPK4x1r/cduvsTQMQDshsBmgi4MHh8UBrphTI+eReFP106z9EemoTGCjxM7o9zOWPdfh4UxT+R8euKMKGk1s6YYkaFPGmlT2CIuc2Wy7dzi+U86UJeWm7PlNUxYzs8+WLCKVEezvqn

tBeUXOViL1frdMfQ1QB9pwSfbgNd44UTMPuDGYYo1+gxBUhigJ56VjWWA81gxR1JG9f+xVI2u9zN6fdLeTfa3tbVt6UCiOEWbdMwCLS7oy0HvPNDWmuF1pS0jaQPnFluZRsksEfWNkmUTbaZEyFHNNlR3TIZ846dHcvhICuE3D60dw+vgdTY7QsW+XHC6jWwkBJAKAFAC7vsDYAVJW22LdtlnkESacEhq8afnp2yHr4LI6QtHmZ0x6WcYMUJMKAh

iQz+RUM2/KdmUJP4VCz+NPGoW52v4eduWjQu/j5wf6s8hWtTZoVxi6GhdJWbNL/pF0CoDD/+XTJVriMS6KZ6i0vYxPnixo9s5hnEGZEVwXiYw142MSAggw1LYDNhSzWrixVe57DTepow4aIjIGi9YCZwvKkgXzbBZmC2RTDhFl9FvCbmw1MPl8NQApZPwvw6am8wBFJ9U2fo0oOn1Wq0cy+ebQMZR3+D1YS2EhKFuWw46nVAhTcfYH0EGBDgQIq4

daMUlKSEACIZYZcGcBZAUBZaI/JxmPzdQvBJ+pIpIfpxSGRg0haYDIbSIs5Q19smYEoSdn7oMszKmJPGgU1p7E0BR9QoUSHyaH38WhO7Gpu0KlFAUZRPPcrJ/3ZLf82ml7QYWq2GEHQRQXGJLiGDQpi07BEwu6MQI/wGjL4RXL1AvBQxBRWcVojYflS2Hgd7RuwqDqAXWYujvuNvK0ucMgAUMaB3iahvQNoa0CViKvGCHrjOp8DGBpMQQTwzMabI

1WAjW3I4LTrujHcRIIRiROcEe4HGdjDwV7jcGY42xJdN5HcTb48cBQMeUgM0APCEB0IUQ0JvYSMh2oJ+JInTjP17HOEuo0SQcTSOURZD6RaALfhXg1p2oZkulNfoZS6iciV63I5zouz5F08gEDPNdsKO870kxRp9Noc/2Z6v9ma3QsLvzwfo/8gJz9BVqqMAGewxhkta6JjU7ZWQkqYDc1F4Ty4yklhnEJ4BMgzDwYdeZrZDv+NtEO18Bjo0CR9w

t6ujLaiHKCV6PWICF8AaBIQhoOVABj8sghZkIVND7htQyJBAjt8IuHRjSO/w5NvGMzGQBkxJWVMQC3TElS8peBekGCzrIN8ER+YpEWdW44It0A9ARcAnkIDbgKAzQIctgH0B5wDwRANgEOR4CLgE0YPFTiuQh7y9OxokskckMknhTjgMkixMOOyEb9PCE43wg5y5ECtKhN2fSRf3ZbMolx9lFcdWhFHmSNxgoJ/hUJf6dDwcsoj/lKwVF9ClROw1

yde1nBKt7u14zUbeNm7oUHxMvSoChgcjYxTSr42YaryGLq8jgdqTRkCR/CxSQOOA/WtaGWIwRxuITHFrTKbgjA482AHoPBHgjLgjoz3LCZBxAJcQwJaUiCacKykBoixakZmazPZmcz+JDM1YAcFBLxC/oEyDWhPULBdsX8Z8c6aZzkl0jRxVnSEtCQsg3R4S9kRXppKybH8dJj0nkXeTxKX93pRkm/qSSBmXZ/Ov5R6S7N3Egz9xK+Q8f5UVGyto

ZMXAAbe1BReVWEp+R9hqyAYNgwMr47iEVxUl6V4MWMH8bRTt6gdcB1rAgfzNSlfdrEJw4htaIuEVBHSVZQMoLDCyVkAyNZD4QNSD54dSCDzOqSm1j7xlGp5HeMU7GIAuxU+bUmRpNOmmzT5pi05aatPWmbSuIpfTqQxzLk1y+p2YljrmKbIVwRpYslQthGwBx4c4ecBZJoGBQwAhw9AHoHADOAx5JAewPEcqCXI2FzsgNQKMSO05HSJJwJKSQXmM

6ySMeI4uJjj0PLsjkSi8KcbOxnFr05x1Q16bUI+lk1nZNkv6W7OFZsYOh0o72e/wPHgyjxAc5ye0z/4v1zxSrBbIjNxx3jJmaM4xBmCmRvtZm+MtGHANCnQNHgpiVHpZDX6695mFMm0WBztHYLgJfM4XOb3zlW8bino0WWxPGkShJASwGAGIBVZXzohEPUxI/MSHiSKRumGyNSIuk6zv5JLH8hpXJYZhKW+lW6VpPulWzt2T0llryIgX8jHZgo2/

mZKppwKAZHs2BcgpAoOSWmx4i9jBTcmkS4uR+EshqKl4ESZe3EPiPBgbAgN5ayVAsLQptRhTMYE9VeGvFGaWj050EuYjV0SkGJkpucg4YLILlCKRZZDQ5ttT6qVyGOvVXanXMqnB9uknwyMhGOjIps/hIU95p3OeYQogRSYkESmLBFpiKlO1BeeCyXnNYhpTfCtq2Vb6bd2+EAfAG0DjwIBnA1QAANJJAQIFAZZcuE0BJAywQ5UgDknwBdBWxK2Q

ScpQOlPyexE8JSmEuSCo8NFX8q6X9iDpIlj4PtbSV+1rjxUng0PXydjWyFUobZqfEjG9OKa2KYFHPY+o/wlHbj1xrilmnKPC5OSTx3i2GQhVvZcBCFoA4haKFIXXQPIAic1P7I/ZU4v2WwROQohQxGhieprX8XFOq4ASuFSUkCbkudH5LBF5A4RWQ1gn5A6GU4Ghn7V5VTBPaauN5UhPgloTnlMEEOhhLAA2h+BsynCVkmsGx1fFhEiibRIcEu5K

JBE8QdY08Gxd7G+qouj4NzouNWJ0ynjkYD24HdJAR3E7mdwu5Xcbud3Y5cXVOXOACV5ypRXp2uWIZblQ4zRY8t/mMMSelbU4EaAjWRqI1VkEwfS2AVMt8m4Cu2SCpXbLi7FnsynvAslGwqvZbihFY5NaZeKeSPioYUq0xaYrku2KrqLitmiBQ7IbqN1AaMsjBTiVavdWJFDJx6UHIMU2lewvimcKslGDXhUQPR7oChZRcv8TBOoE8rkJMEflTzPF

VTgGGINSJNuSjXrqGwd8dhrKpSS8hTcuE5VaIJ1UJ1JBvilOsRIka+DecMjeQTnyUH59VBRfdQVeG6TYBekFKOeCTO0b4sEq3EO+F0gWRLJapjQqwSIPwnMgHxuq/OgHjPV0SC6XgxiSctNUsS3GY01EegC+D1B9AFAVcBxJlkEijIFeRRd2PElz9vidy7WQ8oUmoAEmnhXeMTNSZ9sjFFs5vPGqc5gKXpyayBTYrTXgqkFuTLNTCtFHAy81YM+U

RgshmByXJwc9ybe0THX0ToQSqOcYnHa2QJ4oyV8fgyV7ID1eQUNeKaUQw9q0l2Uw3oBO4UQAbWKUvJQIuOGFKSGGcyPhUAua0FyAVc/Ns5uDERtqp4fRpZH3qkzVss7Sh2C1LT49L2pfSmeVWg81wjIWK85vpkyraiL0NEobcM4GWVCA2A9QJILgCHDbhcAmAIWGyFMD6BcAkVK+TtLvkQ8/od6PiPps2BL9F4SlLGtoF+DY0zaq8VeFpuumWR3I

G+BNHpTGJ2pmNKS1jSZRAXmL5xBkxcTxs+npqXFAmpxWYozVM09xqC32egqJXEJ+hQcs8WjgvGY54IX9KtZGBrXj1zUXRVOQaPChFd6N6A5MJErKDkyHNHCrOTOq24rE6ZbbDYtHmwjxBlljQaoDwEO2QQaZItJuMoBZBhQ48ygTABWJwLMAE8uAAiJUCgDYQDwy4ADEpz+pOMQd9XD7U3AoCYBvqQsECIMHqDEBWwecGAHYA2UUB4IkgNoJ/TG6

PccddEKCNtybgQgE8BEIUHACRbLKywB4ZwI0GWUwBtwzQE4LgFPgPcO6rOiWqDrEUjA4ATY5ZULAhDwQug8EBADwAoB9Bik2AZQMQDjwx56AMu7HTRG5kcNslzKv9umFGS4wTR2Qt0ZlPs2Ux15EgNgL9v+2A7DtsigSXLK2xUjX2YyeEu1vgYnTAoxwVrZFD0odaOB1Gy4FCVJxuoTEppGZC2ppak9RE04hNdT1tlb17ZoK3jUzwhUs9LJAXaya

Xtsmra+M7i3oVtqhnSbdtnBfbYwnj6BLI54wmXr3VGTzw/2AUz9txXxnxKJkIezKmnL17pLTNjK63cOrt0IlOKXmDKT9yKX28fR+wVAIABG/QAKMRHMVgg0OvrFSxUG+nffvq+kVTcO3m8MZGOI4VA257e1pXGI6XJke5qZYET816WchUt6WzLdlty35bCtOQcwqVunnlkGOzAE/dvrP2gtF5g0vMeMoLGcQplaGgHgwEGBlgWu9bLoMwEXAsg48

mgECARDaDwQjAiyHgNtJvmqdcWDkNyBvjtT2Qb4FCxXs5EChuRRkWQ9MIvEij9t9smwOeLpV+DQ9AS/kZjXwdR6Lw/oE9XeI8AEQmK52p/PSefy43WL+8YKkvfxszWLb0i828OTXuPYbaBeDeqTeZpk2+LW9bEI7SjNS6nbXUWMUzIJA03UL0qsSgzPQvu1bAvUVCoDmwue39rXtdXd7YzPFnK6NlaujXVrp1166DdRuk3Wbom5y7puShZLUYDOB

sBiASu/YJIGKQ8BlwbAIcLgATwJ40C+gZZaMOZ2y6LdbOhXVjveL+6xFQgMsCyB2Vlg5AG3eiAup4VC57toxOJHZHHW+ZXdnyJLagYaNNGywLRwqXNzkWnKImcQWvKZl4ghRVaIGEYsjzwrY0/2jwOHkGsQTxVy8Kc+yIhipb/zShch8bYCsKYqGKMs2vjTuM0PQrK9GhlbSgtr35qPFmC5FcWtRV+KfQUAI6BWpFLBKdRpiDGdgwWHRLDRA+nTU

ZmEPwZVaLCp7VPsyV4DZ9XR+fQIgET/qClHK1fQ1TFRJBUArpe4RKAJNEmrml+uuU3MI7eio+z++/bGKanP7u5vcpamFoHnoHMDygbA7gfwOEHiDpBuAOQdLKRafRpJl0kMoGnwj4DBDCZbC2REeMxFCABPCyHoDQhmxecNgAKDaAbL9gxSYgKeGhDrSKDy5Sre6sODzxa49kc3sFHNS4MlK5pgvOmBCiZgJk5xQsNRuR6aMx2SpBKlanNmrwXgk

UOyE8HMyHAJ2ls+Q7pI41KGC9KauoTcfUN3GKhgmx40mYU12TQZaC8TZtvmTbam9Kosw0q1N0AnL1NR3TDYa9QphuDhh7TXfnCngm4l9C9WrMd0rrC6VGShlQ7WqOoGIdUOmHXDrYAI6kdKOtHRjriPGq2j7Oj7VMbqPJaBQecRCCcDgDxAuZVRvHUEYqCVB6AzAbcI0EGAgQkgq4LoK2DLCKxnAbQIwAeGwj1BwI5R83UHkt07qh1aJjMBfEYP2

dsTviigUMYtViL5zi55c/8fbprFpjAelyOuWeBWQF4NYFDOFFuj2nDg4STw3+0QHwltKCeuIJjGHZ8R3MbI5jdOxz3sbmWk2qxYZNUPF6ymy2x6SmcBk6HnjomrM4isLXC8CzpajyfQCAvhybx/DGXsaQJ7lcnDMDROedqOCmcETva3w/SoSkonnz0abowiQbX6xndK+wY1VxymchKgqAQAAD6gAac1iTzATS7pc81VTKTNU3zT8NblxkH9oDJ/Q

7GZNv7ulH+8LegCVMqm1TPQDU1qZ1N6mDTRp4U2AaCyGW9LMW1jtKdmWryEtyBlET2ch2DBodsO1cPDsR3I7Ud6O2gizqQ2AZ1gPxbQKpvipYwKWKi1IVZESbOnHgNBsZFamum91kgZONPQiVMQh7mNol8NdjVNJ2Q/2Iyd5eccUOWLlDZF649AsTM5r7ju7VMyNfovwqxNTFzxSxdwV7alWJq9M9xakHHbeBrRdVjqMEQhRNgpM2s7aisjGjjW2

NByIFDbN9qpLA6mS7zK6NmJR1XEfo5aVUtcrp1AR4JAhOoZir3r9DGq/GnzxuoGr7qR1quomStWPIGM3uvGhrAyq5VHRhVXuqjoHqwN/DXVeqtWuSNr1akVy6qfVOantTyy3U/qb6CGmhTJjLQUCAOl1gZ+kyReIhgiTzIzBEYhIMayOCR7NgkpCwaBpsEWN5dvg8iaeukGY2Bo3+jLVlpy15aCtRW4A2VrJtvr1GtVo0IZ1MTAQMZ/kQDqYKA2u

pw15qVeGjXCjYx4OJjSwXhJoSQaXB9EhDa/UNUW3HGmV6pChoCHDGgh6AQncTtJ3k7Kd1OzQLTvp2M7XVS1iAB233DJBMZf6tPY2r7GjINOWMNWkvwsRr9rpFplUiLnGQRM0wiAv0/DS34dbLImPCQ+HqXpjbc9VQzjbGe43kWEzlFui9Ra0O0knj6ZvQz0IhlGGsFp41i3go8nrcSz6N36uAN5v/0MU0tdGDWfgF/LjR2DXW1q3OuSWOz0l7OTk

vN44MLECaR6x6NxPihuVb192h9bnVfXt7Eq44KnJ712QVZ3EG+KwI0phIFEHW+DEyL3sq49ja8FMHpULCeQUMwN3XGTgSCo0AoseieDDXvsrF40LWzYJplPj2G7IH9tyKfAbCCRCwsDusMBCSDbrOGCN7hkquRvp1UbAtuZDIOyAyMdzaW0W3/oluAHitIB1RnLe0G1xCwnmBeGpJhqqkANjN2eNjXJy2nLg/95MJzZNu2D+7ZEzVUnWFpXrUGBD

kW7/vFsAGpbJWmWyLUVhUOKbhwFJmmEvycP4qpiZh5rdMjxUjQYuWyHfCGTBQeHh68DfgDNvUSjV7g4gNBoYlt67bfg81SgfUtp8NAF4XQHhHMDhA8glQG0NoD2j4AogugJWAfrqX1z3hoYqk8Bv/T+aGTQW/LPJudsQBoQ/HQgCcGYBtAyjwF2o7LKytrl38c8Q4DHt7oQEirrqBeKcGxrwkdr9kcztRrhRmRRkdWheND3wvvKNO3EaWk6eJl2o

PzCh6M31bLtXGiSldrzhkVZgswRAqfKFftmzXCa4V9kt4/Xq54RytRQJjiF6gCjZD4BV+Y0To/MyoZUTcl+fQhisj6xWFlXMhkLy5rT6uz65sHWpFdvEASdZOinVTpp3LK6dDOpndOYqMPm6I8q5S5BOeuzF+5sXcEV1Jj6uOIQ7jtxGIGYDePfH/jwJ9AeJMTO3HLMWF1458d+OggyL4J19ObIjSQry8qogwk0BGgFYze93egA0LkBiAi4ECLUT

905PA76wOSbcpvg8RXzxg3PNwEpUta/oZODZnEgmTUa+IBeNMOQs2BM5p25slMJ+qikGO6bI7WNTOyLtEXE1pduYtvQdkV2hrVdqvY4oeO0XDX8zzM+tuzM1mrnO29uwtcAHkvmE3d1VdhRmQRMzRTarGEVxvga188xFVJZPpM3In57Nu7o08E61H92VX5zlWvoixovoXGLzx/C+xdQjnhDaAALxpuAA5L6MzeouoXMLxNwi+0ApvbhHADN9m9nS

5vyTDcz9RcWTAmkhkBVq/Q0pv20mSOAWtpYn2f0hbQXHUgK/mzjcFu4XRbktzCLLdZuc3Ep/arFvY5rynbnO7nbzv52C7hdou8XZLul1XzR+EPc4GdIvgXw74H4tC/aeVlzxf7mYMzDMLFdph3ItkN1EMkig2QX5me5EnMbPsYyHd+t00or0It9PiLSawZwNeGf6vRn1dsxTRecWmvc1U1xiwWtmvKj5rLegUuS9JtcWkZpZ1Yn3em6bWfJqYDGu

aihN1mOnXrlOxFHEvGa1LNzyhnc97v0zMdqBmPM0F+AwBNl97R80yuHV3Xl7D1z8y7uLlTrFcb2763ysQkCrZ1U4JDLe/UcPvRcz7yJG++UQfuxipmb9yg93VpJEbGD7m1RKIlaqMP1SUR2pEIc/6xb/+yW0AZkcvr5H76imxcBgdepsaUw2yOOLmSAafgpV2sLA6siWpjHmDnm9h/PV6ee7pqwzxUAIjEBVw+wXAH0CEC6mjAEINxKuAIiNBikR

gGAG2koc2fTK7kO3fElGSBQycnAjPYY01vtP4hRofyOppCi4xfP2n8SOY71U22NVNjhDXY7dXIay6jt388lsY/MfWP+Gz4uE3NRQkJ497uyORUzClON8GnDKgkKGSAwMZdTiJr1v0duoZmteUQ9nrY1/vNXMZ7V4XtTUjPTJVF8D7XYvpQfdDLx/Q5a6RVFqOmtrpD/a4mReSsKYMUBxrTxmtLP2VxYfdAyEREzcUE+nw0ic7PXWHRIb+fRYmWOr

3aq69lDpC8JDxuPHw77F0i9wDaAUC+ADmPlLKl5vEfQ7rF4i9xfo/MfOP4QtUopMROzLrbpMvSYT7zUu5KfVk05ZkZc6edhAPnRwAF1C6RdYuiXVLsdf/N+3sb/Nwm5R9E+AnJPnAj1IKlTucxoysK4S8ivymZuqB8L5F+i+xfUvCXwgEl5S9peMvWTxciaYcu5OXI6YNyCqQnpjJPUpstfiwczDM2YBAHL1E8GXzdaC8Ieq39lxCh6bmNvTwuzk

x2956gVOrovUd9XE/SHFrss7yKzA/itXj01uDx8bu84KS1Hd8KuS5SBOuwB5Z/hzh7RhuoCrTpg0Sp+NGgPzTJf/18D8Deg+4Jqv5LWz6Xdc+V3vP9dwL/HPeDJz3Zz7fiO+0VBsIZwZoARAXMgQFsuOwI/c4qDNABQzgIWLUDLD0BWwbQNXS0caDxABQFAOPPF87+Ia/n/DpI6geWVwA1CCARcMsqHBQBBgkgHoEYEqCNAOAxSEYG0CHJ+g7z8R

yowf451qQtzO5vcweaPMTzM8wvMrzG81382vB8H+c4bSzRMR59K+BfYYfWZWjdqXCAEH9h/UfwIUjfL7RiFE9QsDqtI1AEgXgT3NRUbBnfKZFd8XPH+X2w/VGyAK8VHJkQ1oM9d4Cz0zjYu2ek9vYFXLtBrRngNd67U72NdIPPgIT9rvGaxT85rdPztdM/LYBe8MuPl2TBzeVCwNEqbIrkxMVHczGnsQfOe2N4F7eSytN6HRAO/M1LEpQlAeAVAE

ABOC0AA7Y26YOYQABezQAAbTDqkABQxUABO7X0tTAywO6Z7ApwNcDq3cJ00F6lZuRpNafKy1icu3OyyZ9CsDNk/0IAdXyi8YvOLx189fVL3S9U+cF3AN3AqwPoQvA9qhcC5fEZUb4ZTRA0S1uvVAxn85/BfyX8V/CEDX8N/Lfx38t3JiXdVEMNyCmYrTTGiptmDEYhvgz3c1GTBRLZWSqsPCVwgERyKFDBDMICbhxeUTsDTl3h9wC+EQEIpKlVYC

NXEP0uMgPYBDUNeAtM34CxrE1yEC3+RP1g93jSTVbsUVWLlb1yXeTBz81rGw0Kc2tEMwe14BAr3JU+9f6BVINA2vy0DoZGAMXtiBSxFVdAXYWWBc7aV62plxPXxFE8OjfeynBYUPeAKt40UPV1FCwYCFYEQoKEkUR5gu1F0Z54QB3oYtgXK1ICb4WyEuAFED+2MgZgjEIXgsQiNX2BcQqcBqsgpC3y2ArfRhVYE4gFDHxZsYCJUERX2ZB1doAXRV

V4YVVHTzRthHEL2kY1IWIM18EgxL2S9kgw3zkdybbL3zxjMCyEEhv3CyCURNHdz0ERdGFC1EtUmem1MYTHU23z9AvIR0FtQvCQClD4g7X1lD9fFIKs8lQ/pDnh54CJhjsjgbGBrBV4cKFjUSvB4DPhsYN3wXh1Q2+yG0jbLm2FD6vfP0IkWvA1Tg0YNCAOcYHbP7nndgjFXTCNNdbXV119dQ3WN1izLAL38A7GFF7oNOcAjl5LgHSixMI9XRWt98

rIZkQEdjUvFVpa4PiAq9U5L1E9cpgh4ASYL4CZCFc2rRAT/Zlg4PxLsOAsP0O8QPY7zA9pnLcXGs5naDwWck/Y4JbtPje70Q9SXCoHJczgSw1o9q1GMJl5EHCNSxkrtLTUWF6FR4F+Be9DyA+DKPIN20CQ3Lj3U5p2QEInV2zTezBDF1CEM+sxPL8JKA74RIBGYKqc4BxQavJhl7DRLAcI8ghwvkKhCH7C0xt8qnP9nys/Q3XF6DkgC4Dd9wCboz

S4nzcEJKBawVsIiYtgDsPfxWBY4HgdMYCxGURTMNMDHY1PbCTQchBPzxFCcHDGytCv9IhwkczPMh2lsnQhR2y8TSVMHIpdbOh1h5tQjFBa0xkSZAq9QlMdQjDeHfz024BHUUMtCJQioFXBSAHgFbAQILeTRZnAAiAhBoQKAAPBlATQFVNWwA8H4isvY4HshuiArwm9VZG8Nc8WHTWS/E5ggSEX49MBSJNC+HbD1jCaJKxzjCJzF5DNVUNaK0ScEA

DfzLBiAJIGhB72ZlwI1x4HrXoMF4FUO0ZnIiPSNAXgd/EPc6wUfSYCN+XGA04xic4F+ICPFp27DjFCMx6t+nfPX284zKBR4DQPC72TNY/RBW2DhApuwk1Vw1PxD5m9TcIkByXaSiddtRdZ2xhjMeeCQE6zTKmEsMwI0Huhbwg3nvDvgnOVt1XzC4GDMHtV8IGN+PZx0HcxfQnxxdJfDH2l8yfQqVc0GOA6OR8jotH1OjSpcn1DEalRuWp8mleMRa

UbLRk2C0ulVqTZMwXfpSrRrozFyTcJfQJ1J9epYlwV84tWU045RpCKKbhj/U/3P9L/a/1v97/R/2f9X/f20G9wLEOndD88C4mKdPxE9xbDeg34E147ICyCbDVFR0xHZc7J4GSZrtKqKZsRkWOTmDtWMNxHCozf9y1dOAoZw2CKLFqL4DZwqyT2DOog4JEDk/E4LXC0/b4wuDKgeoF3CyzfcP8jsKayAype6FwxJUqzFQKGRdRDOyWjdaFaM/CG/T

Dzo9+/CQDjxqgRf2w1KgGPDaN5VH4JHUGtF8OX0gXPaI/DHaHez5U6QqYHGRaYgEm156NVJh1wZgmsDZiLgDmIsgGIiOiYikbOr0fZsHC9WC9HcIW2tCIvOIK194ve0PlDN6EPmdCKIjATHZiZKZEQFS4iSOK4WtSiP0o9OCJgLsQNRSK2QzQ/m2TixQ1OI4iIATSO0jdIuPH0jDI4yNMjzI6EEsjrI+WxVJzOetVNJNgOC3ps3PIigSA8KJWXOB

TnGflq8owiDRjCoNQKNg1rHbeKTDQolMLt4UAq2JtiKAO2IG9AaX4AwjF4fiAJUwMToMIRMLHW2CgX2C4lVcN+D7wKc2bOqyGZqVF92PgjhONXVdRw9gIGcGorgOA9mo6cNaia7AQKW14/CWO6icza13zMNw/kie96gZClGi1nNZlNJdbP1y+8SVb9l+8UBcJWQxVZQ2MzkqZczSdi7dAREqdz7XjxUs9o4wKBjC3ZN0eFJAbH1LdUADN1QBM3cG

IKkq3DDjc0RffH0OiQY4t04TU3TgF4S03fhMESypYRKejKffwLDEW3N6LpMQg+nyTJ7LPuT+iKgRGLVhkYq/xv87/B/yf8X/N/yF9NqQGNF8boyRNHcXhORIUSzovAmUSsxYZTgNoYooKisFTZLS7idIvSJAgDIoyJMizIiyKsjytSg12lTlDyD3gNnAbWCgbIEnAe0HfbcnZsScDfCpDqNSKALxxXAzVh5nw/326s2AixXqi+Y9YOMkQnOIhO8R

YivTFiJrBuyu8kEq1zzMTDAaPQSpA4pCVizYsiluDWRRAXO1CPU2HKtdnKeMqcjgShMplthE2MP9EnIxLP8L/UxLRiLEzGOsTaZDK339EjbbhnMcnZLS6B9AfAFbB6gOABOB1uCf1NjEnFIzSMMjLIxyM8jAoyKNFpUo3ACnuNc0n9FdEI1V11dLMMiNcwmIwLDNk350gCv/fHTUg48BgghBmgIWEOT6gZQCkgegCEGwhiASoAh1KgTAOBT7zUFI

fRHYtaOdjnwpSzdigQxx3hi1IQ5OOTTk85IvjcWMJFOBYSBXmKjdQlY0UlkeOyOM4+w+eEQFckjGUZFwlAlR5CbIDSXhpqWNVyD9uY3bzATKk6bT1coEyP3sVGaWBN2DBA8WIzMfZPnhXDczRvQ6SHvQaPQByXYpE4smaFa2dcwYYCBEQx2LWLrNKA1tQJkIwba1055vaZJe1qEjj1usl7PBgBDiUt8IutnHZgBGBUAGwMABOUw5gnE5gA5hnocp

SCwA04NLDTI0inwblm3QIOidLLC2GssolTtwZ8mTCIPTYVqZy07itIoJN7iQk/uPCSh4keP8tbEn0RjSg0uNIxUD0LxKlMfEudxKDrk1I3SM4ATI2yNcjfI0KNijV5IaC7bMeDfxcrToiwtyKOyK01CiDWlrgTIc1Hsh6teDDqc7Ub+3GQFgnRgUQTjOmA04YSRT1H0aDOsFKSVgscKlSJw+MynD5UupPFFlU+BJgTlnRuzr1m7LVOMM27NBLF4t

wyoCZcO9StSsMsPZSIL9DQM+0ERyqA0TpsiucZGxRQoIHwucjYuvwfDOPJe0JSDA6NxBDBPLezoEfwuCJWJ/oVdK2BVJfFnNQIkXXB3SqY7Lj4gD034BjjYCQUMbjj1CQVbi1I2QQ0jC0nuL7iwkweMiTR46h1VpAoWm09D40ARF98K4wCN0cFefR04puUnyJYjowgLxbigvNuIM91IiQHoAOTLoCwMcDPAwIMiDEgzIMuMoyGOBCMxDDW9DBN1D

H0K4/BlXFIwo9Q3j/IreMscd44KK78D4zr1TDW0puBFh5INoBOAEAb5z3C+/eSgnh35TYB+VvXFJJJj2QwsAnhe6eq1y5tFfbGDtbTGEjYcMaK5WZiCLbbwlTVghcSv4ZtC9O+kFUznnqT3ZW9P2C1UtbQ1SlnFBJ1S30uGSe8hSbBOU0OIDMDMQVSWLIzTleWQxIT1ePfk9RopZ1L8NXUw5ywY6EqfkW8mE92MnV9o+xOBiXE20Dx90XBxNmybQ

Yy1qVQ+SJ3MsW5d6JjEdEwEQSde3CLWF879abMTclsvIJDJ0lJXzDUVfJQkSdf/Xc33NDzY81PNhOEAOvNbzQsP3iIeHKx4EmRBsDrUmA9fEigz4cBxfZQ9PCnfj1KKZBAdinXd1MQpmZjUhIyMsDCWNdRLmOtlerCpLPSmokyUvSZw69LnDGkhcMu8GLC11EDpYvqJhlzg5D0qAhwXpN791rbyQeBb7RT13gRkpMDPC6FFAXhMM7KZOr8YMqhNm

SaE/FK49PU5DLh95cUEK9i51SEKt1BVEoAEQC8WB3Kp6DZfjlpIkEqPN5dGeE17oxgmGyYEpwS4B3SHIbiFhy9KeuMiREco0EJiUcjMCoyBBOOK09148YSTj5MxjPwcsbZUxxsPLPG28sibEmz0yXICiIYTqvAr0vhMBFyNK9kgOJAno+gr0IxlivatCszTHOwXNDhGN3MMJJQjOOlC7Q3XzlCDfPONfUsvF0JOsenUKFPgxcTKI1tksXKyCl63M

O3hJzUNeOsyzHTePNt4NeMN3j7Mz7KvVD4t3TTCKgQYAIhllE4BjwkgLoBkUPs0CzN8PVH2nOA+IGPMEQMwasNflOIbg3kIVPfyGWMPUMV3KdSoqKXNEVSWV1pYj0kBPKTQ/A73PS5U/LKvTy9YrO0M70rqMfSeo59NOCvjKnKe8sE79MBNGsnsJD17WPo0EsNHLrPkQdcoZDazHtCS00CrrYNzn0Nol+0K5xsklMmzWE47PF9josGLcTZfKNIHd

UC26OJ97omX1x9fAkMTUT1smn2aVtsjuTCD4nZnyiDnLV+jSC7E8RIcSi3O6MUTHozxMlMZ3REWV84Y/xNQMOAMfLaAegECCMArxSfLqNWXIyHMxcrczFgdy8u6CUoiQ2uFGR2reNFICJ6OpzWN9BJp3WNWnNHLMULjbLN1duAnHOvy8c2/IQVUEBBLKzDg0nKljeo8QLljqc2R2NT0PdOmwoVHQcUuArU02EEgjrLGnViHtc5z2iqPGApfNnPVk

VFdECn1JnsUC5guBjWC/ApRdsCsRIWzEi1H2SL8XWIkIJE00yx81yCrbIalH9L6JoLIgvNO+NGCnAoSL2E0GPR8UihtK4LQrZtN4KUAtUGwATgVcEqA+geCHwAedGPHqBcDECGWUWQbCEqAEuPzOvkTfVPhhQF6V0Kt8jQRB3tNFPJPXtYn3VDEmCqAqzgnoWtYqM/EgGBmOY0+6azlmNbTZhU6zRtcVPRy6o8/Majcsq/NqSLC1oQaSVUppPvSW

kp/OQT2k19IkDHvKQKOVrg39Lz9VY4xEad3MIV000RksKUuB8WdbGXwQiybLCKhPeZKbgegVmSEA7/VcDwNFwIchF1NAA8BO56gGPEXBMnLFI/9tk7rDxSdAtdPOBDWcMMjc+PcKP4LEnOAGaBJAEkoTxXqfYGRZ4gGAEqBVlAiG3A+gErWNNb5U3ykKFKBRQWNaIo2Wrx7fSRBUoAoR4Hwy6rd3yeUFFVQq4NeIX4CDD8LIelMxZjCQ3JxFigwo

BUMc24ogSBYiP3MK70orKsKTvR/MWcn0qrJ+LnCp7xAg6cpoluDjSfyQhLBLYhMIS21A8mrM6I/rMut/DOZO/8KgNEp6AMSyoCxK48HErxKCSvoCJKSSt5ISNlIlEujwegU8DzgBQfQGKQWQFwH0B9gZgCHIzgQgGWU84asQzLP/HZPBSfQOAH10jAPOCgAE8CEBjwKAPOCMAhySQGhBlTZoGUA+gOsopL2jWXJus5LNdM8jiYmIt2imS1X0ScYy

uMoTKky5ZXxLCS4ktJLlYosJxjnATonLwUmZ+34gIGE6UJUC8JRHmN1HI0T1kAGQ+2NZ40dGmbVTyjJmRJe2U+Gh5fs1JgjtLi8oWuKeY8cIvzscmpLXEictqLgT780rIfSnS5/JdKzgkOTJdKgd7LQ8iFIEpVj/0iYWdNAGc1FVd4BDtWNE64/vQnh4SxE0+DoC5Et2S/0KfOS1iACgFUAAoaoGYR2PIbNcxVhU60EgKEucqesPYyXLlzVcGXLw

i/wsAH8lcrB8uilPMeDFQj3y5Usc9CvcKDdQ7c+Gw090HIUObz3cXTwtDcHNOPQB2izou6Lei/osGKWQYYtGLxigPIMzmcQsFlorc5zxRCI8nULySMefcDsiiQpvOTzm4wRzTytKjuNZL2S6oE5LoQbkpZBeS/kqSBBS4UonzFQgSJdCwzCei4gGYh4PEj7KjomZsjy+yBK4os4G0TzaMmzIwq7MpryCi947dx7yXMo+P7yJAWivorBgRippT3Vf

vV60wMcnDMy+xAj0RoDS/yGs5I9RXmukoc4ZCGQpkGB2MF9CmqLKSSLfqxlTTCkCqj9FUnYIJzXisCuaSScirOdLvi+Ctk1EKyIQayu9fhAyoqKIgMEtK89rOhM1Mba3UwQoMMtntyK1aOpKtgWkoasxc4EMc0JATIGDBogKmDCBgwCRFSKKgF6uYA3q6gA+q+iYgq818i6/U0SHYOnyoKs08INf19ElnzUhlyzEuxLcS9cpTK0y7cuzYRTCLF+r

/qwGq+rGi6d2aLZ3VovKr0AbcFXBikIcmKR6gdctqqwLYyAUVT4GOygz5A1WntMPxKuMQEYHDTGadckx4ESYCvc4E2BF4f4mGrfyh6UMLzStYImrIEswseLbS/HNFj5q36TNd1Uv2TaTtU10vfypAqJK/yeLYxHoN4eNTSbVHgznO6zmcTGimQLqpEuuqQ3Gkrgs14IlJ2ZmE5ArsS2IPoCyB/AYASP0zYJOA9rH4L2BWyXogovBqpqYos+i4nRa

nKKaOA7KrSRfd2s9rA6yGIKDwreLSuy+CxcqbhlwSoEXBcAJsXwABQSQHhBuJSoFUIRgbAGYBNAe2OiTpivcvBsoSSOIX14qMDBZTOIafmJEx2SQwxkUMajR9o5eQKDnSyRfCw0oAYK3KxhMeAKFNL8MKWuMLw/PLPlrhYxWpeKSs1VOgrlwyrNWq38hCo/SWQT0tRkDw4xHZsLiNbw9cGzVwxQF5eJB1srra42JCRKKkC1nNUDTAGaA4AVsFh1N

dB2OgD8U6cu0Z0wL1OdqJsrryccm4Z+tfr3633QkKWXGFGXVrfFMB+JOrCJgfjITY4AG1yMx8u+UHtDfj4MpkYdkX5MqU2TFrA/P8slqbi6WpyzZUuWtAqVahbQgq67Veo+KYKr4s1q1qwsye8f0LasZzNQIKGZwzEZfDwrw8wMrtTRQHkL1iAymlQo9louDNtrh1H+pHYLgB6pYTkCaXwuifa1NjhBypXIr8D84gIOpMU0+MUhqSiyOuT5Ya2go

qK1IbOtzr86wuuLqDwUuqFhy6yuurqbE+jiUb1Gs7KbTia9OpQCQIHgDzhFwZQHoA4AJIAQBikRcH0BNATQEwA48VsDYAeAIWEiqdyqYrFKZi7KzRCZkeKhQxaHZoNVd18eEk99VZLms4p583JJ2Kb4E0Q6rU7ARtfLj4Y4u+VY5Km1VoLioholqzS0htnrJwh4qobo/GhpvTIK+hqWr1a27ycLtaxCoFA966wwPqOIdg0sgharZwhN9A4AoeB1Q

uvOnYES9sxtrIyxso91cyvoHzLCy4sucBSy8ssrLqy2svf8JzZitkssGe2tGCvDWzRxNgXFALYAdmvZqLKSyssorKqymsp6TB09r2nzzUz9R6d48rYExheXTUFlpy8RsCxk+IX3x4Mz6YGmfClHF03IoGwf3ziE1Qw2v4hr4KevMoZ6qbXIbJq8/S6aZqu0tmdqG4nJg97CzVLgqt69ao/SgmPWpTj6cmwwWNes8M0EbRQIGEWayKL1AbzUwM51I

q7wqRs2ayS82IW41IYkFIAkgD50LrP6icvB8ZG26vxROKhkpdr3w3ivwj+KzDPlbhPKYE/jEW6zlEtdrVgSCg54DFpGCsWjyEUrjcB3NUrTHdStUjvKpTPQAfGvxoCagmkJrCaImqJpia4mhJs0EBI44CmRpPas2QjfQiuM99HKxfiCh6tGsDcrTQ2TM8qSJdPJkZfKjkq5KeSvkoFKhSkUsy95bQzjkiPUYKECh/oczM1krwrijh5RcLKuNDpM3

Ko2sAo+zKtsEw2x2KqOvfwVczgGiVuhApWmVvELJiqfIlL9yvSgwjHy4CCMEjjdmp2KKvSLPl41NOFqWaNOPqp2tBq+EkIagEq4pIaAK09KAr7iyhumrCspervy6Gt4sdL16lauYbaW1hqkDfMtwqU1tqzLnasoshAo5auoPhrNqGcZLLipshNZt9SNmoXJurTrCeF/sFG12vzYca5QHer1QfGv9FREn6vVBca6DvjSVEvIqp8Q6vzVTS4+UIOhq

fqnNPf06CmRmea8ygsrebDmj5pObvm0Azjr4O16sg6AapDvrTm8WAw8aeCrxtJqknRoHp0v0SaS6AJFEYCHIEANoAyd9gZQByNRSqg3dURkLFAc8eIPDP8KWqw91rh8VJJRChEIkpuBy9iiprRphUzJlqbe9DWgabXXHFtnFt2zHN3aKGqaoKzIVI9vtKbCteqOCN6y9vXDfivVLmJKgf1uWcTU3P36TJmpnNMz1aJdIOrFKblpo1AGeaOiLvDfn

JmSzNHvybhFkFsrbKOyrsp7K+ygcpZAhykcvOau/S5snLrmpVtAdPve5qjdV9FAPi6LARLs7Luy3sv7LBy4cuxjAaSmJy8o7ZJI0xsaZYsih3IIVK9CdGQ2y2LRQQ4FnTTnb8RFr7DZqwG6n3UFvnysYF8o3biG1ptM6LS/mOqSiWg9us7LCslu6aKWpcIc6L2l9JYa2LKQISjGWhTPxwbDO6DU0tFI6pmjJMwRrCl5ozXhqcb64VrvqfnB+v2TU

DDaWqAegfQAhAjkuVsErOjKcvy6cLJfQAakC9VrQy5kjDN3tfw3Vv/CBu5YxNIImEbvOqUJcbumaOKkPVshrWmjN8jVVF3M0r2I51o46uOiEB46+OgTqE7GK0TuXAA84vOVISReaPZszSZKpPhTgKNq/EXKuNqkyE4lvMTbHWonqYyJANNv8qM24KqzawqnNv9b846KpQbO2Ep32d+IQlH9DJIuwyGQOQoBksgjQ42zx6m42zLbyYNZts7yCq3cu

cyO2sqrcy1IT7u+7fuzFMSbB2jtmxglO2vEFreWtJOrAocz8WnjXTTGATsPCXqrCh+qrgw8NVXOVxPzMsk9LM67iizpW6rOsvWeLj287ygqGG89tgrN65zrdKpAiYrvbO9ThtXzScDKlwqITDWhu1wHUzB9Cnur4JcknY+2rMQS+rirXtHq+Hy8YEOujrxrkOhfFUaIOqDs+qO+7RrCcSC/vrILQ6mPm0Soa3RLw7HLAjseRmy8rvbLKulLpq70u

ursrSXG8Dtb6e+oGoJr5fFOsuzJla7JmVIU+oGhTYU/QHhTEU5FNRT0Uu3r6TTe3FlVpEgHp0M0LUXeAfwWqhKgSAGDUzNpKNaamLIo/oOeCV74qpDBvLQ1Vsj4hZ0w3JazsuH9wyz/y/JgQBXTXGDIaTC2Wss6b8hPts6H8xBM+KNavbqvaDuxCoxrPO9wpO6f6M7pshScIzOmjRk8ZHHtG3eKngdK+q6t8UnYrjw4r9q1VsAbfUz2L4r51HVuh

CpgMDBa0GwYzPxUqnZXrABDWKPNk9MYS+rOBfYkoEBJAB/R2AHawNXN1x0wXYvsNzMdGgMdcI2Gx1alK/dUdzzGEJGUG0aNeDUH4TKCFhQLy7Rhvj9FGNRvh5VNVTYiRHYnsCTWMktPYyIk4eN1qoqmyKd9fgQPvRoGwC+Cyq541fN611Q6GxgdVZZXuyrdemTP/TU87VUTiDehiSN7HMu/pKrzevvMt6KgIWG4g2AGPAPBYjRKJxj0wAZBGZzRc

fgvd7TFWVOBljN3wX4+IMVxIDopRAVoC07HToAUtvYBIj7P4RAdxhkB9psvz92uPoslMBjbpmqz2nbrT6nO2WJGaP09URQrc+17w4hLIXWwmQ2c1IXfbGzC+oERLgWIS9o+c0IuNjX6NgZwZMwegzX4do7irA6IsPeC4g7AwADyNPfVwLJE6Awzcvob2rg60RdyHiB3hz4ZqK0Cn4bTc/hoOqTTdG1LEw725QxuoKo63NJjrfFKoueGgRkEe+gvh

otwhGoR5OrGVCgltPaMVoRhDgBldKIE7BL1aABihMgagh8IdgBgEIAEAUSixz8maoA5HORlYDT4RAMBC6BOwfQDZBcWucRGG5B7kbfU7wWpAFHikNkZj7R0HkclH8HAUeXBiW5ygVG+RgUaFGZhwoHVGpRjIC1GOoia11GlRjIA1NbCw7GNHDCAUcXBBmxkYlGNRjIFyMdG2qUtH+Rx0c0aSC10YFHwsYILTTxR3kb1HBR/KvbyrcL0YyBJsY3pD

GnMnUftHAx/OiFhlOE6G5HmAbAHBBmQQWnvxZ8qIpxRZSkwQlBUxuEHwBP6QjR2L502ej0oDbP4AgAjAHAgsJgvBgAIBS4D9QCg7US4EUIwxpaUU1eMEge5H8QEgGejGRvseIA2QBAFK9Bx7gmIAhyNIwQBJsO9mCA/xXMxIAnsFSGKQ4QNSH0JsQAAAoV+agF4AHrPccTRsvHoAABKekALgG2Y3CAQNx3AG3G6B3gDvGjM3caDbTx9sdjGW0V+B

tH/eb/KNRXJAuC9BuCLyr5s5xsGBTrsAIgE1sU63gjpGSXSaBzhL0ZovbG7AVcBvlmAYssWQpxvaFnHNAecbilGEOoEIBGAIWGl9SzXv3VZggfCb76eR4LH0AExtPEZL2zG1soJ8JwieIm/I9oyUhvpcIEqR6IaMCAA=
```
%%