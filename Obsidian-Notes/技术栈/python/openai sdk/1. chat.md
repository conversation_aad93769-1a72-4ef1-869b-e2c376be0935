## create a client

first of all, to begin with, you have to create a client object, using which you will send and recv messages. 

首先，需要创建一个client对象。如果是第一次遇见，你可能觉得有些奇怪。实际上，这是很常见的做法。

```python
from openai import OpenAI

client = OpenAI(
# common parameters are:
	base_url='http://xxxx.com',# no /v1 suffix
	api_key='sk-xxx'
)
```

other parameters, to be honest, are useless in china. we don't use project id, orgnazation, etc. right?
对于openai, 他这个client对象实际上参数真正需要的也就这两个。其他什么project, organiaztion什么的...不会有人真的用官方的key并且用到那些服务吧？

真的是真的，虽然他有一堆参数，但是实际上，真的用到的，就只有这两个，什么project,orgnazation,websocket_base_url都基本不用

## chat request

#### not streaming 

just request like this:

```python
response = client.chat.completions.create(
messages=messages,
model='xx',
)

```

还有其它参数, 不过常用的参数加起来就这些：

```python
messages=messages,
model='xx',
stream=false,
# functions, function_call 弃用了，我们就不讨论弃用的了
tools,
tool_choice,
parallel_tool_calls,
response_format,
```

不用工具也不限制格式的话，就上面三个就够了。下面四个等下会说。

返回的内容可以直接print(response)查看。对于提取response中的内容，简单的，通用的方法是这样：

`print(response.choices[0].message.content)`
### streaming

steaming 也几乎一样。区别就是steaming在创建的时候并不是创建一个response,而是创建一个可迭代的对象，我们暂且称之为steam.返回的时候，也可以直接类比上面的返回格式。格式相似，只不过：

1. 流式输出返回的是一个个的chunk,而不是一个完整的message.在oenai sdk中，获取返回的chunk的content的方法是：chunk.choices[0].delta.content。是不是很熟悉？我们可以认为他用response.choices[0].messsage.content把整个response分割成了一个个小的response chunk,获取content的方式也不再是choices[0].message.content 而是choices[0].delta.content. 这个delta可以理解为delta_message
2. 非流式输出的response中有一个usage问题需要注意。在流式输出中，这个usage要首先在request中设置stream_options，然后在接受到最后一个chunk的时候，里面会带有usage的数据。其他chunk中的usage的数据都是null。我们等下会说

ok 经典的写法是这样：

```python
stream = client.chat.completions.create(
stream = True,
messages=msg,
model = 'gpt-4o-mini'
)

for chunk in stream:
	if chunk.choices[0].finish_reason != 'stop':
		print(chunk.choices[0].delta.content,end='')
```
#### stream输出usage

usage, 即用了多少token, 这个内容在非流式输出的情况下是默认自带的，但是对于流式输出，情况则不一样。对于流式输出，正如刚才说过，如果需要usage,那么你需要在request中，也就是stream对象定义的时候加上stream_options选项，内容为`{"include_usage":True}`. 也就是这个样子：

```python
stream = client.chat.completions.create(
	stream=True,
	stream_options={"include_usage":True},
	messages=messages,
	model='gpt-4o-mini'
)
```

对于stream_options,虽然他的名字叫options,但是目前只有一个option,也就是include_usage.

那么，当我们在函数中加入这个stream_options={"include_usage":Ture}选项之后，返回的chunk会有什么改变呢？答案是：1. 所有chunk都会增加一个usage字段，但是除了最终的chunk其他的chunk这个字段都是无效的（null）2. 在所有chunk发送完毕后，再附加一个chunk.这个chunk中有usage内容，并且这个chunk的choices是`[]`。

让我们捋一下。不发送usage的chunk stream是这样的：

1. 发送普通的chunk块，这些chunk块格式类似非流式输出的返回格式，但是内容是一小段一小段的文字。相当于普通的非流失输出按照messages切分成了一个个delta.
2. 发送完所有普通的chunk块，会有一个结束块，这个块的choices[0].delta.content是null,也就是python中的None.同时choices[0]带有一个值为‘stop’的finish_reason。

那么，如果发送usage, chunkstream是什么样的呢？也很简单，就是这样：

1. 首先，不要忘了所有的chunk这次都带上了usage数据，但是除了最最后一个usage chunk,注意不是之前的最后的结束chunk,其他的usage数据都是null,也就是没有用。
2. 发送普通的chunk块，这些chunk块格式类似非流式输出的返回格式，但是内容是一小段一小段的文字。相当于普通的非流失输出按照messages切分成了一个个delta.
3. 发送完所有普通的chunk块，会有一个结束块，这个块的choices[0].delta.content是null,也就是python中的None.同时choices[0]带有一个值为‘stop’的finish_reason。
4. 结束块之后，还有一个usage chunk.这个块的choices为[]，也就是说没有什么choices[0]这种东西了。然后chunk中的usage中带有usage数据。

可以这么写：

```python
for chunk in stream:
    if not chunk.choices:
        print(chunk.usage)
    elif chunk.choices[0].finish_reason == 'stop':
        print('\n message stop here')
    else: print(chunk.choices[0].delta.content,end='')
```

```
Hello! How can I assist you today?
message stop here
CompletionUsage(completion_tokens=9, prompt_tokens=8, total_tokens=17, completion_tokens_details=CompletionTokensDetails(accepted_prediction_tokens=None, audio_tokens=0, reasoning_tokens=0, rejected_prediction_tokens=None), prompt_tokens_details=PromptTokensDetails(audio_tokens=0, cached_tokens=0))
```
