终于来到我们喜闻乐见的，威力无穷的tools啦！

tools,即工具，是llm和外界交互的重要手段，同时也是实现agent能力的重要途径。借助定义的tools,可以让llm使用各种工具，比如查看天气，发送邮件等等。

## 调用原理

本质上它的调用原理就是这样：

![[Pasted image 20250210090659.png]]

可以看到，tool_call的要点就是，我们把工具和messages一同发给llm，llm就会返回一个带有工具调用的信息，由 **用户**执行函数并告诉llm结果，最终llm给出最终的信息。



看一段代码先：

```
from openai import OpenAI

client = OpenAI()

tools = [{
    "type": "function",
    "function": {
        "name": "get_weather",
        "description": "Get current temperature for a given location.",
        "parameters": {
            "type": "object",
            "properties": {
                "location": {
                    "type": "string",
                    "description": "City and country e.g. Bogotá, Colombia"
                }
            },
            "required": [
                "location"
            ],
            "additionalProperties": False
        },
        "strict": True
    }
}]

completion = client.chat.completions.create(
    model="gpt-4o",
    messages=[{"role": "user", "content": "What is the weather like in Paris today?"}],
    tools=tools
)

print(completion.choices[0].message.tool_calls)
```