# 0. 前置知识

一个fastapi应用的基本结构类似这样：

```python
import uvicorn # 使用uvicorn，这是一个asgi服务器，基本上都用这个，暂时不用考虑相关细节。
from fastapi import FastAPI # 导入Fastapi类，必须步骤
app = FastAPI() # app实例化FastAPI，这就代表了你的webserver示例

@app.get("/") # 定义各种api，本质上是被装饰器修饰的函数。
async def root(): # fastapi推荐使用async def，不熟悉的也可以用直接def的同步函数
    return {"message": "Hello World"}

if __name__ == '__main__':
	# 这里是jupyter中的写法。正常情况下不需要如此。直接用uvicorn命令行中用uvicorn
    config = uvicorn.Config(app, host='0.0.0.0', port=8009)
    server = uvicorn.Server(config)
    await server.serve()
```

后面再写代码的时候，前面三行的定义和最后的运行就不再贴出来，都是不变的。只留中间函数的定义
# 1. 路径参数&查询参数

## 1.1 路径参数

路径参数是什么，我就不多说了。

有几个要注意的地方：

1. 路径参数没有默认值

最常见的写法就是：

```python
@app.get("/items/{item_id}")
async def read_item(item_id: int):
    return {"item_id": item_id}
```

在此基础上，路径参数还可以：

### 1.1.1 使用枚举+mixin限制路径参数值

这是很常见的作法，就是有可能你的这个url传入的路径是确定的几个值之一。

定义枚举并mixin一下str:

```python
from enum import Enum

class ModelName(str, Enum):
    alexnet = "alexnet"
    resnet = "resnet"
    lenet = "lenet"
```

端点函数：

```python
@app.get("/models/{model_name}")
async def get_model(model_name: ModelName):
    if model_name is ModelName.alexnet: # model_name会在请求的时候自动转换为ModelName类型。如果转换失败将会返回错误码。
        return {"model_name": model_name, "message": "Deep Learning FTW!"}

    if model_name.value == "lenet":
        return {"model_name": model_name, "message": "LeCNN all the images"}

    return {"model_name": model_name, "message": "Have some residuals"}
```


### 1.1.2 定义包含路径的路径参数

这个也比较细节，有点意思。就是有可能用户请求的路径本身是一个路径。这个时候要用特殊的语法。

```python
@app.get("/files/{file_path:path}")
async def read_file(file_path: str):
    return {"file_path": file_path}
```

打开浏览器，输入 http://127.0.0.1:8000/files/home/<USER>/myfile.txt 会看到："file_path":"home/johndoe/myfile.txt"}
o


## 1.2 查询参数

这个其实也没什么。只要是没出现在路径参数中的参数，就是查询参数。

注意：

1. 查询参数可以有默认值

```python
@app.get("/items/")
async def read_item(skip: int = 0, limit: int = 10):
    return fake_items_db[skip : skip + limit]
```


> [!QUESTION]   
> 查询参数为什么要写成 '/items/',能不能写成'/items'. 
> 
> 答: 可以写成 /items. 这两种写法匹配对应的两种路由, 如果只写了其中一种, 另一个会被自动重定向到写了的那个.


在此基础上，查询参数还可以：

### 1.2.1 设为必须查询参数

很简单，只要把默认值去掉就行了。

```python
@app.get("/items/")
async def read_item(skip: int, limit: int = 10): # 这时候skip就是必须的查询参数了，你不传入他就返回错误码
    return fake_items_db[skip : skip + limit]
```

### 1.2.2 设为可选查询参数

这个也没什么，不知道为什么datawhale要单独拿出来。就是设置默认值。在1.2开始就是这么做的。这意味着这个参数即使没有传入也有默认值。


### 1.2.3 多个路径和查询参数搭配使用

这个也没什么，就是一个api的函数可以同时拥有路径参数和查询参数。

```python
@app.get("/users/{user_id}/items/{item_id}")
async def read_user_item(
    user_id: int, item_id: str, q: Union[str, None] = None, short: bool = False
):
    item = {"item_id": item_id, "owner_id": user_id}
    if q:
        item.update({"q": q})
    if not short:
        item.update(
            {"description": "This is an amazing item that has a long description"}
        )
    return item
```

### 1.2.4 总结

总的来说，这节可以分为两部分。

1. 首先，查询参数如何使用？就是路径参数中没有的参数放在函数定义里，这个参数就可以作为url的查询参数。
    1.1 不论是可选参数还是默认参数，其实都算是默认参数。不过是可选参数的默认值是None
2. 路径参数和默认参数可以混用

### 1.2.5 从应用角度考虑

实际上, we are talking about GET method, and a GET method essentially looks like this:

```
GET /xxx?k1=v1&k2=v2 http/1.1
head1: value1
head2: value2
```

so, the key problem is to deal with the info in a GET method. 结合 What we've learned, we 可以想到这样的问题:

我们可以如何设计get请求? 后端又应该如何处理?

设计get请求无非就是, 1. 首先设计一个端点,这是必须的. 然后把请求的资源放在路径参数或者查询参数中.

从这个角度考虑, 结合fastapi的功能再去学习就舒适了一些. fastapi一定要有处理path和query参数的能力,这是必需品. 在此基础上. 可以有更多的细节与特性:

1. 设计path参数的时候,要考虑到参数是一个路径的情况. fastapi 应该有处理这种情况的能力,并尽可能地处理得好
2. 有时候我们传入的参数是一个枚举值, fastapi is supposed to deal with that gracefully
3. 对于查询参数, 除了要处理这种情况, fastapi还应该考虑: 默认参数等情况
4. 对于path和query, fastapi都具有优雅的验证能力.

## 1.3 path和query的验证

这个其实也没什么, 就是fastapi的特性机制: 通过自带的path和query类以及python的annotated对传入的参数做验证.

关于annotated的写法,需要自己找一下. 这个东西, 其他框架用的好像不是很多, 用来文档自动化生成什么的好像多一些.

### 1.3.1 path的验证

```python
from typing import Annotated
from fastapi import Path

@app.get('/items/{item_id}')
async def get_item(item_id:Annotated[int,Path(ge=3)]):
	return {"message":f"you are requesting item {item_id}"}
```

### 1.3.2 Query的验证

```python
from typing import Annotated
from fastapi import Query

@app.get('/items')
async def get_item(skip:Annotated[int,Query(default=0,ge=0)]):
	return {'message':f'you skipped {skip} items'}
```

上面这版本是第一次尝试,写错了. 类型提示中指定默认值是不对的,同时也是不纯粹的做法. 默认值应该放在后面.

改一下应该是如此:

```python
from typing import Annotated
from fastapi import Query

@app.get('/items')
async def get_item(skip:Annotated[int,Query(ge=0)]=0):
	return {'message':f'you skipped {skip} items'}
```

除此之外:

- 借助path/query还可以指定str类型规则,长度,正则等等
- 对于query, 如果是必须值,则不需要制定默认值,和1.2说过的一样.

# 2. POST 请求体的处理

从post请求本身的角度来说, 它长这个样:

```
POST /xxx?xx=x http/1.1
header1: value1

bodycontent
```

他就是比get请求多了body部分,也就是多了个请求体.而请求体的类型有很多种, 包括最常见的 json, x-www-urlencoded等. 所以, 让我们看看fastapi框架是怎么处理这些请求体就完了.

我提前公布答案先. 对于body,他基本上以下几种情况

1. content-type是appliction/json: 
	1. 不详细声明请求体类型: 把参数声明为dict,list等基本类型
	2. 详细声明请求体类型:把参数声明为pydantic的model
2. content-type是x-www-form-urlencoded,使用表单模型,后面会说.

## 2.1 appliction/json类型的处理
### 2.1.1 不使用pydantic声明类型

这一节说的都不使用pydantic声明类型, 并不常用,但是还是知道比较好.

对于这种类型,我们首先要明确, json类型有什么类型. 可以参考[[JSON是什么]]. 一言以蔽之, 以下是有效的json类型(以python中数据类型为例):

最常见/兼容度最高的: dict,list
不常见,但也是json的类型: str, int, None, float, bool

对应这几种类型,除了None(不会有人专门设计一个参数传递一个从来不会变的None的), fastapi都有对应的写法.

```python
from fastapi import Body
# dict
@app.post("/items/")
async def read_item(item_id: Annotated[dict, Body()]):
    return {"item_id": item_id}

# list
@app.post("/items/")
async def read_item(item_id: Annotated[list, Body()]):
    return {"item_id": item_id}

# str
@app.post("/items/")
async def read_item(item_id: Annotated[str, Body()]):
    return {"item_id": item_id}

# int
@app.post("/items/")
async def read_item(item_id: Annotated[int, Body()]):
    return {"item_id": item_id}

# float
@app.post("/items/")
async def read_item(item_id: Annotated[float, Body()]):
    return {"item_id": item_id}

#bool
@app.post("/items/")
async def read_item(item_id: Annotated[bool, Body()]):
    return {"item_id": item_id}
```

这样就ok了. 想必各位也是一眼就看出来怎么用的了. 但是我们需要知道,上面这些写法, 都并不常规. 下面介绍fastapi常用的写法

### 2.1.2 使用pydantic显式声明类型

再强调一遍，我们的json类型的body只有可能是：dict list int float bool str None这些类型。其中，int float bool str None都不是容器类型，直接按照上一节的方法就可以做验证。对于list和dict这样的复杂的容器类型，我们通常使用pydantic做验证。

fastapi 对于具有复杂容器类型的body,使用pydantic做类型定义与声明. 

一个普通的dict， 使用pydantic的写法如下:

```python
class Item(BaseModel):
    name: str
    description: str | None = None
    price: float
    tax: float | None = None

@app.post("/items/")
async def read_item(item: Item):
    return {"name":item.name, "price":item.price}
```

期望的例子是这样的:

```bash
curl -X 'POST' \
  'http://0.0.0.0:8009/items/' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "string",
  "description": "string",
  "price": 0,
  "tax": 0
}'
```

很简单,就是用ptydantic声明一个类型,然后在url端点函数中对应地声明某个参数是这个类型. 

如果期望的例子是一个list，其中有很多元素，比如一个列表中有很多Item，也很好写。just like this

```python
class Item(BaseModel): # 定义不变
    name: str
    description: str | None = None
    price: float
    tax: float | None = None

@app.post("/items/")
async def read_item(item: list[Item]): # 这里改成list[Item]就好了
    return {"name":item.name, "price":item.price}
```

fastapi对于requestbody是aplication/json，类型python dict或者list就是如此做验证的。在此基础上, 衍生出许多许多许多其他用法,以下用法大多都讨论body是一个dict，list的话嵌套一下就行了:
#### ******* 使用pydantic的其他用法

**多个Pydantic 模型请求体作为参数**

```python
import uvicorn
from fastapi import FastAPI
from pydantic import BaseModel
app = FastAPI()
class Item(BaseModel):
    name: str
    description: str | None = None
    price: float
    tax: float | None = None

class User(BaseModel):
    username: str
    full_name: str | None = None

@app.post("/items/")
async def read_item(item: Item, user: User):
    return {"name":item.name, "price":item.price, "user": user}

if __name__ == '__main__':
    config = uvicorn.Config(app, host='0.0.0.0', port=8009)
    server = uvicorn.Server(config)
    await server.serve()
```
期望的输入是
```bash
curl -X 'POST' \
  'http://0.0.0.0:8009/items/' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "item": {
    "name": "string",
    "description": "string",
    "price": 0,
    "tax": 0
  },
  "user": {
    "username": "string",
    "full_name": "string"
  }
}'
```

**嵌入单个请求体参数**

```python
class Item(BaseModel):
    name: str
    description: str | None = None
    price: float
    tax: float | None = None
@app.post("/items/")
async def read_item(item: Item = Body(embed=True)):
    retur {"name":item.name, "price":item.price}
```
期望的输入是k

期望的输入是:

```basPOST' \
  'http:/kh
curl -X 'POST' \
  'http://0.0.0.0:8009/items/' \
  -H 'accept: application/json' \
  -H '{
  ​￼"itekContent-Type: application/json' \
  -d '{
  "item": {
    "name": "string",
    "description": "string",
    "price": 0,
    "tax": 0
  }
}'
```

**嵌套使用**

```python
class Image(BaseModel):
    url: str
    name: str


class Item(BaseModel):
    name: str
    price: float
    tax: float | None = None
   k description: str | None = None
    price: float
    tax: float | None = None
    image: Image | None = None


@app.post("/items/{item_id}")
async def update_item(item_id: int, item: Item):
    results = {"item_id": item_id, "item": item}
    return results
```

期望的输入是

```bash
​￼k期望的输入是

```bash
curl -X 'POST' \
  'http://0.0.0.0:8009/items/1' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "string",
  "description": "string",
  "price": 0,
  "tax": 0,
  "image": {
    "url": "string",
    "name": "string"
  }
}'
```


#### ******* 字段验证

让我们再来说说字段验证吧，之前path和query都有，这个肯定也有。这也是使用pydantic 声明类型后非常方便的一个功能。

这个也是很简单了，很熟悉了。 正如我们之前在Path， Query中使用的那样，对于请求体也有类似Path，Query的简单验证能力。like this:

```python
class Item(BaseModel):
    name: str
    description: str | None = Field(
        default=None, title="The description of the item", max_length=300
    )
    price: float = Field(gt=0, description="The price must be greater than zero")
    tax: float | None = None


@app.post("/items/{item_id}")
async def update_item(item_id: int, item: Annotated[Item, Body(embed=True)]):
    results = {"item_id": item_id, "item": item}
    return resuls
```

title依然是用于自动化文档生成，在程序中没啥作用。

在此基础上，还可以:
##### 使用其他pydantic支持的数据类型

在http中，有时候我们传递的信息是更加特殊的str，比如时间，uuid等。在pydantic中，也做了对这些类型的支持。可以直接声明为这些类型，fastapi/pydantic会帮我们验证。


```python
from datetime import datetime, time, timedelta
from uuid import UUID

@app.post("/items/{item_id}")
async def read_items(
    item_id: UUID,
    start_datetime: Annotated[datetime | None, Body()] = None,
    end_datetime: Annotated[datetime | None, Body()] = None,
    repeat_at: Annotated[time | None, Body()] = None,
    process_after: Annotated[timedelta | None, Body()] = None,
):
    start_process = start_datetime + process_after
    duration = end_datetime - start_process
    return {
        "item_id": item_id,
        "start_datetime": start_datetime,
        "end_datetime": end_datetime,
        "repeat_at": repeat_at,
        "process_after": process_after,
        "start_process": start_process,
        "duration": duration,
    }
```

期望的输入是

```bash
curl -X 'POST' \
  'http://0.0.0.0:8009/items/b6abc40a-2d46-452f-aff8-fdcf22e3dfa7' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "start_datetime": "2025-05-16T05:46:45.747Z",
  "end_datetime": "2025-05-16T05:46:45.747Z",
  "repeat_at": "05:46:45.747Z",
  "process_after": "P3D"
}'
```

pydatic内置也有支持的特殊类型的str，比如`from pydantic import HttpUrl`可以导入url的类型，这是一个有httpurl约束的str类型。

#### ******* 添加额外信息-example等
##### **在pydantic模型中添加example**

加入输入数据的例子，like this:

```python
class Item(BaseModel):
    name: str
    description: str | None = Field(
        default=None, title="The description of the item", max_length=300
    )
    price: float = Field(gt=0, description="The price must be greater than zero")
    tax: float | None = None
    model_config = {
        "json_schema_extra": {
            "examples": [
               {
                    "name": "Foo",
                    "description": "A very nice Item",
                    "price": 35.4,
                    "tax": 3.2,
                }
           ]
        }
    }

@app.post("/items/{item_id}")
async def update_item(item_id: int, item: Annotated[Item, Body(embed=True)]):
    results = {"item_id": item_id, "item": item}
    return results
```

这个也是给自动生成文档提供例子的

##### **在pydantic field字段中添加example**

这个也好理解， like this

```python
class Item(BaseModel):
    name: str = Field(examples=["Foo"])
    description: str | None = Field(default=None, examples=["A very nice Item"])
    price: float = Field(examples=[35.4])
    tax: float | None = Field(default=None, examples=[3.2])


@app.post("/items/{item_id}")
async def update_item(item_id: int, item: Annotated[Item, Body(embed=True)]):
    results = {"item_id": item_id, "item": item}
    return results
```

这个才比较符合直觉。给field添加元信息字段，就类似给Path和Query添加元信息

##### 在Body中添加example

晕了，整这么多添加example的方式干啥。一般用不到，就先放这里了。

```python
class Item(BaseModel):
    name: str
    description: str | None = None
    price: float
    tax: float | None = None

body_examples = {
    "name": "细胞生物学",
    "description": "考研书籍",
    "price": 35.8,
    "tax": 0.6,
}


@app.post("/items/{item_id}")
async def update_item(item_id: int, item: Annotated[Item, Body(embed=True,example=body_examples)]):
    results = {"item_id": item_id, "item": item}
    return results
```


## 2.2 表单

这个留到后面了， 他也没讲这个

ok,终于也是学到表单了. 让我们首先先说明, 当我们讨论http的表单的时候, 我们讨论的是----

### 2.2.1 http中的表单

~~当我们讨论webserver, 本质上还是在讨论http.~~ 老样子, 让我们看看http中的表单. 这个也是相当常用的一类请求. 当我们在讨论表单的时候,其实讨论的是一种古老的http中的名词,表单(form). 时间回到很久以前,那个时候, 网页上的表单(就类似你的登陆的时候要填写用户名和密码,最终要发送, 这就是一个经典的表单的功能.)中的数据需要发送的服务端, 如何组织发送的数据就是一个重要的问题. 如何发送呢?通常使用这两种形式:

- **Content-Type: application/x-www-form-urlencoded**

这是最简单的一种表单的组织方式, 其http报文长这样:

```
POST YOUR_SERVER_ENDPOINT HTTP/1.1
Host: yourdomain.com
Content-Type: application/x-www-form-urlencoded
Content-Length: [calculated_length]

username=john_doe&email=john.doe%40example.com&message=Hello%20World!%20This%20is%20a%20test.
```

注意看body部分, body中用k1=v1&k2=v2这种格式组织了很多键值对, 这就是我们说的表单的格式

- **Content-Type: multipart/form-data; boundary=----xxxx**

这是另一种表单的组织方式, 和上一种的区别在于,这种表单具有发送文件的功能. 看看http报文:

```
POST YOUR_SERVER_ENDPOINT HTTP/1.1
Host: yourdomain.com
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Length: [calculated_length]

----WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="username"

john_doe
----WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="email"

<EMAIL>
----WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="message"

Hello World! This is a test.
----WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="profile_upload"; filename="profile.txt"
Content-Type: text/plain

This is my profile.
----WebKitFormBoundary7MA4YWxkTrZu0gW--
```

这种请求和上面的区别就是

1. 使用了boundary, 隔离了不同的表单的项目里面的内容 (看上去复杂一些

**总结**

不论如何,以上就是http中最常见的, 涵盖绝大多数表单发送的请求的格式. 并且, 现代的很多网站中的一些请求本身并不是从表单组件发送出来的,但是依然也利用了url-encoded这种格式的表单请求.这是很常见的做法. 以前我一开始不明白为什么这种很常见的url-encoded的请求为什么要叫application/x-www-form-urlencoded,就是因为他一开始来自于表单的请求,后来逐渐应用于其他场景.


### 2.2.2 那么, fastapi如何处理表单数据?

easy, 聪明的你应该能想到, fastapi估计也定义了form类用来处理form. yes. 让我们看看:

```python
from fastapi import From

@app.post("/login/")
async def login(username: Annotated[str, Form()], password: Annotated[str, Form()]):
    return {"message": f"your username is {username}, and password is {password}"}

```

这就是form的写法了. 很理所当然吧? 注解中就写fastapi定义的From(),这样的话框架就知道这个变量是一个表单中的字段.


> [!tips] 奇怪的地方
> 有一点问题, 不知道为什么, 在jupyter中无法运行这个代码, 会报bad request, 但是写到文件中就没事.
> 
> INFO: 127.0.0.1:51444 - "POST [/login/](https://file+.vscode-resource.vscode-cdn.net/login/) HTTP/1.1" 400 Bad Request

让我们看看例子:

#### *******. 发送urlencoded

```bash
>curl -X POST http://127.0.0.1:8009/login/ \                                     -- INSERT --
     -d "username=admin&password=xx" --trace-ascii -

Note: Unnecessary use of -X or --request, POST is already inferred.
== Info:   Trying 127.0.0.1:8009...
== Info: Connected to 127.0.0.1 (127.0.0.1) port 8009
== Info: using HTTP/1.x
=> Send header, 154 bytes (0x9a)
0000: POST /login/ HTTP/1.1
0017: Host: 127.0.0.1:8009
002d: User-Agent: curl/8.13.0
0046: Accept: */*
0053: Content-Length: 26
0067: Content-Type: application/x-www-form-urlencoded
0098: 
=> Send data, 26 bytes (0x1a)
0000: username=admin&password=xx
== Info: upload completely sent off: 26 bytes
<= Recv header, 17 bytes (0x11)
0000: HTTP/1.1 200 OK
<= Recv header, 37 bytes (0x25)
0000: date: Mon, 26 May 2025 03:23:16 GMT
<= Recv header, 17 bytes (0x11)
0000: server: uvicorn
<= Recv header, 20 bytes (0x14)
0000: content-length: 56
<= Recv header, 32 bytes (0x20)
0000: content-type: application/json
<= Recv header, 2 bytes (0x2)
0000: 
<= Recv data, 56 bytes (0x38)
0000: {"message":"your username is admin, and password is xx"}
{"message":"your username is admin, and password is xx"}== Info: Connection #0 to host 127.0.0.1 left intact
```

注意几个地方. 我返送的contenttype是什么?就是urlencoded. 成功接收到内容了吗?接收到了. 接收到的返回的content-type是appliction/json.

#### ******* 发送multipart

```bash
>curl -X POST http://127.0.0.1:8009/login/ \                                     -- INSERT --
     -F "username=admin" -F 'password=xx' --trace-ascii -
Note: Unnecessary use of -X or --request, POST is already inferred.
== Info:   Trying 127.0.0.1:8009...
== Info: Connected to 127.0.0.1 (127.0.0.1) port 8009
== Info: using HTTP/1.x
=> Send header, 198 bytes (0xc6)
0000: POST /login/ HTTP/1.1
0017: Host: 127.0.0.1:8009
002d: User-Agent: curl/8.13.0
0046: Accept: */*
0053: Content-Length: 265
0068: Content-Type: multipart/form-data; boundary=--------------------
00a8: ----02c5bTPMtCJ5vciee0KY87
00c4: 
=> Send data, 265 bytes (0x109)
0000: --------------------------02c5bTPMtCJ5vciee0KY87
0032: Content-Disposition: form-data; name="username"
0063: 
0065: admin
006c: --------------------------02c5bTPMtCJ5vciee0KY87
009e: Content-Disposition: form-data; name="password"
00cf: 
00d1: xx
00d5: --------------------------02c5bTPMtCJ5vciee0KY87--
== Info: upload completely sent off: 265 bytes
<= Recv header, 17 bytes (0x11)
0000: HTTP/1.1 200 OK
<= Recv header, 37 bytes (0x25)
0000: date: Mon, 26 May 2025 03:32:41 GMT
<= Recv header, 17 bytes (0x11)
0000: server: uvicorn
<= Recv header, 20 bytes (0x14)
0000: content-length: 56
<= Recv header, 32 bytes (0x20)
0000: content-type: application/json
<= Recv header, 2 bytes (0x2)
0000: 
<= Recv data, 56 bytes (0x38)
0000: {"message":"your username is admin, and password is xx"}
{"message":"your username is admin, and password is xx"}== Info: Connection #0 to host 127.0.0.1 left intact
```

ok啊, 我们看到, 及时发送multipart/form-data, fast这个接口也是成功处理了. 

让我们稍微花哨一点. 发送一个文件, 但是fastapi中没有写这个文件的处理逻辑. 看看 fastapi如何处理这种情况.

文件名字就叫test.file, 内容为:

```
1
2
3
4
5

```

命令为
```bash
>curl -X POST http://127.0.0.1:8009/login/ \                                     -- INSERT --
     -F "username=admin" -F 'password=xx' --trace-ascii - -F 'file=@./test.file'
Note: Unnecessary use of -X or --request, POST is already inferred.
== Info:   Trying 127.0.0.1:8009...
== Info: Connected to 127.0.0.1 (127.0.0.1) port 8009
== Info: using HTTP/1.x
=> Send header, 198 bytes (0xc6)
0000: POST /login/ HTTP/1.1
0017: Host: 127.0.0.1:8009
002d: User-Agent: curl/8.13.0
0046: Accept: */*
0053: Content-Length: 437
0068: Content-Type: multipart/form-data; boundary=--------------------
00a8: ----3ZUZ0r4LOhfjr1pi9otf6F
00c4: 
=> Send data, 437 bytes (0x1b5)
0000: --------------------------3ZUZ0r4LOhfjr1pi9otf6F
0032: Content-Disposition: form-data; name="username"
0063: 
0065: admin
006c: --------------------------3ZUZ0r4LOhfjr1pi9otf6F
009e: Content-Disposition: form-data; name="password"
00cf: 
00d1: xx
00d5: --------------------------3ZUZ0r4LOhfjr1pi9otf6F
0107: Content-Disposition: form-data; name="file"; filename="test.file
0147: "
014a: Content-Type: application/octet-stream
0172: 
0174: *******.5..
0181: --------------------------3ZUZ0r4LOhfjr1pi9otf6F--
== Info: upload completely sent off: 437 bytes
<= Recv header, 17 bytes (0x11)
0000: HTTP/1.1 200 OK
<= Recv header, 37 bytes (0x25)
0000: date: Mon, 26 May 2025 03:34:57 GMT
<= Recv header, 17 bytes (0x11)
0000: server: uvicorn
<= Recv header, 20 bytes (0x14)
0000: content-length: 56
<= Recv header, 32 bytes (0x20)
0000: content-type: application/json
<= Recv header, 2 bytes (0x2)
0000: 
<= Recv data, 56 bytes (0x38)
0000: {"message":"your username is admin, and password is xx"}
{"message":"your username is admin, and password is xx"}== Info: Connection #0 to host 127.0.0.1 left intact
```

ok啊, 所以fastapi的处理逻辑是:

如果有多余的文件之类的, fastapi并不会处理,也不会报错. 他只会提取有效的, 在端点函数中定义的部分, 也就是username和password字段. 

可以仔细观察一下http报文的样子哦.
## 2.3 上传文件

我们刚才提到了, multipart/form具有上传表单的功能.实际上,基本上所有的上传文件的方法都是使用了这种哦国内方式上传文件.

刚才*******其实已经展示了 curl命令如何上传文件. 可以仔细观察一下, 上传文件的时候, 文件的二进制内容就附加在某个boundary中.

```bash
>curl -X POST http://127.0.0.1:8009/login/ \                                     

...

0068: Content-Type: multipart/form-data; boundary=--------------------

...

00d5: --------------------------3ZUZ0r4LOhfjr1pi9otf6F
0107: Content-Disposition: form-data; name="file"; filename="test.file
0147: "
014a: Content-Type: application/octet-stream
0172: 
0174: *******.5..
0181: --------------------------3ZUZ0r4LOhfjr1pi9otf6F--

...


```

ok, 那么问题来了, fastapi如何处理上传的文件? 哈哈哈 想必你也是猜到了, fastapi中有File()来处理这种情况. 但是实际上, 情况还要复杂一些. fastapi定义了 File()和UploadFile()两种类

### 2.3.1 使用File()

这个就是这样写. 你应该能猜到..

```python
from fastapi import FastAPI

@app.post("/files/")
async def create_file(file: Annotated[bytes, File()]):
    return {"file_size": len(file)}
```

对应的请求是:

```bash
>curl -X POST http://127.0.0.1:8009/files/ \                                     -- NORMAL --
      --trace-ascii - -F 'file=@./test.file'                                    
Note: Unnecessary use of -X or --request, POST is already inferred.
== Info:   Trying 127.0.0.1:8009...
== Info: Connected to 127.0.0.1 (127.0.0.1) port 8009
== Info: using HTTP/1.x
=> Send header, 198 bytes (0xc6)
0000: POST /files/ HTTP/1.1
0017: Host: 127.0.0.1:8009
002d: User-Agent: curl/8.13.0
0046: Accept: */*
0053: Content-Length: 224
0068: Content-Type: multipart/form-data; boundary=--------------------
00a8: ----tswG4ph9CESaFXoT8yuajq
00c4: 
=> Send data, 224 bytes (0xe0)
0000: --------------------------tswG4ph9CESaFXoT8yuajq
0032: Content-Disposition: form-data; name="file"; filename="test.file
0072: "
0075: Content-Type: application/octet-stream
009d: 
009f: *******.5..
00ac: --------------------------tswG4ph9CESaFXoT8yuajq--
== Info: upload completely sent off: 224 bytes
<= Recv header, 17 bytes (0x11)
0000: HTTP/1.1 200 OK
<= Recv header, 37 bytes (0x25)
0000: date: Mon, 26 May 2025 03:49:19 GMT
<= Recv header, 17 bytes (0x11)
0000: server: uvicorn
<= Recv header, 20 bytes (0x14)
0000: content-length: 16
<= Recv header, 32 bytes (0x20)
0000: content-type: application/json
<= Recv header, 2 bytes (0x2)
0000: 
<= Recv data, 16 bytes (0x10)
0000: {"file_size":11}
{"file_size":11}== Info: Connection #0 to host 127.0.0.1 left intact
```

ok啊 没问题. 很符合我们学习到的内容:

1. curl使用-F + @ 发送文件
2. fastapi中, 如果直接return 一个dict 那么他最终是返回了一个appliction/json
3. 这个例子中 fastapi自动解析上传的文件,对应到参数file上. 返回了file的size. 文件中是5个数字,6个换行(最后是两次换行.)

我们试试花哨的. 我们再发送一点端点函数中没有定义的参数试试. 我再附带两个username和password试试.根据我们的经验, fastapi应该会忽略端点函数中没有定义的参数, 只解析已经定义的参数.

```bash
>curl -X POST http://127.0.0.1:8009/files/ \                                     -- INSERT --
     -F "username=admin" -F 'password=xx' --trace-ascii - -F 'file=@./test.file'
Note: Unnecessary use of -X or --request, POST is already inferred.
== Info:   Trying 127.0.0.1:8009...
== Info: Connected to 127.0.0.1 (127.0.0.1) port 8009
== Info: using HTTP/1.x
=> Send header, 198 bytes (0xc6)
0000: POST /files/ HTTP/1.1
0017: Host: 127.0.0.1:8009
002d: User-Agent: curl/8.13.0
0046: Accept: */*
0053: Content-Length: 437
0068: Content-Type: multipart/form-data; boundary=--------------------
00a8: ----pJ2Rw2NZeUNNh43MIhtGqv
00c4: 
=> Send data, 437 bytes (0x1b5)
0000: --------------------------pJ2Rw2NZeUNNh43MIhtGqv
0032: Content-Disposition: form-data; name="username"
0063: 
0065: admin
006c: --------------------------pJ2Rw2NZeUNNh43MIhtGqv
009e: Content-Disposition: form-data; name="password"
00cf: 
00d1: xx
00d5: --------------------------pJ2Rw2NZeUNNh43MIhtGqv
0107: Content-Disposition: form-data; name="file"; filename="test.file
0147: "
014a: Content-Type: application/octet-stream
0172: 
0174: *******.5..
0181: --------------------------pJ2Rw2NZeUNNh43MIhtGqv--
== Info: upload completely sent off: 437 bytes
<= Recv header, 17 bytes (0x11)
0000: HTTP/1.1 200 OK
<= Recv header, 37 bytes (0x25)
0000: date: Mon, 26 May 2025 03:48:26 GMT
<= Recv header, 17 bytes (0x11)
0000: server: uvicorn
<= Recv header, 20 bytes (0x14)
0000: content-length: 16
<= Recv header, 32 bytes (0x20)
0000: content-type: application/json
<= Recv header, 2 bytes (0x2)
0000: 
<= Recv data, 16 bytes (0x10)
0000: {"file_size":11}
```
嗨呀 没问题.

File就是这样了. 那么, 我们最开始提到的UploadFile是什么? 实际上, File在接受到之后, 存在了内存中. 但是有的时候你的文件可能很大, 所以我们希望可以文件如果过大了就储存到硬盘上. 这就是UploadFile. 这是一个当上传的文件超出限制之后自动存储到磁盘上的类. 并且,他的写法也有点不同.

	### 2.3.2 使用UploadFile

UploadFile是一个类, 是一种type, 所以他的写法是:

```python
from fastapi import UploadFile

@app.post("/uploadfile")
async def upload_file(file: UploadFile):
    return {"file_size": file.size}
```

这里的file的类型不再是bytes, 而是UploadFile. 测试如下:

```bash
>curl -X POST http://127.0.0.1:8009/uploadfile \                                 -- NORMAL --
      --trace-ascii - -F 'file=@./test.file'
Note: Unnecessary use of -X or --request, POST is already inferred.
== Info:   Trying 127.0.0.1:8009...
== Info: Connected to 127.0.0.1 (127.0.0.1) port 8009
== Info: using HTTP/1.x
=> Send header, 202 bytes (0xca)
0000: POST /uploadfile HTTP/1.1
001b: Host: 127.0.0.1:8009
0031: User-Agent: curl/8.13.0
004a: Accept: */*
0057: Content-Length: 224
006c: Content-Type: multipart/form-data; boundary=--------------------
00ac: ----Zt1wkB6WuSWWoctMG2dL9z
00c8: 
=> Send data, 224 bytes (0xe0)
0000: --------------------------Zt1wkB6WuSWWoctMG2dL9z
0032: Content-Disposition: form-data; name="file"; filename="test.file
0072: "
0075: Content-Type: application/octet-stream
009d: 
009f: *******.5..
00ac: --------------------------Zt1wkB6WuSWWoctMG2dL9z--
== Info: upload completely sent off: 224 bytes
<= Recv header, 17 bytes (0x11)
0000: HTTP/1.1 200 OK
<= Recv header, 37 bytes (0x25)
0000: date: Mon, 26 May 2025 04:05:00 GMT
<= Recv header, 17 bytes (0x11)
0000: server: uvicorn
<= Recv header, 20 bytes (0x14)
0000: content-length: 16
<= Recv header, 32 bytes (0x20)
0000: content-type: application/json
<= Recv header, 2 bytes (0x2)
0000: 
<= Recv data, 16 bytes (0x10)
0000: {"file_size":11}
{"file_size":11}== Info: Connection #0 to host 127.0.0.1 left intact
```

ok啊 uploadfile作为一个特殊的type, 他有一些自带的方法. 很方便.比如,我们可以使用upload的一些自带方法:

```python
@app.post("/uploadfile")
async def upload_file(file: UploadFile):
    content = await file.read()
    return {
        "file_size": file.size,
        "file_name": file.filename,
        "conent_type": file.content_type,
        "content": content,
    }
```

请求&结果:

```bash
>curl -X POST http://127.0.0.1:8009/uploadfile \                                 -- INSERT --
      --trace-ascii - -F 'file=@./test.file'    
Note: Unnecessary use of -X or --request, POST is already inferred.
== Info:   Trying 127.0.0.1:8009...
== Info: Connected to 127.0.0.1 (127.0.0.1) port 8009
== Info: using HTTP/1.x
=> Send header, 202 bytes (0xca)
0000: POST /uploadfile HTTP/1.1
001b: Host: 127.0.0.1:8009
0031: User-Agent: curl/8.13.0
004a: Accept: */*
0057: Content-Length: 224
006c: Content-Type: multipart/form-data; boundary=--------------------
00ac: ----fkvs20r8vcXC0vc3Zo7uU8
00c8: 
=> Send data, 224 bytes (0xe0)
0000: --------------------------fkvs20r8vcXC0vc3Zo7uU8
0032: Content-Disposition: form-data; name="file"; filename="test.file
0072: "
0075: Content-Type: application/octet-stream
009d: 
009f: *******.5..
00ac: --------------------------fkvs20r8vcXC0vc3Zo7uU8--
== Info: upload completely sent off: 224 bytes
<= Recv header, 17 bytes (0x11)
0000: HTTP/1.1 200 OK
<= Recv header, 37 bytes (0x25)
0000: date: Mon, 26 May 2025 04:10:32 GMT
<= Recv header, 17 bytes (0x11)
0000: server: uvicorn
<= Recv header, 21 bytes (0x15)
0000: content-length: 111
<= Recv header, 32 bytes (0x20)
0000: content-type: application/json
<= Recv header, 2 bytes (0x2)
0000: 
<= Recv data, 111 bytes (0x6f)
0000: {"file_size":11,"file_name":"test.file","conent_type":"applicati
0040: on/octet-stream","content":"1\n2\n3\n4\n5\n\n"}
{"file_size":11,"file_name":"test.file","conent_type":"application/octet-stream","content":"1\n2\n3\n4\n5\n\n"}== Info: Connection #0 to host 127.0.0.1 left intact
```

来, 看看有什么有趣的.

1. 成功收到了相关信息.注意,contenttype是application/octet-stream, 这是文件本身的content-type,而不是发送的请求的content-type. 也就是这里的content-type: 

![[Pasted image 20250526121226.png]]

2.filesize, filename, contenttype, content都没问题.都是一一对应的文件的属性. 

ok啊, 在此基础上, uploadfile还可以:

1. 设为optional arg. 也就是可以有默认值, 也就是说可以不上传文件.
2. 可以通过在端点函数中声明 `file:list[UploadFile]`来实现对上传多个的文件的支持.
3. `files: Annotated[list[UploadFile], File(description="Multiple files as UploadFile")]` 也是可行的. 也就是, 可以用file给uploadfile添加metadata, 甚至可以给`list[UploadFile]`添加metadata



# 3. Headers

headers就是请求时候的headers，懂得都懂。fastapi如何处理headers中的header呢？很经典，它定义了一个Header类。用的话这么用

```python
from fastapi import FastAPI, Header

app = FastAPI()

@app.get("/items/")
async def read_items(user_agent: Annotated[str | None, Header()] = None):
    return {"User-Agent": user_agent}
```

期望的输入是：

```bash
curl 'http://0.0.0.0:8009/items/' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'accept: application/json' \
```

注意：这里定义的时候，user_agent即对应headers中的User-Agent. 由于python不支持变量名中带有-，于是你应该定义成下划线，fastapi会自动转换。大小写是随意的，推荐小写，符合python snake_case
# 4. Cookie

Cookie是headers中的其中一个，其value一般是一个/多个键值对， e.g. `Cookie: k1=v1&k2=v2`。fastapi如何处理呢？很经典，fastapi定义了一个Cookie类可以做处理。

```python
from fastapi import Cookie, FastAPI

app = FastAPI()

@app.get("/items/")
async def read_items(ads_id: Annotated[str | None, Cookie()]):
    return {"ads_id": ads_id}
```

期望的输入是：

```bash
curl -X 'GET' \
  'http://0.0.0.0:8009/items/' \
  -H 'accept: application/json' \
  -H 'Cookie: ads_id=1'
```
# 5. 响应

当我们讨论响应的时候, 其实讨论的就是下面这一坨:

```
http/1.1 200 ok
header1:xx
hearder2:xx

body-cotent
```

所以, 我们本质上还是在讨论这几个部分: 首行(包括版本,状态码,消息), headers, body

让我们看看fastapi在这一块干了什么吧.

## 5.1 响应体

响应体有很多种, **我们依然还是讨论是json的情况**. 也就是content-type是appliction/json. 对于json,可以有很多种形式,比如dict,list,或者str int float None bool.想必你也想到了,既然请求中的请求体用了pydantic或者普通的数据类型, 那么, 响应中的response应该也是如此:

1. 可以没有约束
2. 也可以用pydantic做dict,list类型的控制.
 
 让我们看看fastapi怎么做的.let's dive right in!
### 5.1.1 可以使用pydantic定义响应体

一个普通的请求体,其实你可以直接返回任意数据类型, 可以不做额外的操作, fastapi会自动处理, 但是fastapi推荐使用pydantic定义你的返回响应体类型.

fastapi可以让你定义pydantic model,来约束response body. pydantic对象就对应了appliction/json这种类型中的普通的json object,也就是dict. 写法上 like this:

```python
class UserIn(BaseModel):
    username: str
    password: str
    email: EmailStr
    full_name: str | None = None


class UserOut(BaseModel):
    username: str
    email: EmailStr
    full_name: str | None = None


@app.post("/user/", response_model=UserOut)
async def create_user(user: UserIn):
    return user

```


> [!Question] 为什么不把数据类型放到函数声明的返回值中?
> 个人觉得是因为, 那样写意味着python类型提示还是希望返回值类型是那个model. 也就是说我们需要自己做类型转换,把类型转换为那个类型.但是:
> 
>1. 让我们手动在函数内把数据转化为model, 好像确实也不太优雅. 有点违反了单一职责的原则. 
>2. 或许fastapi就是在两种差不多设计中选了一种...?

list类型就是声明为list类型就行了. 当然你也可以在list中声明数据类型, 比如一个pydantic的model

```python
@app.get("/items/", response_model=list[Item])
async def read_items() -> Any:
    return [
        {"name": "Portal Gun", "price": 42.0},
        {"name": "Plumbus", "price": 32.0},
    ]
```

哈哈,很简单吧? 也是声明model, 然后放在装饰器的参数里面就行了. 对于str, int等其他类型, 教程暂时没有写.


> [!NOTE] 使用pydantic的好处...
> 使用pydantic定义返回类型并不是必须的, 你完全可以缺省这部分. 但是这样定义返回的数据的结构, 可以让pydantic帮你做验证, 甚至也能自动生成自动化的文档.


在此基础上, 还可以:

#### ******* 给响应体加入默认值

```python
class Item(BaseModel):
    name: str
    description: Union[str, None] = None
    price: float
    tax: float = 10.5
    tags: List[str] = []


items = {
    "foo": {"name": "Foo", "price": 50.2},
    "bar": {"name": "Bar", "description": "The bartenders", "price": 62, "tax": 20.2},
    "baz": {"name": "Baz", "description": None, "price": 50.2, "tax": 10.5, "tags": []},
}


@app.get("/items/{item_id}", response_model=Item, response_model_exclude_unset=True)
async def read_item(item_id: str):
    return items[item_id]
```

#### ******* 使用 response_model_exclude_unset 参数

```python
from typing import List, Union

from fastapi import FastAPI
from pydantic import BaseModel

app = FastAPI()


class Item(BaseModel):
    name: str
    description: Union[str, None] = None
    price: float
    tax: float = 10.5
    tags: List[str] = []


items = {
    "foo": {"name": "Foo", "price": 50.2},
    "bar": {"name": "Bar", "description": "The bartenders", "price": 62, "tax": 20.2},
    "baz": {"name": "Baz", "description": None, "price": 50.2, "tax": 10.5, "tags": []},
}


@app.get("/items/{item_id}", response_model=Item, response_model_exclude_unset=True)
async def read_item(item_id: str):
    return items[item_id]
```

然后响应中将不会包含那些默认值，而是仅有实际设置的值。如果实际设置的值有和默认值一样的值, 那么他是会返回这个值的.(很合理吧)


> [!NOTE] response_model_include 和 response_model_exclude
> 
> 不推荐用这俩. 应该能才出来是干啥的吧? 

#### ******* 考虑继承pydantic model

当需要用到多个类似的model的时候, 可以**考虑**继承这几个model.用法如下:

```python
class UserBase(BaseModel):
    username: str
    email: EmailStr
    full_name: str | None = None


class UserIn(UserBase):
    password: str


class UserOut(UserBase):
    pass


class UserInDB(UserBase):
    hashed_password: str


def fake_password_hasher(raw_password: str):
    return "supersecret" + raw_password


def fake_save_user(user_in: UserIn):
    hashed_password = fake_password_hasher(user_in.password)
    user_in_db = UserInDB(**user_in.dict(), hashed_password=hashed_password)
    print("User saved! ..not really")
    return user_in_db


@app.post("/user/", response_model=UserOut)
async def create_user(user_in: UserIn):
    user_saved = fake_save_user(user_in)
    return user_saved
```

好处就是不用多次更改相同的公共部分.

## 5.2 响应码

对于fastapi, 默认函数执行完了就会返回200的状态码. 但是有的时候会返回不同的状态码.so,如何优雅地使用状态码?

废话不多说,最简单的:

```python
@app.post("/items/", status_code=201)
async def create_item(name: str):
    return {"name": name}
```

优雅的方法:

```python
from fastapi import FastAPI, status

app = FastAPI()


@app.post("/items/", status_code=status.HTTP_201_CREATED)
async def create_item(name: str):
    return {"name": name}
```


> [!NOTE] 技术细节
> 也可以使用 from starlette import status。
> 
> 为了让开发者更方便，FastAPI 提供了与 starlette.status 完全相同的 fastapi.status。但它直接来自于 Starlette。


# 6. 依赖注入


依赖注入是什么...? 其本质就是, fastapi可以通过特殊的语法将一组可以重复利用的代码剥离出来, 简化path operation func的写法. 比如, 你这个程序所有的接口都要做鉴权操作, 那么每个接口理论上都要验证token, 但是似乎...每个路径操作函数中都写上一段验证逻辑,或者封装一下每次都调用一次鉴权的函数,fastapi都觉得不算优雅.(其实就我而言 感觉貌似还算可以, 不算很丑陋?)  fastapi 对此做了原生的支持.