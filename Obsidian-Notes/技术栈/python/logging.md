普通的logging用法就不多说了. easy的场景我建议用loguru, 大型项目我建议用下面的方法

[Modern Python logging - YouTube](https://www.youtube.com/watch?v=9L77QExPmI0&t=45s&ab_channel=mCoding)

---
# 0. how logging works

![[Pasted image 20250610222622.png]]


- each logging.info() or sth like creates a obj including all kinds of useful info. things like serverity, the current time, current thread, async task,  the location in the source code, and so on.
- 每一个logger都可以设置一个level, 这样在产生日志的时候会过滤低级别日志.
- 每一个logger都可以设置多个(应该是吧)filter, 可以用来丢弃或者改变消息.(比如过滤特殊字段等等.)
- handler
	- 每个logger都可以绑定多个handler, which tell you how and where to log a record. like to stdout, to a file, over email, or to a commerical log service(我理解类似elk之类的?)
		- 从上图可以看出来, 一个logrecord, 会经过所有的handler, 即使有的handler的level比较高没有输出log record. 但是如果被filtered, 就没有输出的机会了. 
	- 每个handler都有自己的filter**s**, fotmatter
	- formatter 可以设置格式, 比如时间戳之类的,这就不用多说了吧.
- logger tree.
![[Pasted image 20250610224036.png]]

接下来是一段废话知识.

实际上存在名为logger tree的东西. 通过名字带点创建logger, 你就能得到一个loggertree. 注意最上面是rootlogger, 默认存在的. 当你logger的时候,子logger的消息处理完了会向上传递给parent logger, 最终一直传递到root logger.这个过程称之为传播.

如果传播的过程中, 被某个logger的filter(不是handler的filter)过滤掉了, 那么传播就到此为止了.

为什么说废话呢?因为作者推荐这样:

![[Pasted image 20250610224531.png]]

也就是所有的子logger都不需要绑定handler, 设置filter, 统统交给rootlogger来处理handler的问题.

推荐:

- 不使用rootlogger(也就是logging.info()这种), 而是使用自己定义的logger.
- 不必每个文件都get一个logger. 

### 1. using dictConfig


一个简单的配置如下

```python
logging_config={
    "version":1,
    "disable_existing_loggers": False,# 禁用除了此config之外的所有logger
    "filters":{},
    "formatters":{
        "simple":{
            "format":"%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        }
    },
    "handlers":{
        "stdout":{
            "class":"logging.StreamHandler",
            "formatter":"simple",
			
            "stream":"ext://sys.stdout"
        }
    },
    "loggers":{
        "root":{"level":"DEBUG","handlers":["stdout"]}
    },
}
```

关于format的格式化字符串怎么写, 文档里有. 但是我感觉还是找几个好用的自己收藏一下就好了.
[logging — Logging facility for Python — Python 3.13.4 documentation](https://docs.python.org/3/library/logging.html)

ps

- 直接使用dictconfig意味着需要把config直接定义在代码内. 你可以定义在json中然后load进来.

## 1.3 异步输出日志

![[Pasted image 20250611164753.png]]