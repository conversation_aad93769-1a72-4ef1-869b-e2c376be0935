
- `poetry init` 初始化，生成pyproject.toml  `uv init`
	- `poetry env use pythonx.x` 创建虚拟环境  `uv venv -p python3.12`或者`uv venv -p 3.12`
	- poetry创建的虚拟环境默认放在一个刁钻的位置，我们把它换到项目目录下 uv默认放在当前目录
- `poetry shell`进入虚拟环境 这个平时也不用,uv也不咋用
	- 他好像就是开了一个shell，使用exit即可推出
- `poetry add x`就是pip install。判断是否有虚拟环境；写入pyproject；根据pyproject写入poetry.lock；根据poetry.lock更新虚拟环境 `uv add xxx`
	- 虚拟环境的更新依赖于poetry.lock, not pyproject
- `poetry lock`根据pyproject更新poetry.lock uv貌似没有这个
	- 还是那句话，虚拟环境的更新依赖于poetry.lock, not pyproject
- `poetry install`根据poetry.lock更新虚拟环境
- `poetry add x --group dev`将x安装到dev组中，这样可以隔离开发用到的包和发布用到的包，减小环境大小
- `poetry update`更新所有可以更新的模块，或者`poetry update x`仅更新x。更新规则受pyproject的限值
- `poetry show`显示poetry.lock中的包，也就是你安装的包。使用pip安装的包不会被显示。（所以不要在poetry项目中使用pip,是吗？）
	- `poetry show --tree`显示依赖关系
- `poetry remove x`对应pip uninstall，但是会顺便移除可以移除的依赖
- `poetry export -f requirements.txt -o requirements.txt --without-hashes --dev`输出requiremets.txt
	- 使用poetry就不能用pip freeze导出了哦

anyway,总结如下:

uv init -p 3.12
uv venv -p python3.12  // 貌似用不到,直接add就完了.
uv add x
uv remove x
uv 

### 后记

实际上,我也不需要什么花里胡哨的功能.我只需要:

1. 能创建一个基本的python项目结构 
2. 智能地管理包的依赖的安装,移除
3. 安装requirememts.txt
4. pip install -e . 

冷门一点的:

1. 分group安装
2. 输出requirements.txt

以上内容,这几个命令就行了:

1. uv init -p 3.12
2. uv add x & uv remove x
3. uv add -r requirements.txt
4. uv pip install -e . //应该是吧

5. uv add x --group dev

