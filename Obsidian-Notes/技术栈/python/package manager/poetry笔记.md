
# what can poetry do?

最方便的地方：
1. 卸载package的时候安装也会卸载依赖
2. 更新库的时候也好更新？
3. 好看
还有一些没有那么重要的：
1. 虚拟环境管理
2. 管理打包和发布
3. 区分不同场景下的包（开发，测试）

不好的地方：
虚拟环境

# 命令列表

- `poetry init` 初始化，生成pyproject.toml
- `poetry env use pythonx.x` 创建虚拟环境
	- poetry创建的虚拟环境默认放在一个刁钻的位置，我们把它换到项目目录下
- `poetry shell`进入虚拟环境
	- 他好像就是开了一个shell，使用exit即可推出
- `poetry add x`就是pip install。判断是否有虚拟环境；写入pyproject；根据pyproject写入poetry.lock；根据poetry.lock更新虚拟环境
	- 虚拟环境的更新依赖于poetry.lock, not pyproject
- `poetry lock`根据pyproject更新poetry.lock
	- 还是那句话，虚拟环境的更新依赖于poetry.lock, not pyproject
- `poetry install`根据poetry.lock更新虚拟环境
- `poetry add x --group dev`将x安装到dev组中，这样可以隔离开发用到的包和发布用到的包，减小环境大小
- `poetry update`更新所有可以更新的模块，或者`poetry update x`仅更新x。更新规则受pyproject的限值
- `poetry show`显示poetry.lock中的包，也就是你安装的包。使用pip安装的包不会被显示。（所以不要在poetry项目中使用pip,是吗？）
	- `poetry show --tree`显示依赖关系
- `poetry remove x`对应pip uninstall，但是会顺便移除可以移除的依赖
- `poetry export -f requirements.txt -o requirements.txt --without-hashes --dev`输出requiremets.txt
	- 使用poetry就不能用pip freeze导出了哦

爽！舒服！记完了！


