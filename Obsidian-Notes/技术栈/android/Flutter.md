# xx

## layout

MaterialApp, Scaffold, Center, Row, Column, Expanded, Align, and Text
### Widget: MaterialApp

MaterialApp()这个widget不是一个具体对应到界面上的组件的widget，他是一个抽象的widget，用来定义app的名称，主题等。还有定义路由等功能，这里不展开，其他的知识感觉都是高级用法了。

在MaterialApp() 的quick document中，MaterialApp是这么被描述的：

```text
Creates a MaterialApp.
At least one of home, routes, onGenerateRoute, or builder must be non-null. If only routes is given, it must include an entry for the Navigator. defaultRouteName (/), since that is the route used when the application is launched with an intent that specifies an otherwise unsupported route.
This class creates an instance of WidgetsApp.
```

A key point in this text is, you have to pass the home argument or the three other ones. the others is not commonly used, so, remember to pass the `home` parameter.

```dart
class MyApp extends StatelessWidget {

	@override
	Widget build()=>MaterialApp(
	title : 'my app title',
	home :  
	)
}
```

现阶段，感觉只要记住`home`参数是必要的就好了，其他的都不知道是什么意思。

## Widget: Scaffold

脚手架， 不想写废话了。很熟悉了已经


### Container

非常万能。注意，如果container初始化的时候没有指定大小也没有子元素，默认会选择尽可能大的尺寸，如果有子元素，则会与子元素保持一致。
## ConstrainedBox FittedBox RotatedBox

ConstrainedBox: 限制box的大小
FittedBox：根据策略缩放子组件的空间
RotatedBox：旋转子组件，并适应其大小
SizedBox：明确设定box大小

## StatefulWidget

statefulwidget就是自己保存了一些状态的组件。状态的更改会引起小组间的重新渲染。

比如，我写一个page小组件，让他显示一些会变化的内容，比如一个随着我的点击要改变显示的内容的组件，这个时候就经常用到stateful组件。

但是这种和provider的效果好像差不多？

没错。但是还有有区别的。使用provider可以把状态的变化通知多个widget，但是statefulwidget不会在状态变化的时候通知别的组件，只会自己默默地重新渲染一下。