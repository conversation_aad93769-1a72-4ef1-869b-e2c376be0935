# Dart数据类型
## 声明

声明的方式可以分成四种：

var x;
dymamic x;
*tpye* x;
Object x;

如果是var,则会在第一次赋值的时候判断类型。如果是dynamic,则为可变类型，可以赋值各种类型。*type* x 的意思就是声明的时候就指定类型。Object有点特殊，回头再说。

		dart在声明的的时候可以直接指定类型，比如 String, List 这种，也可以用 var， dynamic这种，让程序自动判断类型。但是对于var和dynamic, 变量最终的类型都是几种基本的类型中的一种，（int String之类的），也就是说，var 和 dynamic并不像是指定变量类型的关键字，更像是不指定类型的一种特定语法。Null也是一种类型，并且只有null一个实例。

## 未定义变量 


			对于没定义的变量，情况分一下几种。
	
	1. 是否是var或者dynamic？如果是，则默认为null
	2. 如果不是，那么他是局部变量（函数内定义）还是全局变量或类中的变量？如果是全局或者类的变量，则默认为null	
	3. 如果是局部变量，那么要注意。这个时候他的类型比较特殊。如果你尝试获取他的类型，会提示报错。因为对于局部变量而言，必须要赋值，然后才能使用。
	
	Dart 中的 `var` 和 `dynamic` 都允许未初始化的变量默认被赋值为 `null`, 不论是局部变量还是全局变量，类中的变量。对于值null, 其类型为Null. Null只有一个实例null.


以上说的，在dart2.12之后改变了。2.12引入了null safety机制，which means 声明变量类型的时候(使用var 和 dynamic除外)必须说明是否可以为空（type? x）才能用默认空值，否则一定要赋值才能使用。

也就是说，对于表达式 String x; 即便是类的变量或者全局变量，也不能直接使用了。

但是还是有两个例外。var 和 dynamic. 因为他们严格意义上并不是一种类型，更像是一种特殊语法。也就是说，如果你使用var和dynamic声明，并且没有使用？，然后并不赋值，也没事，依然可以使用。

一言以蔽之，就是使用dynamic和var的时候可以使用默认的null值，但是其他类型不可以，除非用？显式声明。

## 字符串

字符串就是经典字符串啦。

定义或者声明字符串使用 String 关键字。比如String x=‘1’ 或者 String y =“amazing！” 。相信你也看到了，单引号和双引号都可以。

字符串也有类似python中的f-string的操作，也就是通过特殊的语法可以在字符串中包括变量名或者表达式。那就是：${} 。比如:
```dart
var x = 1;
print("x is ${x}");
```
输出就是 x is 1。 当{}中是一个标识符的时候(也就是变量名), 你也可以忽略花括号：

```dart
var x = 1;
print("x equals $x");
```

就像如果你不慎用错了\$，比如print(’x equals $1‘)的时候所报的错：

```
A '$' has special meaning inside a string, and must be followed by an identifier or an expression in curly braces ({}).
```

这里我想试试$后面可不可以不带{}就跟一个表达式，于是报错了。

### 字符串拼接

最简单的拼接就是直接使用+。比如‘asdf’ + 'ffff'，结果就是'asdfffff'。另一个，对于字符串常量，也就是字面量，也就是直接写出来的字符串而不是字符串变量，可以直接相邻着放在一起，这样也没问题。

```dart
String str1 = 'hello,';
String str2 = ' jack';
print(str1+str2);
print('hello' 'mike');
//print(str1 str2); //无效的用法
```

## 数字

两种类型，int和double. int类型大小为-2^63 ~ 2^63 -1 。double就是那个那个，我也不知道多大，就是那个c语言的经典储存方法。小数点可以移动的那种。

```dart
num a = 1;
num b = 5.5;
int c = 4;
double d = 6.6;

//以上定义都没问题。num是int和double的父类，因此用num定义也是ok的。（你甚至可以用Object定义）

//一些其他方法：

print(a.toString()) //toString()是object类的方法，几乎所有对象都能调用


//下面看看parse方法，你一定一看就懂了。

String str1='5';
String str2='5.5';

int e = int.parse(str1);
double f = double.parse(str2);
//int g = int.parse(str2); //报错，不能转换
double h = double.parse(str1);


```
### 类型收窄与类型推断

int 和 double都是num的子类。你完全可以用num a = 5;来定义一个变量。不过只要你制定了类型（即只要没有用var 和dynamic这种需要推断类型的关键字），那么它就会认为这个变量是你指定的类型。在此编译器会认为a就是num类型，因此，产生了一个很重要的问题。

正如前面所说，所有的类型就继承了Object类，因此你可以用Object x =xx; 来定义一个变量。但是这么做的后果就是无法使用xx对应的类型的方法。 在这里也是一样。你制定了num这个类型，那么他就只能使用num类型的方法，而不能使用int类型的方法，比如isEven()。

```dart
num a =5;
print(a.isEven); //报错
```

```
main.dart:3:9: Error: The getter 'isEven' isn't defined for the class 'num'.  
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isEven'.  
print(a.isEven); //报错e
```

但是，dart有一个神奇的类型收窄机制。通过在代码中对变量进行类型检查或条件判断，Dart 编译器可以将变量的类型从一个更广泛的类型“收窄”到一个更具体的子类型。这种收窄允许在代码块中使用更多特定于子类型的方法和属性。

```dart
void main() {
  num a = 5;

  // 在这里，a 的类型是 num，所以不能直接调用 isEven
  // print(a.isEven); // 错误：num 类型没有 isEven 方法

  // 使用类型检查进行类型收窄
  if (a is int) {
    // 在这个代码块中，a 的类型被收窄为 int
    print(a.isEven); // 合法：因为 a 被判断为 int 类型，所以可以调用 isEven 方法
  }
}

```

类型收窄除了可以使用is 触发，还有is!，as等方法。这里先不说了。


## 列表

一个普通的定义是：

```dart
List lst=[1,2,3];
```

```dart
var x = lst.length; 
lst.add(4);
lst.sort((a,b)=>a.compareTo(b));
lst.removeLast();
lst.indexOf(4);
```

## 集合

```dart
Set set1=Set();
set1.add('orange');
set1.addAll(['apple','banana','chocolate']);
set1.remove('chocolate');
set1.contains('banana');
set1.containAll(['banana','apple']);
```

## Map（即字典）

```dart
//几种定义方法
		var map1={'k1':'v1','k2':'v2'};

var map2=Map();
map2['k1']='v1';
map2['k2']='v2';

var map3=Map<String,int>();
map3['k1']='v1';
map3['k2']='v2';
map3.remove('k1');
print(map3.keys);
print(map3.values);

Map map4={};
map4=map3;
map4.forEach((k,v){print('k:$k,v:$v')});
```

## Object类型

都懂的。

```dart
Object obj = some_obj;
```

这个时候，obj被认为是一个Object类型的对象，只能使用Object类的基本方法，不论后面的someobj是什么类型，你只能调用Object类型的基本方法。

		如果你把Object换成dynamic,则会跳过检查，直到运行的时候才知道方法能不能被调用。


## 枚举

感觉不会用到呢...

## is & as

is 用于类型判断，as用于类型转换。

对于is而言，一个对象obj及其子类的对象使用 is 判断是否为obj类的时候，都将返回True.

```dart
int num1=1;
if (num1 is num){
	print('num1 is num');
}
```

as用于类型转换。~~比如有有一个变量x，由于可能为truck类也有可能为taxi类，于是你定义为car类。但是有的时候你会调用car独有的方法，这个时候你就无法直接调用，因为编译不会通过。~~

	```dart
	（num num1）{
	
	//忽略上文
		num1.isEven();//会报错
	//忽略下文
	
	}
	```

~~但是你可以写~~
	```dart
	(num1 as int).isEven();
	```

憋说了，as被is薄纱，简直没有用的地方，is就完了。

## void

void有必要单独拎出来说说。

在定义函数时，void用于在定义函数的时候放在函数前面表示函数没有返回。但是在dart中，如果你没有任何返回值，你可以不在定义前加上void。并且，实际上，这种情况下，有一个隐式的return null被加在了函数的末尾。

														实际上，所有类型的函数都可以不加类型，dart会根据return自动推断。

如果你加上了void但是又尝试return一些东西，会报错，这很合理。不过如果是return null，那就没问题。return一个void类型的function也是可以的。

下面看一个有趣的程序。

```dart
class Myclass{
  void sayHi(){
    print('hi!');
    dynamic a=0;
    return a;
  }
}

void main(){
  Myclass mc = Myclass();
  var b=mc.sayHi();
  print(b);
}

```
在这里呢，Myclass有一个类型为void的成员函数。但是有趣的是，我们让他返回了一个dynamic类型的数字。dynamic我们都知道，他会让编译器跳过对他的类型检查。当编译器在尝试检查sayHi这个函数的返回值的类型的时候，发现是dynamic,于是就不报错了，即使我给他赋值为0.

但是，这样并不代表就能 ‘架空’ void。在main中的地三行，print(b)会报错。这是因为，即便你用dynamic跳过类型检查，尝试得到一个不该得到的变量，这个变量也不会让你使用。也就是说，dart不会让你使用void函数返回的值。

![[Pasted image 20240906095650.png]]

我们再试一下：
```dart
void testvoidfunc(){
  print('shit');
}

main(){
  print(testvoidfunc());
}
```
编译报错：

```
compileDDC
main.dart:6:9: Error: This expression has type 'void' and can't be used.
  print(testvoidfunc());
        ^
```
总结一下void：

1. 函数不返回东西的时候，void可以省略。
2. 实际上所有类型的函数，定义函数时的函数类型都可以省略。
3. 尝试将一个void函数赋值给一个变量不会报错，但是使用这个变量的时候会报错(compile time)。

再拓展一手：
使用泛型时，void也能用到，效果类似Object

```dart
		List<void> lst=[1,2,3,4]; // similar to List<Object> lst[1.2.3.4]
```
但是和Object类似，好像都没啥用：

```dart
main(){
  List<void> lst=[1,2,3,4];
  print('${lst[1].isEven()}');
}

```

```
compileDDC
main.dart:3:15: Error: This expression has type 'void' and can't be used.
  print('${lst[1].isEven()}');
              ^
main.dart:3:19: Error: The method 'isEven' isn't defined for the class 'void'.
Try correcting the name to the name of an existing method, or defining a method named 'isEven'.
  print('${lst[1].isEven()}');
                  ^^^^^^
```

```dart
main(){
  List<Object> lst=[1,2,3,4];
  print('${lst[1].isEven()}');
}
```

```
```
```
compileDDC
main.dart:3:19: Error: The method 'isEven' isn't defined for the class 'Object'.
 - 'Object' is from 'dart:core'.
Try correcting the name to the name of an existing method, or defining a method named 'isEven'.
  print('${lst[1].isEven()}');
                  ^^^^^^
```

尝试调用某个类型的独有的方法的时候，都会在编译时报错。void报 void cannot be used. Oject报 method xx isn't defined for the class 'Object'.


# Dart流程控制

流程控制还是循环，判断那一套，不过还是写一下熟悉一下。

## looping

```dart
void main(){
for(var i =0;i<10;i++){
	print(i);
	}

List lst=[1,2,3,4,5];
for(var element in lst){//即使是用来遍历的变量，也要定义了才能用。
	print(element);
	}
lst.forEach((i)=>print(i));

var j=0;
while(true){
	j=j+1;
	print(j);
	if(j==10)break;
	}
}

```

## switch

switch就是经典switch. switch的变量只能是int或者String.
// 好久没写switch了啊...

```dart
void main(){
var i = 1;
switch(i){
	case 1:
		print('now the number is 1');
		//do something
		break;
	case 2:
		print('now the number is 2');
		//do something
		break;
	default:
		//not 1 or 2;
		//do something
		break;
	}
}
```

还是那几个经典要注意的事项：

1. 别忘了在每个case后面加上break, 不然不会中断。
2. 设置一个default是一个不错的习惯。


## if

经典if.

```dart
void main(){
bool flag1 = true;
bool flag2 = true;
bool flag3 = false;
bool flag4 = false;

if(flag1 || flag2){
	print('at least one of flag1 and flag2 is true');
}else {
	print('flag1 and flag2 are both false');
}

if(flag1 == true){
	print('flag1 is true');
}else if (flag2 == true){
	print('flag2 is true');
}

//也可以不用==判断，直接放在（）中

if(flag1 || flag2 || flag3 || flag4){
	print('at least one of flags is true');
}
		}
```



# Dart面向对象
## 成员变量，成员函数，static

最简单的类的定义：

```dart
class myclass1 {} //直接空也没问题
class myclass2 {
	String? str1;
	String? str2;
}
```

超简单，没什么东西。

你还可以使用static关键字，表明这个变量是类的变量，不需要实例化/初始化一个对象来使用它。

```dart
class myclass {
	static String str1='this is a test string';
}

main (){
	print(myclass.str1)
}
```

再写个方法，也是没什么东西，纯属让自己熟悉一下：

```dart
class myclass {
	String welcomestring='hi!';
	welcome(){
		print(welcomestring);
	}
}

main(){
	myclass myobj=myclass();
	myobj.welcome();
}
```

简单！顺便说点别的。

很多语言中创建对象都要用关键字new，但是在dart中可以省略。也就是这样写： `myclass myobj= new myclass();`

方法依然可以是static的。

```dart
class myclass {
	static welcome(){
		print('hi!');
	}
}

main(){
	myclass.welcome();

}
```

## 构造函数 constructors

不说了，懂得都懂，直接开写。

```dart
class Hero{
	String? firstName;
	String? lastName;
	Hero(String str1,String str2){
		firstName=str1;
		lastName=str2;
	}

	sayName(){
		print('${firstName} ${lastName}');
	}
}

main(){
	Hero myHero=Hero('barry','yang');
	myHero.sayName();
}
```

ok，一遍过。要注意：1. 构造函数没有返回值 2. 构造函数通常和类的名字一样

你可以使用this关键字，但是一般来说，只有发生了名字冲突的时候才要用this。

```dart
//使用this
class Hero{
	String? firstName;
	String? secondName;
	Hero(this.firstName,this.secondName);
}

//在变量名冲突的情况下使用this
class Hero{
	String? firstName;
	String? secondName;
	Hero(String firstName, String secondName){
		this.firstName=firstName;
		this.secondName=secondName;
	}
}
```

注意。类不会继承构造函数。并且，如果你没有定义构造函数，子类会自动调用父类的参数为空的构造函数，如果父类也没定义一个参数为空的构造函数，它会一直向上找直到找到。

这里还提到了factory关键字，但是我感觉没啥用，暂时用不到。


## getters and setters

get和set关键字定义了一种特殊的操作。试试吧：

```dart
  class Hero {
	Hero(this.firstName,this.lastName);
	String? firstName;
	String? lastName;
	get fullName=>'${firstName} ${lastName}';
	set fullName(n)=>firstName=n;
	sayName(){
		print(fullName);
	}
}

main(){
	Hero myHero=Hero('barry','yang');
	print(myHero.fullName);
	myHero.fullName='shit';
	print(myHero.fullName);
	myHero.sayName();
	
}
```

## 接口 interfaces

dart的接口和其他语言略有不同。接口的概念就是你可以首先定义一个类，然后再尝试用implements关键字实现这个类。原先的类就起到了一个接口的作用。dart的接口不需要显示的定义，如果你尝试implements一个类，那么原来的类就起到了接口的作用。

接口可能看上去和继承差不多，甚至更麻烦，因为他需要override superclass中的所有变量和方法。并且，关于使用接口和继承的讨论在oop中也一直存在。但是，这两个实际上是不同的东西。**继承只能继承一个类，而使用接口，你可以implements多个类**。 因此，如果你想写一个模仿多个类的类，你可以用接口，不然的话，应该用继承。


## 抽象类 abstract classes

抽象类就是这么一种类。他就是专门为你提供类的模板的，你要去继承他，然后把他没有定义的类定义一下。定义类时使用abstract可以定义抽象类。比如我有一个超级英雄类，他们都有一个飞行的能力，但是来自不同的国家，所以说不同的语言。

```dart
abstract class Hero{
	fly(int n){
		print('flied for ${n} kilometers');
	}

	sayMyName();
}
```

对于这个抽象类，你在继承的时候，就要实现sayName这个函数，但是可以不用实现fly这个函数，因为他已经定义了。

接口有点像一个所有所有方法和变量都没有实现的抽象类
## mixin

这个和接口有点像。就是你定义类的时候，使用with关键字可以把其他定义为mixin类型的类融合进来。然后其他类有的你就也都有了。并且你不需要重新定义。

## 可见性 visibility

类中的成员变量，函数前面加上下划线表示对库私有。dart的私有是库级别的，也就是同一个库中的类之间可以访问其他类的私有成员。

一句话，对库私有。没有什么对类私有什么的，没有划分到那个级别。

## 运算符 Operators

```
 <, >, <=, >=, -, +, /, ~/, *, %, |, ^, &, <<, >>, [], []=, ~, ==
```

也没什么，就是这些运算符可以在类中重载。比如你重载一个+运算符改为乘或者其他动作，那么它就会做其他的动作，而不再是普通的加号。

`==`运算符可不要随便重载哦，一是影响`==`判断，二是要顺便记得重载hashCode getter.


给这几种方法排个序，我觉得最容易用到的就是：普通的继承，抽象类，mixin, 接口。


# Dart 函数
## 参数

dart的参数也和其他语言的参数类似，让我们看看吧。让我们记住一个准则，所有的参数，不论是什么类型，在函数中都要有一个值。

### 位置参数

经典位置参数。定义时就像这样：`func(String arg1,String arg2){}` 按照位置传递参数。

### 可选位置参数

类似默认参数。意思就是你可以为这类参数指定默认值。在其他语言中，就直接写上默认参数就ok了，但是在dart中，对于可选参数，你一定要拿方括号括起来。

`func(String positionalarg, [String optional_positional_arg1 = 'test1'])`
`func(String positionalarg, [String? optional_positional_arg1 = 'test1'])`

1. **位置参数必须传入**
2. **可选位置参数，要么声明可以为空，要么给出默认值**

### 命名参数

命名参数没有可选不可选之分，默认都是可选的。使用时，参数用花括号括起来。

`func({required String arg1, String? arg2, String arg3='test'}){}`

可以看到，为了保证所有的参数一定要有一个值，你要么，**使用required**关键字，要么，**声明为可空**，要么，**给出默认值**。一定要满足至少一个。


**A function can have any number of required positional parameters. These can be followed either by named parameters or by optional positional parameters (but not both). 位置参数后面只能跟可选位置参数和命名参数中的一种**


## 断言

```
assert(a==1,'say something if a !=1');
```

断言用于调试阶段，不会对发布后的程序产生影响。使用断言表示你认为这里应该满足某个判断，否则就会推出。


## 泛型

泛型可以用来：

1. 定义一些变量时，告知dart某些东西的类型
2. 定义类的时候，定义为一个容器，既能限制这个类中的某些变量的类型，也能扩展这个类中某些变量的类型。在实现类的时候，dart会检查传入的变量是否符合泛型的限制
3. 定义函数的时候，也可以限制/扩展函数中某些变量的类型。

让我来试一下：

```dart
var nameList=List<String>();
var nameMap=Map<String,String>();

class nameClass<T>{
	T myName;

	nameClass(T value){
		myName=value;	
	}

	T getName()=>return myName;
}

setName<T>(nameClass myName,T value){
	myName.myName=value;
}
```

犯了几个错误：

1. 不再推荐使用构造函数创建List和Map
2. lambda写错了，不用写return,箭头后面就是返回值
3. setName的nameClass应该是nameClass<T\>

再试一下：

```


var nameList=<String>[];
var nameMap=<String,String>{};

class nameClass<T>{
	T myName;

	nameClass(T value){
		myName=value;
	}

	T getName()=>myName;
}

setName<T>(nameClass<T> myName, T name){
	myName.myName=name;
}
```

ok了老铁

