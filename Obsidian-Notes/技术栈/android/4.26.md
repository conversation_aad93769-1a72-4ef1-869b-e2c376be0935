
main.dart中：

```
void main() {
	runApp(MyApp());
	}
```

告诉flutter运行myapp()定义的app。程序开始就会进入MyApp()

```
class MyApp extends StatelessWidget {
  const MyApp({super.key});
  
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => MyAppState(),
      child: MaterialApp(
        title: 'Namer App',
        theme: ThemeData(
          useMaterial3: true,
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepOrange),
        ),
        home: MyHomePage(),
      ),
    );
  }
}
```


创建MyApp类，继承了一个无状态的小组件。that means，程序本身就是一个无状态的小组件。MyApp()中的代码设置了：
应用状态（稍后会详细介绍）、命名应用、定义视觉主题以及设置“主页” widget，即应用的起点。


```
class MyAppState extends ChangeNotifier {  var current = WordPair.random();}
```
这里引出应用状态的概念。应用状态是什么我也不知道，flutter中有很多管理应用状态的东西，而继承changenotifier是一种易于理解的方法。

好像知道是什么了。MyApppState定义了程序运行需要的数据。现在只有一个变量，也就是随即单词。而使用changenotifier可以像其他widget通知更改。上一个代码片中的使用 `ChangeNotifierProvider` 将创建状态（我也不知道什么叫创建状态，create status？）并将其提供给整个应用。然后，任何 widget 都可以获取状态。

悟：可以自由地创建继承自changenotifier的appstate类，然后在其中定义不同的数据，然后在不同的widgets中使用不同的appstate的changenotiferprovider。我们这个例子中创建了一个AppState，然后直接提供给了MyApp，这样的话所有的widgets都可以看到appstate中的变量变化，受到其影响。



```
class MyHomePage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    var appState = context.watch<MyAppState>();

    return Scaffold(
      body: Column(
        children: [
          Text('A AWESOME  idea:'),
          Text(appState.current.asLowerCase),

          ElevatedButton(
            onPressed: (){
              print('666');
            },
            child: Text('Next'),
          ),
        ],
      ),
    );
  }
}
```

