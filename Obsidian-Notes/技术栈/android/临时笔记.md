
# dart面向对象
## 成员变量，成员函数，static

最简单的类的定义：

```dart
class myclass1 {} //直接空也没问题
class myclass2 {
	String? str1;
	String? str2;
}
```

超简单，没什么东西。

你还可以使用static关键字，表明这个变量是类的变量，不需要实例化/初始化一个对象来使用它。

```dart
class myclass {
	static String str1='this is a test string';
}

main (){
	print(myclass.str1)
}
```

再写个方法，也是没什么东西，纯属让自己熟悉一下：

```dart
class myclass {
	String welcomestring='hi!';
	welcome(){
		print(welcomestring);
	}
}

main(){
	myclass myobj=myclass();
	myobj.welcome();
}
```

简单！顺便说点别的。

很多语言中创建对象都要用关键字new，但是在dart中可以省略。也就是这样写： `myclass myobj= new myclass();`

方法依然可以是static的。

```dart
class myclass {
	static welcome(){
		print('hi!');
	}
}

main(){
	myclass.welcome();

}
```

## 构造函数 constructors

不说了，懂得都懂，直接开写。

```dart
class Hero{
	String? firstName;
	String? lastName;
	Hero(String str1,String str2){
		firstName=str1;
		lastName=str2;
	}

	sayName(){
		print('${firstName} ${lastName}');
	}
}

main(){
	Hero myHero=Hero('barry','yang');
	myHero.sayName();
}
```

ok，一遍过。要注意：1. 构造函数没有返回值 2. 构造函数通常和类的名字一样

你可以使用this关键字，但是一般来说，只有发生了名字冲突的时候才要用this。

```dart
//使用this
class Hero{
	String? firstName;
	String? secondName;
	Hero(this.firstName,this.secondName);
}

//在变量名冲突的情况下使用this
class Hero{
	String? firstName;
	String? secondName;
	Hero(String firstName, String secondName){
		this.firstName=firstName;
		this.secondName=secondName;
	}
}
```

注意。类不会继承构造函数。并且，如果你没有定义构造函数，子类会自动调用父类的参数为空的构造函数，如果父类也没定义一个参数为空的构造函数，它会一直向上找直到找到。

这里还提到了factory关键字，但是我感觉没啥用，暂时用不到。



