[Obsidian-Cheat-Sheet/README.md at main · ieshreya/Obsidian-Cheat-Sheet · GitHub](https://github.com/ieshreya/Obsidian-Cheat-Sheet/blob/main/README.md)

# Heading 1

## Heading 2

--- Spacer  

- Bullet Points

- [x] Checklist

**Bold**

*Italic*

***Italic Bold***

==Highlights== ==ctrl+shift+h==

```Coding Blocks```

[[Links]](Sources)

> Quotes/ Blockquotes

Table Cell A  |  Table Cell B
----          |          ----

<em><strong>✨ Detailed Cheat Sheet </em></strong>

# Heading 1
 
```
# Heading 1 
```


## Heading 2

```
## Heading 2
```

**Line Break**

```

---

```

- Bullet Points

``` 
- Bullet 
```


- [X] Checklist

```
ctrl+l
- [x] list 
```

**Bold**

```
**text**
```

*Italic*

```
*text*
```

***Italic Bold***

```
***text***
```

~~Strikethrough~~

```
~~this text is crossed out~~
```

**==Highlights==**

```
ctrl+shift+h
==this text is highlighted==
```


**```Code Blocks```**

```
By putting 3 (```) signs before and after the code.
``` code ```
```

**[[Links]]**

```
[link](sources)
```

Blockquotes
> Quotes/ Blockquotes

```
> this is a quote.
```


Tables


Table Cell A  |  Table Cell B
----          |          ----


```

| Table Cell A  |  Table Cell B |
----          |          ----

```

---


### 🕹️ General & Editing Shortcuts

| Shortcut Key (General) | Functions                    | Shortcut Key (Editing)                    | Functions                             |
| ---------------------- | ---------------------------- | ----------------------------------------- | ------------------------------------- |
| Ctrl + S               | Editor: Saves the file       | Ctrl + B                                  | Bold Selected Text                    |
| Ctrl + N               | Creates a new note           | Ctrl + I                                  | Italicize Selected Text               |
| Ctrl + P               | Open command pallete         | Ctrl + K                                  | Insert External Link to Selected Text |
| Ctrl + O               | Opens Quick Switcher         | Ctrl + ]                                  | Indent                                |
| Ctrl + Shift + F       | Search in all files          | Ctrl + [                                  | Unindent                              |
| Ctrl + G               | Opens graph view             | Ctrl + D                                  | Delete current line                   |
| Ctrl + Alt + ←         | Navigate Back                | Ctrl + V                                  | Duplicate current line                |
| Ctrl + Alt + →         | Navigate forth               | Ctrl + Click                              | Open Note in Current Panel via Link   |
| Ctrl + F               | Searches current file        | Ctrl + Shift + Click                      | Open Note in New Panel via Link       |
| Ctrl + H               | Find/Replace in current file | Ctrl + P (Cmd + P on Mac)                 | Quick Search                          |
| Ctrl + E               | Toggle edit/preview modes    | Ctrl + N (Cmd + N on Mac)                 | New Note                              |
| Ctrl + ,               | Open Settings                | Ctrl + Shift + D (Cmd + Shift + D on Mac) | Create a Daily Note                   |
| Ctrl + Tab             | Next tab                     | Ctrl + / (Cmd + / on Mac)                 | Toggle Command Palette                |
| Ctrl + Shift + Tab     | Previous tab                 | Ctrl + E (Cmd + E on Mac)                 | Open Graph View                       |
| Alt + Tab              | Next App                     | Ctrl + Click (Cmd + Click on Mac)         | Open Link in New Pane                 |
| Alt + Shift + Tab      | Previous App                 |                                           |                                       |
| Win + Tab              | Task View                    |                                           |                                       |
| Win + Shift + Tab      | Next Window                  |                                           |                                       |
