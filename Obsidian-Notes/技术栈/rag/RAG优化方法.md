---
sr-due: 2025-07-24
sr-interval: 1
sr-ease: 210
---


#review 


> [!tips] 
> 没有最优的rag方案. 面向你的应用, 优化你的pipeline

----
# 1. Index

Index阶段的优化,本质上是优化数据的存储方式,组织方式.

## 1.1 数据预处理

1. 添加文件元信息: 时间, 作者等
2. 将原始文档转换为对llm友好的格式(这废话了, 肯定用markdown.): md, 表格->html
3. 新兴的技术 ColPali, ColQwen可以直接嵌入文档的图片.
4. 数据清洗: 去除页眉页脚,噪声等信息

## 1.2 chunking

固定大小chunk: 垃圾, 不好用
递归chunk: 尊重文档结构, 稍微复杂
按照结构chunk: 比较合理, 但是对于没有结构的文档难以下手
语义切分: 划分为一堆unit, 累积地做嵌入. 当检测到连续两次嵌入的偏差大于x的时候chunk (计算量想想就很夸张)
llm帮你切: 精确了,但是成本高

# 2. 检索前优化

## 2.1 query 转换

- query重写: 很经典. 用户问的问题很有可能问的很糟糕, 让llm重新组织一下效果会好一些.
- query expansion: 生成多个类似查询, 后续再rerank. 由于查询成本会提升, 所以是否值得使用还得看情况.
- query拆分. 有的问题会很大, 很多, 适合拆分为多个子问题并行查询. 比如用户会问:"为什么我吃的很健康还很累? 我的日常饮食应该改变吗?" -> 
	- 导致疲劳的饮食因素有什么
	- 如何确定饮食是否满足能量需求
## 2.2 query routing

- query routing: (就是agentic rag的核心思想)将不同的问题送到不同的检索器. 这针对于你有很多index(知识库)的场景. 可能一类问题是关于人事, 另一类是关于it. 这个过程需要借助llm能力做出判断(送到哪个检索器检索)


# 3. 检索优化

## 3.1 元数据过滤.

**减少数据检索的规模是非常有效的手段**. 通过限制文件类型,权限,时间,语言等可以有效提升检索效果. 借助时间戳也可以实现筛选时间范围能力. 如何选择你想添加的元数据, 也是可以根据应用来考虑的.

## 3.2 向量排除

一般大家喜欢用top_k,但是topk之外, 我们还可以:

(只有部分sdk支持, 有的sdk可能没有)

- 设置向量检索阈值, 超出这个阈值则不检索. 不过阈值的设定比较关键, 不能太小.
- autocut, 类似之前说的 semantic chunking. 根据搜索结果, 按照分数突变进行截断. 这个比较符合直觉, 只保留相关的, 去掉不相关的. 

## 3.3 混合检索

很经典. 就是同时使用向量检索和关键字检索. 既考虑语义相关又考虑关键字相关. 可以控制两个路径的权重. 

## 3.4 rerank

rerank模型具有精细的判断搜索结果和问题的相关性的能力. 通常我们使用vs然后加入rerank做最终排序.

# 4. 后处理

## 4.1 上下文后处理

 - 添加元数据:

对于检索到的上下文, 可以使用metadata增强. 额外的信息可能会有作用. 比如文件的时间戳, 作者等等. 更特别地, 有一种情况: 如果我们文档的快很小, 通过额外附加原始较大的文档快, 可以附加更多上下文信息, 提升回答效果.

- 上下文压缩.

有时候会搜索到很多不相关的内容, 这时候使用一个context compressor去除冗余. 感觉这个不太常用.

# 5. 提示词工程

没问题, 提示词工程也很重要.

cot-> 可以在检索到的信息很密集的时候发挥效果
tot -> 在检索结果中出现了多个方向的答案的时候会有用.
react ->  通过react, 可以让llm在检索结果后再推理, 给予大模型动态地, 多轮检索的能力. 不过延迟一想就很大.

# 6. 微调llm

大多数llm是通用大模型. 当场景非常专业的时候, 可能需要专业知识微调. 如果只是改变一下输出风格,格式, 可以先试试提示词工程. 但是如果需要llm自身可能并不知道的领域知识, 使用微调后的llm是一个可行的选择.**在特定任务和领域，微调后的模型表现通常远超提示词工程。**

pros:

1. 让大模型学会领域知识, 不瞎说话

cons:

1. 计算资源昂贵
2. 最好需要高质量领域知识
3. 每次调整都要重新训练
