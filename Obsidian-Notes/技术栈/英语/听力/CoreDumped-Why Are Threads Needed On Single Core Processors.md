
thread, a subject in computer science that many people are afraid of. and no wonder, it seems complicated.

> [!NOTE] subject **主题** 科目 
> 1. No wonder he's tired — he worked all day.  
> 	（难怪他累了——他工作了一整天。）
> 2. No wonder you're cold, you're not wearing a coat!  
> 	（难怪你觉得冷，你都没穿外套！）
> 3. No wonder people are confused — the instructions are unclear.  
> 	（难怪人们会困惑——说明写得不清楚。）


> [!NOTE] no wonder 难怪
> 这个倒是知道,但是不知道可以这么用.
> no wonder, xxxx(后面跟解释)
> 比如:
> No wonder you're cold, you're not wearing a coat.
> 这也难怪你感觉冷, (毕竟)你没穿外套

ok no, i'm just kidding. today, we'll explore the fundmentals of threads, and how they leverage concurrency to optimize computer resource utilization. Hi friends, my name is george, and this, is core dumped. 3

as long as you get these four concepts, threads are surprisingly easy to understand. 2

 the premise is simple, in multi-process systems,  if we have one cpu and multiple running processes, the operating system make those processes share the cpu by alternating access between them. 10
 
> [!NOTE] the premise is simple - 基本原理很简单/基本概念很简单
> The premise is simple: Take a hand-held power tool.
>
>The premise is simple: lenders contribute to a farm, restaurant, or artisan food business.
>
>https://app.ludwig.guru/s/the+premise+is+simple


> [!NOTE] alternating n.
> 这里的意思是 轮转某个东西. 比如alternating access to cpu between them, 就是在他们之间 轮转 到cpu的权限. 但是这个短语还是有点专业了. 口语化的表达可以是: **taking turns to use the CPU**
> 
>  **合理组合**（假设场景）：
>     - alternating permissions (轮替权限)
>     - alternating data streams (交替数据流)
>     - alternating monitoring shifts (轮班监测)


 this happens very fast. so fast in fact that the user is **under the illusion** that all processes are running simultaneously. 4
