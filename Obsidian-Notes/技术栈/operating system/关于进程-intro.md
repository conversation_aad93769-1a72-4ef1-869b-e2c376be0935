[A PROGRAM is not a PROCESS. - YouTube](https://www.youtube.com/watch?v=7ge7u5VUSbE&list=PL9vTTBa7QaQPdvEuMTqS9McY-ieaweU8M&ab_channel=CoreDumped)

----

**一个问题, what to call the activities performed by cpu?**

- 早期批处理系统把他执行的work units叫做jobs
- time sharing systems. they were designed to share cpu among multiply users, so we said that those systems ran **user programs**
- modern pc - processes, not programs or something

**but, what's the differences between processes and programs?**

let's define a program:

a program is a sequence of instructions and the data the cpu needs to accomplish specific task.  this can also describe an executable file, you may notice, and that's pretty much what a program is. 
![[Pasted image 20250606160105.png]]

### memory layout of the process

**ok, how programs run?**

if you run a program, it will be loaded into memory, that's where cpu start fetching instructions and data.

when loaded into the memroy, the section containing the executable code is known as **text** section, while data such as golbal variables and constant values is loaded into the **data** section.

![[Pasted image 20250606114947.png]]

we already know a lot about the stack and the heap,  such as the fact that they are growing and shrinking all the time.

![[Pasted image 20250606115547.png]]
the text section is the only one never change, neither in size nor content. but we can't say the same about the data section, because even though it doesn't change in size, its content may vary depending on what the running program is doing.( 比如说 change global variables)

now this the memory lay out of the process, but it's not the process itself.
![[Pasted image 20250606115959.png]]

**在此基础上...**

比如, 如果你同时打开两个笔记本, 那么其内存中可能是这样的:

![[Pasted image 20250606120412.png]]

1. 由于打开的是同一个程序, 所以text部分应该是一样的.
2. data部分可能不同
3. heap部分, 由于上面的那个编辑的内容多一些, 所以heap部分会比较大


**关于compile language  and interpreter language**

对于compile lang, 编译出来的程序是符合上面的例子的,比如你c语言写出来的instruction是会放在text部分, 全局变量放在data部分. 但是对于解释性语言呢? 这些语言, 其program 对应的是解释器. 也就是说, 这些语言在运行的时候(你应该想到了, 这些语言在运行的时候本身运行的就是解释器本身), 其text部分放的就不是你写的code对应的instuction了, 而是python解释器对应的instruction.你写的解释语言代码最终会被load到heap中.


