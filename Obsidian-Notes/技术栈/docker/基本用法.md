---
sr-due: 2025-07-24
sr-interval: 1
sr-ease: 210
---

#review

**核心概念（1分钟理解）：**

- **镜像 (Image):** 想象成一个软件的“安装包”，但它包含了完整的运行环境（比如操作系统、依赖库、你的应用程序）。镜像是只读的。
    
- **容器 (Container):** 镜像的“运行实例”。你可以从一个镜像启动N多个容器，它们之间是隔离的。容器是可读写的。
    
- **仓库 (Repository):** 存放镜像的地方，比如 Docker Hub (官方的)、阿里云容器镜像服务等。


**0. 安装 Docker**  
这步略过，假设你已经根据你的操作系统 (Windows/Mac/Linux) 从 Docker 官网安装好了 Docker Desktop 或 Docker Engine。  
验证安装：docker --version

**1. 拉取镜像 docker pull

用法很简单,就是 `docker pull 镜像` ,比如常用的

- `docker pull nginx`
- `docker pull redis`

但是有的时候也有稍微不同的pull,比如:

- `docker pull xxx/imagename`
- `docker pull xxx/imagename:1.1`
- `docker pull xx/xxx/imagename:1.1`

简单来说：

一个完整的镜像名字是:

`[Registry/][Username/]<ImageName>[:<Tag>]`

- **Registry**: docker registry的地址. docker registry就是类似docker 应用商店, 但是每个人都可以托管自己的docker registry, 于是你可以制定docker registry的地址. 默认是`docker.io`,也就是docker官方的registry, 也就是docker hub.
- **Username**: docker registry下会有很多用户/组织发布的镜像. 比如barry在alice托管的registry上发布一个镜像,那么景象的名字就可能是: alice.com/barry/barrys-image:latest. 默认就是`library`,也就是docker 官方维护的镜像.
- **ImageName**: 就是镜像名字. 比如nginx, redis等, 或者我最近在用的alist, qbittorrent等. 
- **Tag**:就是版本号,默认不写就是`latest`

那么:

- docker pull nginx:1.21 是从 **Docker Hub 官方仓库 (Official Images)** 拉取镜像。对应真正的完整的镜像名就是: `docker.io/library/nginx:1.21`
- docker pull xxx/nginx:1.21 是从 **Docker Hub 上名为 xxx 的用户或组织** 的仓库里拉取镜像。对应完整镜像名字就是: `docker.io/xxx/nginx:1.21`
- docker pull gcr.io/google-containers/busybox 是从google的仓库拉取google-containers上传的busybox镜像, 完整的镜像名字就是:`gcr.io/google-containers/busybox:latest`

所以，xxx/nginx 和 nginx 是两个完全不同的镜像，尽管它们可能都基于 Nginx，但内容、配置和维护者都不同。对于初学者和绝大多数场景，直接使用官方镜像（如 nginx, redis, python:3.9-slim）是最简单和可靠的选择。


**2. 查看本地已下载的镜像**

      `docker images `

**3. 运行容器 (核心命令！)**  

docker run [OPTIONS] IMAGE [COMMAND] [ARG...]

Generated bash

      `# 场景1: 运行一个 nginx web 服务器，后台运行，并将容器80端口映射到宿主机8080端口 docker run --name my-nginx -d -p 8080:80 nginx`
    

IGNORE_WHEN_COPYING_START

content_copy download

Use code [with caution](https://support.google.com/legal/answer/13505487). Bash

IGNORE_WHEN_COPYING_END

- --name my-nginx: 给容器取个名字叫 my-nginx，方便后续管理。
    
- -d: 后台运行 (detached mode)。
    
- -p 8080:80: 端口映射，宿主机端口:容器端口。现在访问 http://localhost:8080 就能看到 Nginx 欢迎页。
    
- nginx: 使用哪个镜像来启动容器。
    

Generated bash

      `# 场景2: 运行一个临时的 ubuntu 容器，并进入其交互式终端，容器退出后自动删除 docker run --rm -it ubuntu bash`
    

IGNORE_WHEN_COPYING_START

content_copy download

Use code [with caution](https://support.google.com/legal/answer/13505487). Bash

IGNORE_WHEN_COPYING_END

- --rm: 容器退出后自动删除它。适合一次性任务。
    
- -it: -i (interactive) 保持 STDIN 打开, -t (tty) 分配一个伪终端。合起来就是交互式终端。
    
- ubuntu: 使用的镜像。
    
- bash: 在容器内执行的命令 (这里是启动 bash shell)。  
    * 在容器的 bash 里，你可以 ls, cat /etc/hosts 等，exit 退出容器。
    

**4. 查看正在运行的容器**

Generated bash

      `docker ps`
    

IGNORE_WHEN_COPYING_START

content_copy download

Use code [with caution](https://support.google.com/legal/answer/13505487). Bash

IGNORE_WHEN_COPYING_END

- -a (或 --all): 查看所有容器，包括已停止的。  
    bash docker ps -a
    

**5. 停止、启动、重启容器**

Generated bash

      `# 停止名为 my-nginx 的容器 docker stop my-nginx  # 启动已停止的 my-nginx 容器 docker start my-nginx  # 重启 my-nginx 容器 docker restart my-nginx`
    

IGNORE_WHEN_COPYING_START

content_copy download

Use code [with caution](https://support.google.com/legal/answer/13505487). Bash

IGNORE_WHEN_COPYING_END

- 你也可以用容器ID (Container ID，从 docker ps 获取) 来操作，比如 docker stop <container_id>。
    

**6. 查看容器日志**

Generated bash

      `docker logs my-nginx`
    

IGNORE_WHEN_COPYING_START

content_copy download

Use code [with caution](https://support.google.com/legal/answer/13505487). Bash

IGNORE_WHEN_COPYING_END

- -f (或 --follow): 实时跟踪日志输出 (类似 tail -f)。  
    bash docker logs -f my-nginx
    
- --tail <行数>: 只看最后几行日志，例如 --tail 100。
    

**7. 进入正在运行的容器 (进行调试或操作)**  
如果容器是后台运行的 (-d)，你想进去看看：

Generated bash

      `# 进入 my-nginx 容器，并打开一个 bash shell docker exec -it my-nginx bash`
    

IGNORE_WHEN_COPYING_START

content_copy download

Use code [with caution](https://support.google.com/legal/answer/13505487). Bash

IGNORE_WHEN_COPYING_END

- exec: 在运行的容器中执行命令。
    
- -it: 同样是交互式终端。
    
- my-nginx: 目标容器名或ID。
    
- bash: 要在容器内执行的命令。  
    * 进去后可以 ls /usr/share/nginx/html 等，exit 退出。
    

**8. 删除容器**  
只能删除已停止的容器。

Generated bash

      `# 先停止 (如果还在运行) docker stop my-nginx  # 再删除 docker rm my-nginx`
    

IGNORE_WHEN_COPYING_START

content_copy download

Use code [with caution](https://support.google.com/legal/answer/13505487). Bash

IGNORE_WHEN_COPYING_END

- 强制删除一个正在运行的容器 (不推荐，但有时需要):  
    bash docker rm -f my-nginx
    
- 删除所有已停止的容器 (清理命令):  
    bash docker container prune # 或者 docker rm $(docker ps -aq) (旧方法，不推荐，容易误删)
    

**9. 删除镜像**  
只能删除没有被任何容器 (包括已停止的容器) 使用的镜像。

Generated bash

      `docker rmi nginx # 或者用 IMAGE ID docker rmi <image_id>`
    

IGNORE_WHEN_COPYING_START

content_copy download

Use code [with caution](https://support.google.com/legal/answer/13505487). Bash

IGNORE_WHEN_COPYING_END

- 如果镜像被容器使用了，需要先 docker rm 对应的容器。
    
- 强制删除镜像 (不推荐，除非你确定):  
    bash docker rmi -f nginx
    
- 删除所有悬空的镜像 (dangling images，即没有标签且不被任何容器使用的镜像):  
    bash docker image prune
    
- 删除所有未被使用的镜像 (包括悬空和有标签但未被容器使用的):  
    bash docker image prune -a
    

**10. 数据持久化 (挂载卷 Volume)**  
容器删除后，容器内部产生的数据默认会丢失。为了持久化数据，需要使用卷。

Generated code

      `` ```bash # 场景: 运行 MySQL，并将数据存储在宿主机的 /my/custom/mysql_data 目录 # 注意: Windows 下路径格式可能不同，如 C:\my\custom\mysql_data # 推荐使用 Docker 管理的卷 (named volume) docker volume create my-mysql-data # 创建一个名为 my-mysql-data 的卷  docker run --name my-db -d \   -p 33060:3306 \   -v my-mysql-data:/var/lib/mysql \   -e MYSQL_ROOT_PASSWORD=mysecretpassword \   mysql:5.7 ``` *   `-v my-mysql-data:/var/lib/mysql`:     *   `my-mysql-data`: 宿主机上的 Docker 卷名 (推荐) 或绝对路径 (例如 `/opt/mysql_data` 在 Linux，`C:\docker_data\mysql` 在 Windows)。     *   `/var/lib/mysql`: 容器内 MySQL 存储数据的路径。 *   `-e MYSQL_ROOT_PASSWORD=mysecretpassword`: 设置环境变量，MySQL 镜像会用这个来初始化 root 密码。  **查看 Docker 管理的卷：** ```bash docker volume ls docker volume inspect my-mysql-data # 查看卷的详细信息，包括宿主机上的实际路径 docker volume rm my-mysql-data # 删除卷 (数据会丢失！) ``` ``
    

IGNORE_WHEN_COPYING_START

content_copy download

Use code [with caution](https://support.google.com/legal/answer/13505487).

IGNORE_WHEN_COPYING_END

**11. 构建自己的镜像 (Dockerfile)**  
当你需要定制化环境或部署自己的应用时，就需要构建镜像。

Generated code

      ``创建一个名为 `Dockerfile` (无后缀) 的文本文件： ```dockerfile # Dockerfile 示例 (一个简单的 Node.js 应用) FROM node:18-alpine  # 基础镜像  WORKDIR /app         # 设置工作目录  COPY package*.json ./ # 拷贝 package.json 和 package-lock.json  RUN npm install      # 安装依赖  COPY . .             # 拷贝所有其他文件  EXPOSE 3000          # 声明容器对外暴露的端口 (非实际映射)  CMD ["node", "app.js"] # 容器启动时执行的命令 ```  在 `Dockerfile` 所在的目录执行构建命令： ```bash docker build -t my-custom-app:1.0 . ``` *   `-t my-custom-app:1.0`: 给镜像打上标签 (名称:版本)。 *   `.`: 指定 Dockerfile 的上下文路径 (这里是当前目录)。  构建完成后，就可以 `docker images` 看到 `my-custom-app`，然后 `docker run` 它。``
    

IGNORE_WHEN_COPYING_START

content_copy download

Use code [with caution](https://support.google.com/legal/answer/13505487).

IGNORE_WHEN_COPYING_END

**12. Docker Compose (多容器应用编排)**  
当你的应用包含多个服务 (如 web + db + redis)，手动管理多个 docker run 会很麻烦。docker-compose 用一个 docker-compose.yml 文件来定义和运行多容器应用。

Generated code

      ``创建一个 `docker-compose.yml` 文件： ```yaml version: '3.8' # 版本号  services:   web:     image: nginx:latest     ports:       - "8081:80" # 注意这里用了 8081，避免和上面手动运行的 nginx 冲突     volumes:       - ./html:/usr/share/nginx/html # 将当前目录下的 html 文件夹挂载到 nginx 的网站根目录    app: # 假设你有一个名为 my-node-app 的镜像     image: my-node-app:latest # 或者是 build: . 如果 Dockerfile 在当前目录     # build: ./app-directory # 如果 Dockerfile 在子目录 app-directory     ports:       - "3000:3000"     environment:       - NODE_ENV=development     depends_on: # 定义服务依赖关系       - db   db:     image: mysql:5.7     environment:       MYSQL_ROOT_PASSWORD: mysecretpassword       MYSQL_DATABASE: myappdb     volumes:       - db_data:/var/lib/mysql  volumes: # 定义具名卷   db_data: ``` *   在 `docker-compose.yml` 文件所在的目录执行：     ```bash     # 启动所有服务 (后台运行)     docker-compose up -d      # 查看服务状态     docker-compose ps      # 查看服务日志 (例如 web 服务)     docker-compose logs web     docker-compose logs -f web # 实时跟踪      # 停止并删除所有服务、网络、卷 (除非卷是 external)     docker-compose down      # 只停止服务，不删除     docker-compose stop      # 启动已停止的服务     docker-compose start      # 重启服务     docker-compose restart web      # 构建或重新构建服务 (如果 Dockerfile 有变化)     docker-compose build web     docker-compose up -d --build # 启动时强制重新构建     ``` *   `docker-compose` 命令现在很多时候也集成到了 `docker` CLI 中，可以用 `docker compose` (中间有空格) 替代，例如 `docker compose up -d`。``
    

IGNORE_WHEN_COPYING_START

content_copy download

Use code [with caution](https://support.google.com/legal/answer/13505487).

IGNORE_WHEN_COPYING_END

**13. 清理系统 (非常重要！)**  
Docker 长时间使用会产生很多无用的容器、镜像、卷和网络，占用磁盘空间。

Generated code

      `` ```bash # 删除所有已停止的容器 docker container prune -f  # 删除所有悬空镜像 (dangling images) docker image prune -f  # 删除所有未使用的镜像 (包括悬空和有标签但未被容器使用的) docker image prune -a -f  # 删除所有未使用的卷 docker volume prune -f  # 删除所有未使用的网络 docker network prune -f  # 一键清理所有：已停止的容器、未被任何容器使用的网络、悬空镜像、以及构建缓存 docker system prune -f  # 更彻底的清理：上面的一切 + 未被容器使用的所有镜像 (不仅仅是悬空) 和所有未使用的卷 docker system prune -a --volumes -f ``` **警告：`prune` 命令是破坏性的，请谨慎使用，特别是 `-a` 和 `--volumes` 选项。** ``
    

IGNORE_WHEN_COPYING_START

content_copy download

Use code [with caution](https://support.google.com/legal/answer/13505487).

IGNORE_WHEN_COPYING_END

---

**总结 99% 场景核心命令清单：**

- docker pull <image>: 拉取镜像
    
- docker images: 查看镜像
    
- docker run [options] <image> [command]: 运行容器
    
    - 常用 options: -d, -p, -v, --name, -it, --rm, -e (环境变量)
        
- docker ps [-a]: 查看容器
    
- docker stop <container>: 停止容器
    
- docker start <container>: 启动容器
    
- docker restart <container>: 重启容器
    
- docker logs [-f] <container>: 查看容器日志
    
- docker exec -it <container> <command>: 进入容器执行命令
    
- docker rm [-f] <container>: 删除容器
    
- docker rmi [-f] <image>: 删除镜像
    
- docker build -t <name:tag> .: 构建镜像 (配合 Dockerfile)
    
- docker volume create/ls/inspect/rm: 管理卷
    
- docker-compose up -d / down / ps / logs / build: (或 docker compose ...) 管理多容器应用
    
- docker system prune [-a --volumes -f]: 系统清理
    

---

**进阶方向：**

- 深入理解 Dockerfile 优化
    
- Docker 网络 (bridge, host, overlay)
    
- Docker 安全
    
- Docker Swarm / Kubernetes (容器编排)
    
- 私有仓库搭建 (Harbor)
    

这套下来，你应该能应对绝大多数 Docker 使用场景了。剩下的 1% 就是在特定问题上查阅官方文档或 Google。动手实践是最好的老师，快去试试吧！