
从倒排索引聊起..

### 倒排索引 3.5h

#### 为什么使用排序列表，不用hashmap？

实际上有多种组织字典的方式，他用的好像也不是排序列表，而是fst。

fst的学习，东西也是非常多，草。

说fst似乎离不开fsm,trie, fsa. 那看看这仨吧。

我靠，东西有点多。[# 使用自动机来索引1,600,000,000个键](https://steflerjiang.github.io/2017/03/18/%E4%BD%BF%E7%94%A8Automata%E6%9D%A5%E7%B4%A2%E5%BC%951-600-000-000%E4%B8%AA%E9%94%AE/) 这篇文章讲的挺好。基本对这三种数据结构有了解了。前后两天加起来花了得有俩小时吧。

```
- Trie（前缀树）：

- 确实主要用于共享前缀
- 最常见的应用是自动补全和前缀搜索
- 虽然可以用来实现哈希表，但通常不这么做，因为空间效率不如传统哈希表
- 特别适合处理字符串集合，尤其是需要前缀操作的场景

- FSA（有限状态自动机）：
- 
- 比 Trie 更紧凑，因为可以合并相同的后缀状态
- 主要用于成员检测（是/否判断）
- 在正则表达式引擎中广泛使用
- 可以看作是 Trie 的优化版本（针对存储效率）
- 
- FST（有限状态转换器）：

- 是 FSA 的扩展，增加了输出能力
- 可以实现键值映射（比如字典功能）
- 在自然语言处理中很常用
- 支持双向查找（可以从值找键）
```

###

