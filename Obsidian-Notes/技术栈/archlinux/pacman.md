
# 1. `sudo pacman -Q`

查看所有**已安装**的包

----
- `sudo pacman -Qs <regex>`  模糊搜索. 比如 `sudo pacman -Qs python`找到所有名字和dp中带有python的包
- `sudo pacman -Qi` xx (输入包名前几个字母让它自动补全) 显示包的详细信息


# 1.5 `sudo pacman -S`

这个就不说了, 用来更新软件包, 搜索软件包:

`sudo pacman -Syu`
`sudo pacman -Syyu`
`sudo pacman -S xx`
`sudo pacman -Sy`

# 2. `sudo pacman -F`

找某个 **文件** 是由哪个包提供的. 有时候想装软件的时候会比 `-Ss` 有用. 比如: 我想安装一个命令a,但是a的包名字不叫a,这个时候就可以 `sudo pacman -F a`,  因为提供a命令的包大概率有一个/usr/bin/a文件, 或者其他名字中包含单词a的文件. 搜索完了之后就可以从搜索出来的包中精挑细选, 看看是哪个.