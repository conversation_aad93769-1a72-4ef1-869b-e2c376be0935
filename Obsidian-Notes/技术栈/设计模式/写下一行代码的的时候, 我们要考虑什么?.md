
# 1. SRP 单一职责原则

这个代码是否只关注他本身的逻辑, 而不会有其他的逻辑掺进去?

# 2. 复用

以今天的limit extract作为例子. 我究竟应该把配置读取放到indexer中还是外?

1. 一开始, 一段普通的程序读取配置, 这属于再正常不过的事情了. 我虽然是index, 但是读取配置, 喂, 这不就像一个人工作的时候肯定也需要喝水一样, 属于我的肯定会干的行为吗. 我放在我的类中,很自然啊
2. 抽象出来, 让他不要管配置的读入.而是由其他部分,比如启动的部分,管理配置的读入.这是否合适?



|                  | 让index自己读取配置                                                                                                                                    | 实例化index的时候把配置传进去 |
| ---------------- | ----------------------------------------------------------------------------------------------------------------------------------------------- | ----------------- |
| 代表着什么?<br>收集一下头绪 | 1. index类中要负责config部分的内容. 最垃圾的方法, 从file中读取配置,这个不考虑, 太低级了. 高级一些和config类交互. 这意味着index和config是有一定的耦合的. 加入你用到了一些config以后会废弃的特性, 那么后期就要侵入性地修改index代码 |                   |
|                  |                                                                                                                                                 |                   |
|                  |                                                                                                                                                 |                   |
