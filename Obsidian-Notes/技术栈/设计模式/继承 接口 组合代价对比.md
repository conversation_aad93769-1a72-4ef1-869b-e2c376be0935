

|      | 继承                                       | 接口                                                                     | 组合                                         |
| ---- | ---------------------------------------- | ---------------------------------------------------------------------- | ------------------------------------------ |
| 啥意思？ | 方法写到父类中，子类继承就完了                          | 方法写到接口中，需要的类实现就完了                                                      | 方法写道接口中，再单独起一个类实现这个接口。最终需要这个特定的类的类引用这个类    |
| pros |                                          |                                                                        |                                            |
| cons | 1. 子类会变化。如果子类的同一个方法产生了很多变化，那么很多子类的方法都要改动 | 1.这个其实和继承类似。就是如果你只是设计一个抽象接口（充其量使用新特性--可以使用默认方法）那么也就是和继承一样而已。不同的方法还是要重写 | 这个是解耦的方法。针对你的方法单独设计方法类，然后如果有需要的这个方法的就引用这个类 |

那为什么在观察者中，使用了接口，而不是组合呢？

问题貌似不一样。策略模式问题中，是主体想拥有多种可变的策略。
观察者模式问题中，是一对多关系的解决...?就是说，有一对多关系的时候，推荐使用观察者？
等一下，观察者中，貌似..也是使用了组合啊？
你看，他也是吧observer先抽象成接口，然后又有很多个不同的实现了接口的类。
然后subject再引用这些具体的类。我去，很直观，没啥问题呀。