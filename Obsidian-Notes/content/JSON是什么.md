在看[json schema规范](https://json-schema.xiniushu.com/)的时候看到这么一句

	JSON Schema 是用于验证 JSON 数据结构的强大工具

理解json到底后回看这句话感觉透彻了。

## So, JSON到底是什么

当我们想到json的时候，我们会想到什么呢？

你可能会说...

	哦，我知道，就是那个，大括号括起来的那个，就是python的字典嘛。键一般是个字符串，然后值可以是数字，字符串，或者继续嵌套一个字典。 

是，这些我们都知道，但是这，太不专业了。你应该知道json这个东西，他的spcifications，就是，什么样的是一个合法的json,什么样的不是。他的边界是什么。

## 几个术语

先了解几个东西：

- json定义了object类型：也就是我们常见的`{key:value}`类型。
- 同时还有array类型，就类似python的list类型。

以下内容参考[json.org](json.org)：

object的定义：

	An _object_ is an unordered set of name/value pairs. An object begins with {left brace and ends with }right brace. Each name is followed by :colon and the name/value pairs are separated by ,comma.

 *同时他也限定了object类型的key一定要是string。json.org的描述中没说，但是图上画了* 

array的定义：

	An _array_ is an ordered collection of values. An array begins with [left bracket and ends with ]right bracket. Values are separated by ,comma.

这里多次提到的value,也给出了细致的定义：

	A _value_ can be a _string_ in double quotes, or a _number_, or true or false or null, or an _object_ or an _array_. These structures can be nested.

value有六种：

1. string
2. number
3. boolean
4. null
5. object
6. array

也就是说：

1. 对于object类型，key只能是字符串，value可以是六种value中的任意一种
2. 对于array类型，其元素是value的集合，故元素的类型也就是六种value中的任意一种

下表从 JSON 类型的名称映射到它们在 Python 中的类似类型：

| JSON    | Python    |
| ------- | --------- |
| string  | string    |
| number  | int/float |
| object  | dict      |
| array   | list      |
| boolean | bool      |
| null    | None      |

不难看出：

1. 我们最常用的json的格式，其实就是object类型，也就是`{key:value}`这种类型，可以理解为python的字典类型。

ok,了解了json定义的几个类型，接下来就看看json是什么...虽然我们已经几乎知道了
## json就是...

简而言之：

很久以前，json被认为只能是object类型和array类型。但是现在，任何json value都是有效的json.也就是说：`"hello"` , `true`, `1`,`null` 这样的都是都是有效的json。

但是注意几个需要小心的地方：

- 当你使用http中发送application/json类型的数据时，有些服务器可能还不支持解析非 object 和 array 类型的 数据，如果可以的话还是兼容性高一点比较好


参考来源：

[**JSON Schema 规范（中文版）**](https://json-schema.xiniushu.com/)
[Is this simple string considered valid JSON?](https://stackoverflow.com/questions/7487869/is-this-simple-string-considered-valid-json)
[What is the minimum valid JSON?  最低有效的JSON是什么？](https://stackoverflow.com/questions/18419428/what-is-the-minimum-valid-json)
[json.org 中文](https://www.json.org/json-zh.html)