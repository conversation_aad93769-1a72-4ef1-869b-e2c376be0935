
# part1 微调

## 一 低秩微调

低秩微调主要知道LoRA以及LoRA的几个变体，但是其他具体不同的低秩微调这个方向不太清除。

1. LoRA微调，通过在模型的权重参数中加上两个小的低秩矩阵。微调时训练两个低秩的矩阵实现高效微调

## 二 角色扮演微调

### 1. 评估微调模型能力的方案

方法：考虑到是一个role play的任务，比较难以量化，可以使用llm as judge方法判断。同时，也可以使用a/b测试看看是否有效

指标：

- 人格一致性：是否与设定角色的人格一致
- 情节一致性：上下文是否连贯
- 角色知识掌握：是否掌握角色知识，是否了解角色过往经历

数据集：

测试数据集示例

假设我们角色扮演的是一名大学教授

| 用户输入      | 用户期待回复                       |
| --------- | ---------------------------- |
| 你是谁       | 我是一名大学教授，负责xxxx              |
| 你从哪里毕业的？  | 我当年是从xxx大学毕业的，现在在这里作为你们的xx老师 |
| 帮我做做这题... | 我可以帮助你学习，但是我不能直接xxx          |
## 2. 训练数据准备

包括但不限于角色设定、世界背景与常识、常见问题解答、能力边界判定等场景，除此之外，还要面向角色扮演常用的语言，对话风格准备对应语料。

**训练数据**可以根据问题，寻找是否有现有数据并补充。或者也可以给一个能力比较强的模型，让这个模型生成训练数据。

**数据格式**可以是将上面数据分类后均匀打乱排序后的一个jsonl file。打乱后的数据有利于模型每次训练都可以在多个方向上优化

## 3. 可能的问题以及应对

可能有这几个问题：

数据集构造困难：难以收集目标角色的数据集。这个问题可以试着从小规模的数据集构造大规模数据集

多轮对话效果差：构造更多的多轮对话数据

回复效果差：可能是数据集质量不好/掺杂低质量数据，要提高数据集质量

回复多样性差：可能是数据少了，可以增加多个方向的数据集数量

# part2 rag

## 1. 理想化的rag： llm+向量数据库流程

先说说最简单的rag流程：

index阶段：清洗语料 -> 语料分片，嵌入
query阶段：用户提问 -> 嵌入用户问题为向量 -> 在向量数据库中搜索得到相关语料 ->将语料放在prompt上下文中让llm回答 

我个人觉得， 比较通用的，基于向量数据库的rag流程：

index阶段：根据业务场景清理语料（去除冗余信息等）-> 设计合适大小的chunk size，overlap，嵌入。必要时可以引入高级嵌入方法，比如层次化嵌入+纯视觉嵌入
query阶段：用户提问 -> query重写（包括复杂问题拆分，问题转换为与语料匹配的格式）-> bm25+向量数据库 多路召回（粗排，兼顾语义与字符匹配）->rerank（精排，精确匹配问题与答案）->将结果放入llm上下文，llm判断语料是否充足，是否继续检索还是直接回答。

面向业务需求，可以引入其他rag优化技术，包括：

1. 多索引 针对不同语料建立多个数据库
2. graph based rag技术

## 2. 聊天记忆的存储 更新 检索


这里仅考虑使用rag技术，即每次回答都使用rag将相关记忆放到上下文中。

1. 最基本的，考虑相关性。每次回答将用户问题相关的记忆检索到

考虑这是一个长期对话的npc，针对记忆系统的建模，可以引入以下内容：

存储：

1. 用户画像的记录，包括用户喜欢什么，是什么样的人等  
2. 长期记忆，每次对话都整理为一些summary，将对话记录以及summary存储起来，便于后面检索
3. 短期记忆，也就是短期和和用户聊天的记忆，这个可以简单地通过附加最近多轮聊天记录模拟
4. 程序化记忆，用户有时候想让大模型拥有一些能力，比如讨论问题的方式等。这种深层次的，影响深远的记忆可以通过微调技术实现

更新：

1. 洞察用户的画像，及时更新
2. 异步整理所有聊天记录。可以通过转写将聊天记录整理为 日期-用户讨论了xxxx 等这种格式，便于检索
3. 短期记忆，即可以通过控制聊天上下文长度实现
4. 程序化记忆，针对用户提出的一些，可能影响深远的，影响正常模型能力的要求，准备语料，通过微调，prompt engineering等定制化模型输出风格

检索：

1. 用户画像可以每轮都附带，或者根据最近聊天记录附带
2. 长期记忆可以综合当前会话相关性，时间等因素打分召回
3. 短期记忆可以近似为最近聊天记录，暂时不需要检索
4. 程序化记忆也可以不检索

# part3 agent

## agent工作流

以react为例：re-reasoning -> act-action -> observation 循环

一般来说，大体上是以下步骤：

收到任务 -> 任务拆解->plan -> 多个智能体调度 -> 信息收集 -> 反馈给初始智能体，判断效果->结束

许多agent还会有memory组件，作为处理任务的上下文



# 算法题

```python
class TrieNode:
    def __init__(self):
        self.children: dict[str, TrieNode] = {}
        self.is_end = False


class Trie:
    def __init__(self):
        self.root: TriNode = TrieNode()

    def insert(self, word: str):
        node = self.root
        for char in word:
            if char not in node.children:
                node.children[char] = TrieNode()
            node = node.children[char]
        node.is_end = True

    def search(self, word: str) -> bool:
        node = self.root
        for char in word:
            if char not in node.children:
                return False
            node = node.children[char]
        if node.is_end:
            return True
        else:
            return False

    def startsWith(self, prefix) -> bool:
        node = self.root
        for char in prefix:
            if char not in node.children:
                return False
            node = node.children[char]
        return True
```