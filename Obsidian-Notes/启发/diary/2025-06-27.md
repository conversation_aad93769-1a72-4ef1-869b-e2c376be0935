以前只是大概觉得本科教育可能有问题,今天突然刷到[Site Unreachable](https://www.zhihu.com/question/1921661746881466856),我不禁回忆,到底有什么问题.

抛开我自己学习的内容, 单就考虑开设的课程来说...

我是电子信息工程的, 我的课程有什么问题呢? 从现代的主流的开发流程来看, 四年下来, 我应该是学习不到:

- python
- git
- 现代代码编辑工具 - vscode, vs, jetbrains系列. 虽然大多都是古老的vc6.0, dev c++,但是说实话其实学习内容上是够用的. 但是否也反映出学习内容上的不足呢...?
- docker 更是不用说啦
- linux虽然接触了, 但是也仅仅是入门, 学习了很多命令. 这个还算可以吧.

	可能因为我是电子信息, 所以其实没有上面这些篇软件的内容.但是我感觉, 上面这些对于我们专业,还是非常重要的.



我真的感谢当时选修了一门爬虫, 我学到了很多很多知识. 我知道了原来pycharm原来不读 p y charm, python可以用pip安装工具,并且-i可以换源. 接触到了requests, http, bs4, scrapy等等. 那门课大家都很有热情,老师虽然并不是大牛,也没什么极客范,就是一个看上去平平无奇的女老师, 但是教我们完全是overqualified.

嗯...细细回味一下, 刘老师其实很会教学生. 不论是很有基础的,还是比较新手的, 刘老师都会尽力照顾到我们. 我们哪里做的不错, 他都会挑出来表扬一番. 这么说来, 她其实有中学老师的影子. 不过她的课程也比较有意思, 学生们都乐在其中, 不像有的课程, 虽然老师讲的也不错, 但是大家都坐在最后排.

