
1. 简历内容准备
2. rag方向准备
3. jd内容准备（貌似没有什么能准备的
4. leetcode准备


### CEE loss计算
#### 单个样本

对于单个样本,其Loss为:
$$
L=-\sum_{k}t_{k}\log y_{k}
$$
其中,
$$
y_{k}=\frac{\exp(s_{k})}{\sum_{i=1}^{N} \exp(s_{i})}
$$

#### mini-batch

ok,让我们考虑一下minibatch的情况.假设一共N笔数据,那么第n个样本的交叉熵损失:
$$
L_{n}=-\sum_{k}t_{nk}\log y_{nk}
$$
所有样本的交叉熵损失和:
$$
\sum_{n}Ln=-\sum_{n=1}^{N} \sum_{k=1}^{K} t_{nk}\log y_{nk}
$$
归一化损失:
$$
L=\frac{1}{N}\sum_{n=1}^{N} L_{n}=-\frac{1}{N}\sum_{n=1}^{N} \sum_{k=1}^{K} t_{nk}\log y_{nk}
$$

### Softmax with Loss层

输入 $\mathbf{x}$ 为最后一层Affine的打分.输出 $Loss$.

$$
\mathbf{y}=Softmax(\mathbf{x})
$$

$$
Loss=CrossEntropyErr(\mathbf{y},\mathbf{t})
$$