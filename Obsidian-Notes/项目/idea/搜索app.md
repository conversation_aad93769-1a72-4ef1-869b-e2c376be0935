
# 核心功能

接入azure aisearch, 实现搜索，修改，删除等操作

# 实现方法

1. cs 架构，后端python fastapi, 前端flutter.

# 后端

fastapi的pydantic..

处理post请求的请求体中的json数据有几种方法？

1. 使用pydantic模型
2. 定义函数的时候直接声明成字典，对应的变量就是json内容
3. 定义函数的时候声明成Request，然后获得其中的body等内容

使用pydantic好像是有点作用的，它可以帮你判断传递过来的参数是不是合法的，有效的。

对于search接口，传递到后端的参数有：

```python
# 选择的字段
# 过滤条件...这个...
# 自定义过滤字段

search(
*,
select : List[str] = [x,x,x,x,x,x,x],
search_content: str = '*',
filter_a: List[str] = [],
filter_b: List[xx] = [],# 各种filter
custom_filter_expression: str = '',
search_field: List[str] = '*',
):
```

pydantic就按照这套来验证就OK了。验证后的数据如果还有问题，就是后面代码写的问题了。

