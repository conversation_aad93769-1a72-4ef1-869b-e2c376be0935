两个能用python模拟终端的代码：

```python
import os
import pty
import tty
import select
import sys

def spawn_shell():
    # 创建子进程
    pid, fd = pty.fork()
    
    if pid == 0:
        # 子进程：替换为shell
        tty.setraw(sys.stdin.fileno())  # 设置终端为原始模式
        os.execvp("bash", ["bash"])     # 替换为bash shell
    else:
        # 父进程：处理终端I/O
        try:
            while True:
                # 使用select监听输入和输出
                rlist, _, _ = select.select([sys.stdin, fd], [], [])
                
                for ready in rlist:
                    if ready == sys.stdin:
                        # 用户输入 -> 发送到子进程
                        data = os.read(sys.stdin.fileno(), 1024)
                        os.write(fd, data)
                    else:
                        # 子进程输出 -> 打印到标准输出
                        data = os.read(fd, 1024)
                        if not data:  # EOF
                            raise KeyboardInterrupt
                        os.write(sys.stdout.fileno(), data)
        except (KeyboardInterrupt, OSError):
            print("\nSession terminated")

if __name__ == "__main__":
    spawn_shell()
```

```python
import os
import pty
import tty
import select
import sys

def spawn_shell():
    # 创建子进程
    pid, fd = pty.fork()
    
    if pid == 0:
        # 子进程：替换为shell
        tty.setraw(sys.stdin.fileno())  # 设置终端为原始模式
        os.execvp("bash", ["bash"])     # 替换为bash shell
    else:
        # 父进程：处理终端I/O
        try:
            while True:
                # 使用select监听输入和输出
                rlist, _, _ = select.select([sys.stdin, fd], [], [])
                
                for ready in rlist:
                    if ready == sys.stdin:
                        # 用户输入 -> 发送到子进程
                        data = os.read(sys.stdin.fileno(), 1024)
                        os.write(fd, data)
                    else:
                        # 子进程输出 -> 打印到标准输出
                        data = os.read(fd, 1024)
                        if not data:  # EOF
                            raise KeyboardInterrupt
                        os.write(sys.stdout.fileno(), data)
        except (KeyboardInterrupt, OSError):
            print("\nSession terminated")

if __name__ == "__main__":
    spawn_shell()
```


pty的创建过程，对着spawn抄
如何用os读写普通文件
如何用os读写stdin out err
