<svg aria-roledescription="class" role="graphics-document document" viewBox="0.000030517578125 0.0000152587890625 4800.72265625 1668.0003662109375" style="max-width: 4800.72265625px;" class="classDiagram" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6"><style>#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .error-icon{fill:#a44141;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .edge-thickness-normal{stroke-width:1px;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .marker.cross{stroke:lightgrey;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 p{margin:0;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 g.classGroup text{fill:#ccc;stroke:none;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:10px;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 g.classGroup text .title{font-weight:bolder;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .nodeLabel,#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .edgeLabel{color:#e0dfdf;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .edgeLabel .label rect{fill:#1f2020;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .label text{fill:#e0dfdf;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .labelBkg{background:#1f2020;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .edgeLabel .label span{background:#1f2020;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .classTitle{font-weight:bolder;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .node rect,#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .node circle,#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .node ellipse,#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .node polygon,#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .divider{stroke:#ccc;stroke-width:1;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 g.clickable{cursor:pointer;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 g.classGroup rect{fill:#1f2020;stroke:#ccc;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 g.classGroup line{stroke:#ccc;stroke-width:1;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .classLabel .box{stroke:none;stroke-width:0;fill:#1f2020;opacity:0.5;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .classLabel .label{fill:#ccc;font-size:10px;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .relation{stroke:lightgrey;stroke-width:1;fill:none;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .dashed-line{stroke-dasharray:3;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .dotted-line{stroke-dasharray:1 2;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 #compositionStart,#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .composition{fill:lightgrey!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 #compositionEnd,#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .composition{fill:lightgrey!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 #dependencyStart,#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .dependency{fill:lightgrey!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 #dependencyStart,#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .dependency{fill:lightgrey!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 #extensionStart,#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .extension{fill:transparent!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 #extensionEnd,#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .extension{fill:transparent!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 #aggregationStart,#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .aggregation{fill:transparent!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 #aggregationEnd,#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .aggregation{fill:transparent!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 #lollipopStart,#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .lollipop{fill:#1f2020!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 #lollipopEnd,#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .lollipop{fill:#1f2020!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .edgeTerminals{font-size:11px;line-height:initial;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 .classTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker aggregation class" id="mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-aggregationStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker aggregation class" id="mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-aggregationEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker extension class" id="mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-extensionStart"><path d="M 1,7 L18,13 V 1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker extension class" id="mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-extensionEnd"><path d="M 1,1 V 13 L18,7 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker composition class" id="mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-compositionStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker composition class" id="mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-compositionEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="6" class="marker dependency class" id="mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-dependencyStart"><path d="M 5,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="13" class="marker dependency class" id="mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-dependencyEnd"><path d="M 18,7 L9,13 L14,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="13" class="marker lollipop class" id="mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-lollipopStart"><circle r="6" cy="7" cx="7" fill="transparent" stroke="black"></circle></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="1" class="marker lollipop class" id="mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-lollipopEnd"><circle r="6" cy="7" cx="7" fill="transparent" stroke="black"></circle></marker></defs><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_PipelineFactory_Pipeline_1" d="M1159.259,284L1184.7,296.167C1210.142,308.333,1261.025,332.667,1286.467,360.5C1311.909,388.333,1311.909,419.667,1311.909,435.333L1311.909,451"></path><path marker-end="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_PipelineFactory_WorkflowFunction_2" d="M847.179,284L840.98,296.167C834.78,308.333,822.381,332.667,816.181,375.5C809.981,418.333,809.981,479.667,809.981,541C809.981,602.333,809.981,663.667,819.103,711.121C828.225,758.576,846.468,792.152,855.59,808.94L864.711,825.728"></path><path marker-start="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_EntityExtractStrategy_GraphIntelligenceStrategy_3" d="M2291.674,257L2291.674,273.667C2291.674,290.333,2291.674,323.667,2291.674,360.5C2291.674,397.333,2291.674,437.667,2291.674,457.833L2291.674,478"></path><path marker-end="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_GraphIntelligenceStrategy_GraphExtractor_4" d="M2291.674,604L2291.674,624.167C2291.674,644.333,2291.674,684.667,2291.674,714C2291.674,743.333,2291.674,761.667,2291.674,770.833L2291.674,780"></path><path marker-end="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_GraphExtractor_GraphExtractionResult_5" d="M2571.981,1026L2595.729,1036.167C2619.477,1046.333,2666.974,1066.667,2690.722,1092.5C2714.47,1118.333,2714.47,1149.667,2714.47,1165.333L2714.47,1181"></path><path marker-end="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Pipeline_WorkflowFunction_6" d="M1311.909,625L1311.909,641.667C1311.909,658.333,1311.909,691.667,1273.429,725.591C1234.95,759.515,1157.991,794.03,1119.511,811.287L1081.032,828.545"></path><path marker-end="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_WorkflowFunction_WorkflowCallbacks_7" d="M908.327,981L908.327,998.667C908.327,1016.333,908.327,1051.667,909.998,1074.548C911.668,1097.429,915.009,1107.858,916.68,1113.072L918.35,1118.286"></path><path marker-start="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_WorkflowCallbacks_FileWorkflowCallbacks_8" d="M679.5,1344.402L631.514,1358.835C583.529,1373.268,487.559,1402.134,439.574,1422.734C391.589,1443.334,391.589,1455.667,391.589,1461.834L391.589,1468"></path><path marker-start="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_WorkflowCallbacks_ConsoleWorkflowCallbacks_9" d="M993.799,1411.654L994.44,1414.879C995.082,1418.103,996.365,1424.552,997.006,1435.443C997.647,1446.334,997.647,1461.667,997.647,1469.334L997.647,1477"></path><path marker-start="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_WorkflowCallbacks_ProgressWorkflowCallbacks_10" d="M1247.398,1342.25L1297.853,1357.041C1348.309,1371.833,1449.219,1401.417,1499.674,1422.375C1550.13,1443.334,1550.13,1455.667,1550.13,1461.834L1550.13,1468"></path><path marker-end="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_WorkflowCallbacksManager_WorkflowCallbacks_11" d="M1611.64,1026L1611.64,1036.167C1611.64,1046.333,1611.64,1066.667,1549.02,1093.449C1486.401,1120.232,1361.163,1153.463,1298.544,1170.079L1235.924,1186.695"></path><path marker-start="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_PipelineStorage_FilePipelineStorage_12" d="M3175.693,627.057L3131.711,643.381C3087.729,659.705,2999.765,692.352,2955.783,714.843C2911.8,737.333,2911.8,749.667,2911.8,755.833L2911.8,762"></path><path marker-start="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_PipelineStorage_BlobPipelineStorage_13" d="M3407.559,706L3407.559,709.167C3407.559,712.333,3407.559,718.667,3407.559,730C3407.559,741.333,3407.559,757.667,3407.559,765.833L3407.559,774"></path><path marker-start="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_PipelineStorage_MemoryPipelineStorage_14" d="M3639.46,625.628L3684.844,642.19C3730.228,658.752,3820.996,691.876,3866.381,720.605C3911.765,749.333,3911.765,773.667,3911.765,785.833L3911.765,798"></path><path marker-end="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_StorageFactory_PipelineStorage_15" d="M3407.559,260L3407.559,276.167C3407.559,292.333,3407.559,324.667,3407.559,346C3407.559,367.333,3407.559,377.667,3407.559,382.833L3407.559,388"></path><path marker-end="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_ModelManager_ModelFactory_16" d="M4426.535,320L4426.535,326.167C4426.535,332.333,4426.535,344.667,4426.535,358.5C4426.535,372.333,4426.535,387.667,4426.535,395.333L4426.535,403"></path><path marker-end="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_ModelFactory_ChatModel_17" d="M4296.29,673L4287.739,681.667C4279.187,690.333,4262.084,707.667,4253.533,738.5C4244.981,769.333,4244.981,813.667,4244.981,835.833L4244.981,858"></path><path marker-end="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_ModelFactory_EmbeddingModel_18" d="M4426.535,673L4426.535,681.667C4426.535,690.333,4426.535,707.667,4426.535,738.5C4426.535,769.333,4426.535,813.667,4426.535,835.833L4426.535,858"></path><path marker-end="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_GraphRagConfig_ExtractGraphConfig_19" d="M327.268,284L327.268,296.167C327.268,308.333,327.268,332.667,327.268,354.5C327.268,376.333,327.268,395.667,327.268,405.333L327.268,415"></path><path marker-end="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_ExtractGraphConfig_ExtractEntityStrategyType_20" d="M327.268,661L327.268,671.667C327.268,682.333,327.268,703.667,327.268,729.5C327.268,755.333,327.268,785.667,327.268,800.833L327.268,816"></path><path marker-end="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_GraphExtractor_Document_21" d="M2093.092,1026L2076.268,1036.167C2059.444,1046.333,2025.795,1066.667,2008.971,1092.5C1992.146,1118.333,1992.146,1149.667,1992.146,1165.333L1992.146,1181"></path><path marker-end="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_GraphExtractor_EntityExtractionResult_22" d="M2300.893,1026L2301.674,1036.167C2302.455,1046.333,2304.017,1066.667,2304.798,1090.5C2305.579,1114.333,2305.579,1141.667,2305.579,1155.333L2305.579,1169"></path><path marker-end="url(#mermaid-d343c95d-1d73-4c0b-b53e-a2d5c0c8c1f6_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_WorkflowCallbacks_Progress_23" d="M1230.125,1306.58L1346.358,1327.317C1462.591,1348.053,1695.057,1389.527,1811.29,1415.43C1927.522,1441.334,1927.522,1451.667,1927.522,1456.834L1927.522,1462"></path></g><g class="edgeLabels"><g transform="translate(1311.9085807800293, 357.00006198883057)" class="edgeLabel"><g transform="translate(-26.236608505249023, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="52.47321701049805"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>creates</p></span></div></foreignObject></g></g><g transform="translate(809.9811000823975, 541.000093460083)" class="edgeLabel"><g transform="translate(-32.91071701049805, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="65.8214340209961"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>manages</p></span></div></foreignObject></g></g><g transform="translate(2291.6742515563965, 357.00006198883057)" class="edgeLabel"><g transform="translate(-40.90625, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="81.8125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>implements</p></span></div></foreignObject></g></g><g transform="translate(2291.6742515563965, 725.0001249313354)" class="edgeLabel"><g transform="translate(-16.90178680419922, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="33.80357360839844"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>uses</p></span></div></foreignObject></g></g><g transform="translate(2714.470027923584, 1087.0001573562622)" class="edgeLabel"><g transform="translate(-32.91071701049805, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="65.8214340209961"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>produces</p></span></div></foreignObject></g></g><g transform="translate(1311.9085807800293, 725.0001249313354)" class="edgeLabel"><g transform="translate(-32.02232360839844, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="64.04464721679688"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>executes</p></span></div></foreignObject></g></g><g transform="translate(908.327091217041, 1087.0001573562622)" class="edgeLabel"><g transform="translate(-25.348215103149414, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="50.69643020629883"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>notifies</p></span></div></foreignObject></g></g><g transform="translate(391.5893211364746, 1431.000189781189)" class="edgeLabel"><g transform="translate(-40.90625, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="81.8125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>implements</p></span></div></foreignObject></g></g><g transform="translate(997.6473960876465, 1431.000189781189)" class="edgeLabel"><g transform="translate(-40.90625, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="81.8125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>implements</p></span></div></foreignObject></g></g><g transform="translate(1550.1295585632324, 1431.000189781189)" class="edgeLabel"><g transform="translate(-40.90625, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="81.8125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>implements</p></span></div></foreignObject></g></g><g transform="translate(1611.6396217346191, 1087.0001573562622)" class="edgeLabel"><g transform="translate(-32.91071701049805, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="65.8214340209961"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>manages</p></span></div></foreignObject></g></g><g transform="translate(2911.800395965576, 725.0001249313354)" class="edgeLabel"><g transform="translate(-40.90625, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="81.8125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>implements</p></span></div></foreignObject></g></g><g transform="translate(3407.559383392334, 725.0001249313354)" class="edgeLabel"><g transform="translate(-40.90625, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="81.8125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>implements</p></span></div></foreignObject></g></g><g transform="translate(3911.7647972106934, 725.0001249313354)" class="edgeLabel"><g transform="translate(-40.90625, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="81.8125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>implements</p></span></div></foreignObject></g></g><g transform="translate(3407.559383392334, 357.00006198883057)" class="edgeLabel"><g transform="translate(-26.236608505249023, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="52.47321701049805"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>creates</p></span></div></foreignObject></g></g><g transform="translate(4426.534923553467, 357.00006198883057)" class="edgeLabel"><g transform="translate(-16.90178680419922, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="33.80357360839844"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>uses</p></span></div></foreignObject></g></g><g transform="translate(4244.981346130371, 725.0001249313354)" class="edgeLabel"><g transform="translate(-26.236608505249023, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="52.47321701049805"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>creates</p></span></div></foreignObject></g></g><g transform="translate(4426.534923553467, 725.0001249313354)" class="edgeLabel"><g transform="translate(-26.236608505249023, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="52.47321701049805"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>creates</p></span></div></foreignObject></g></g><g transform="translate(327.26788330078125, 357.00006198883057)" class="edgeLabel"><g transform="translate(-29.799108505249023, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="59.59821701049805"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>contains</p></span></div></foreignObject></g></g><g transform="translate(327.26788330078125, 725.0001249313354)" class="edgeLabel"><g transform="translate(-16.90178680419922, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="33.80357360839844"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>uses</p></span></div></foreignObject></g></g><g transform="translate(1992.146327972412, 1087.0001573562622)" class="edgeLabel"><g transform="translate(-36.46428680419922, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="72.92857360839844"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>processes</p></span></div></foreignObject></g></g><g transform="translate(2305.579372406006, 1087.0001573562622)" class="edgeLabel"><g transform="translate(-32.91071701049805, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="65.8214340209961"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>produces</p></span></div></foreignObject></g></g><g transform="translate(1927.5224266052246, 1431.000189781189)" class="edgeLabel"><g transform="translate(-24.90178680419922, -12.000000953674316)" class="label"><foreignObject height="24.000001907348633" width="49.80357360839844"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>reports</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(908.327091217041, 164.00003051757812)" id="classId-PipelineFactory-192" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-319.51568603515625 -120.0000228881836 L319.51568603515625 -120.0000228881836 L319.51568603515625 120.0000228881836 L-319.51568603515625 120.0000228881836"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-319.51568603515625 -120.0000228881836 C-181.4359656103229 -120.0000228881836, -43.35624518548957 -120.0000228881836, 319.51568603515625 -120.0000228881836 M-319.51568603515625 -120.0000228881836 C-112.16460930225824 -120.0000228881836, 95.18646743063977 -120.0000228881836, 319.51568603515625 -120.0000228881836 M319.51568603515625 -120.0000228881836 C319.51568603515625 -25.06950889205818, 319.51568603515625 69.86100510406723, 319.51568603515625 120.0000228881836 M319.51568603515625 -120.0000228881836 C319.51568603515625 -57.045756509468966, 319.51568603515625 5.908509869245663, 319.51568603515625 120.0000228881836 M319.51568603515625 120.0000228881836 C163.25513836555595 120.0000228881836, 6.994590695955651 120.0000228881836, -319.51568603515625 120.0000228881836 M319.51568603515625 120.0000228881836 C70.70015211851543 120.0000228881836, -178.11538179812538 120.0000228881836, -319.51568603515625 120.0000228881836 M-319.51568603515625 120.0000228881836 C-319.51568603515625 54.75303325128135, -319.51568603515625 -10.49395638562089, -319.51568603515625 -120.0000228881836 M-319.51568603515625 120.0000228881836 C-319.51568603515625 57.47117206829413, -319.51568603515625 -5.057678751595333, -319.51568603515625 -120.0000228881836"></path></g><g transform="translate(-40.92411422729492, -96.0000228881836)" class="annotation-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="81.84822082519531"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«singleton»</p></span></div></foreignObject></g></g><g transform="translate(-59.57590103149414, -72.00001907348633)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="119.15179443359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 165px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>PipelineFactory</p></span></div></foreignObject></g></g><g transform="translate(-307.51568603515625, -24.000015258789062)" class="members-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="274.90179443359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 350px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+workflows: dict[str, WorkflowFunction]</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="187.18751525878906"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 257px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+pipelines: dict[str, list[str]]</p></span></div></foreignObject></g></g><g transform="translate(-307.51568603515625, 47.99999237060547)" class="methods-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="346.0357360839844"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 424px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+register(name: str, workflow: WorkflowFunction)</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="337.4464416503906"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 415px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+register_pipeline(name: str, workflows: list[str])</p></span></div></foreignObject></g><g transform="translate(0,36.00000286102295)" style="" class="label"><foreignObject height="24.000001907348633" width="555.4553833007812"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 642px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+create_pipeline(config: GraphRagConfig, method: IndexingMethod) : Pipeline</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-319.51568603515625 -48.00001525878906 C-121.58078101805577 -48.00001525878906, 76.3541239990447 -48.00001525878906, 319.51568603515625 -48.00001525878906 M-319.51568603515625 -48.00001525878906 C-119.97549638555432 -48.00001525878906, 79.5646932640476 -48.00001525878906, 319.51568603515625 -48.00001525878906"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-319.51568603515625 23.99999237060547 C-111.93344845261646 23.99999237060547, 95.64878912992333 23.99999237060547, 319.51568603515625 23.99999237060547 M-319.51568603515625 23.99999237060547 C-67.22102584801405 23.99999237060547, 185.07363433912815 23.99999237060547, 319.51568603515625 23.99999237060547"></path></g></g><g transform="translate(327.26788330078125, 906.0001411437988)" id="classId-ExtractEntityStrategyType-193" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-131.33038330078125 -84.0000114440918 L131.33038330078125 -84.0000114440918 L131.33038330078125 84.0000114440918 L-131.33038330078125 84.0000114440918"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-131.33038330078125 -84.0000114440918 C-37.63884346921027 -84.0000114440918, 56.052696362360706 -84.0000114440918, 131.33038330078125 -84.0000114440918 M-131.33038330078125 -84.0000114440918 C-34.71675999999242 -84.0000114440918, 61.89686330079641 -84.0000114440918, 131.33038330078125 -84.0000114440918 M131.33038330078125 -84.0000114440918 C131.33038330078125 -25.93911308902652, 131.33038330078125 32.121785266038756, 131.33038330078125 84.0000114440918 M131.33038330078125 -84.0000114440918 C131.33038330078125 -39.932630348143654, 131.33038330078125 4.134750747804489, 131.33038330078125 84.0000114440918 M131.33038330078125 84.0000114440918 C56.30115372875336 84.0000114440918, -18.728075843274524 84.0000114440918, -131.33038330078125 84.0000114440918 M131.33038330078125 84.0000114440918 C37.605333955677224 84.0000114440918, -56.1197153894268 84.0000114440918, -131.33038330078125 84.0000114440918 M-131.33038330078125 84.0000114440918 C-131.33038330078125 20.87416692444794, -131.33038330078125 -42.251677595195915, -131.33038330078125 -84.0000114440918 M-131.33038330078125 84.0000114440918 C-131.33038330078125 50.11240357292677, -131.33038330078125 16.22479570176175, -131.33038330078125 -84.0000114440918"></path></g><g transform="translate(-53.37500762939453, -60.0000114440918)" class="annotation-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="106.75000762939453"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 164px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«enumeration»</p></span></div></foreignObject></g></g><g transform="translate(-99.43751525878906, -36.00000762939453)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="198.87501525878906"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 243px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>ExtractEntityStrategyType</p></span></div></foreignObject></g></g><g transform="translate(-119.33038330078125, 11.999996185302734)" class="members-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="139.2232208251953"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 204px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+graph_intelligence</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="34.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 93px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+nltk</p></span></div></foreignObject></g></g><g transform="translate(-119.33038330078125, 84.00000381469727)" class="methods-group text"></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-131.33038330078125 -12.000003814697266 C-33.35067242741523 -12.000003814697266, 64.62903844595078 -12.000003814697266, 131.33038330078125 -12.000003814697266 M-131.33038330078125 -12.000003814697266 C-36.69611108049462 -12.000003814697266, 57.938161139792015 -12.000003814697266, 131.33038330078125 -12.000003814697266"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-131.33038330078125 60.000003814697266 C-70.08035965739667 60.000003814697266, -8.83033601401209 60.000003814697266, 131.33038330078125 60.000003814697266 M-131.33038330078125 60.000003814697266 C-78.19418665394323 60.000003814697266, -25.057990007105204 60.000003814697266, 131.33038330078125 60.000003814697266"></path></g></g><g transform="translate(2291.6742515563965, 164.00003051757812)" id="classId-EntityExtractStrategy-194" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-610.7745361328125 -75.00000762939453 L610.7745361328125 -75.00000762939453 L610.7745361328125 75.00000762939453 L-610.7745361328125 75.00000762939453"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-610.7745361328125 -75.00000762939453 C-292.44781091974403 -75.00000762939453, 25.878914293324442 -75.00000762939453, 610.7745361328125 -75.00000762939453 M-610.7745361328125 -75.00000762939453 C-209.3630188973479 -75.00000762939453, 192.0484983381167 -75.00000762939453, 610.7745361328125 -75.00000762939453 M610.7745361328125 -75.00000762939453 C610.7745361328125 -30.696865907365456, 610.7745361328125 13.606275814663618, 610.7745361328125 75.00000762939453 M610.7745361328125 -75.00000762939453 C610.7745361328125 -44.26658198534494, 610.7745361328125 -13.533156341295353, 610.7745361328125 75.00000762939453 M610.7745361328125 75.00000762939453 C314.463838581438 75.00000762939453, 18.153141030063466 75.00000762939453, -610.7745361328125 75.00000762939453 M610.7745361328125 75.00000762939453 C297.1079505237782 75.00000762939453, -16.558635085256128 75.00000762939453, -610.7745361328125 75.00000762939453 M-610.7745361328125 75.00000762939453 C-610.7745361328125 37.1818884059244, -610.7745361328125 -0.6362308175457372, -610.7745361328125 -75.00000762939453 M-610.7745361328125 75.00000762939453 C-610.7745361328125 26.789951612660488, -610.7745361328125 -21.420104404073555, -610.7745361328125 -75.00000762939453"></path></g><g transform="translate(-39.58482360839844, -51.00000762939453)" class="annotation-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="79.16964721679688"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 133px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«interface»</p></span></div></foreignObject></g></g><g transform="translate(-81.36161041259766, -27.000003814697266)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="162.7232208251953"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 208px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>EntityExtractStrategy</p></span></div></foreignObject></g></g><g transform="translate(-598.7745361328125, 21)" class="members-group text"></g><g transform="translate(-598.7745361328125, 51)" class="methods-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="1116.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 1217px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+extract(docs: list[Document], entity_types: EntityTypes, callbacks: WorkflowCallbacks, cache: PipelineCache, config: StrategyConfig) : EntityExtractionResult</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-610.7745361328125 -3 C-239.88992755710467 -3, 130.99468101860316 -3, 610.7745361328125 -3 M-610.7745361328125 -3 C-202.44535582785443 -3, 205.88382447710364 -3, 610.7745361328125 -3"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-610.7745361328125 21 C-189.34075272021687 21, 232.09303069237876 21, 610.7745361328125 21 M-610.7745361328125 21 C-134.69641903245997 21, 341.38169806789256 21, 610.7745361328125 21"></path></g></g><g transform="translate(2291.6742515563965, 541.000093460083)" id="classId-GraphIntelligenceStrategy-195" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-384.8504638671875 -63 L384.8504638671875 -63 L384.8504638671875 63 L-384.8504638671875 63"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-384.8504638671875 -63 C-131.35741706778455 -63, 122.1356297316184 -63, 384.8504638671875 -63 M-384.8504638671875 -63 C-175.54731754074325 -63, 33.755828785701 -63, 384.8504638671875 -63 M384.8504638671875 -63 C384.8504638671875 -35.943508756031854, 384.8504638671875 -8.887017512063707, 384.8504638671875 63 M384.8504638671875 -63 C384.8504638671875 -22.260185638076365, 384.8504638671875 18.47962872384727, 384.8504638671875 63 M384.8504638671875 63 C216.09315534773245 63, 47.3358468282774 63, -384.8504638671875 63 M384.8504638671875 63 C144.08092507965162 63, -96.68861370788426 63, -384.8504638671875 63 M-384.8504638671875 63 C-384.8504638671875 18.26748782790427, -384.8504638671875 -26.465024344191463, -384.8504638671875 -63 M-384.8504638671875 63 C-384.8504638671875 22.88421419816487, -384.8504638671875 -17.23157160367026, -384.8504638671875 -63"></path></g><g transform="translate(0, -39)" class="annotation-group text"></g><g transform="translate(-99.58482360839844, -39)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="199.16964721679688"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 248px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>GraphIntelligenceStrategy</p></span></div></foreignObject></g></g><g transform="translate(-372.8504638671875, 9.000003814697266)" class="members-group text"></g><g transform="translate(-372.8504638671875, 39.000003814697266)" class="methods-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="646.1160888671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 727px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+run_graph_intelligence(docs, entity_types, callbacks, cache, args) : EntityExtractionResult</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-384.8504638671875 -14.999996185302734 C-180.26264913174052 -14.999996185302734, 24.325165603706466 -14.999996185302734, 384.8504638671875 -14.999996185302734 M-384.8504638671875 -14.999996185302734 C-121.1002089602863 -14.999996185302734, 142.6500459466149 -14.999996185302734, 384.8504638671875 -14.999996185302734"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-384.8504638671875 9.000003814697266 C-89.4663936466697 9.000003814697266, 205.9176765738481 9.000003814697266, 384.8504638671875 9.000003814697266 M-384.8504638671875 9.000003814697266 C-105.97533266874939 9.000003814697266, 172.89979852968872 9.000003814697266, 384.8504638671875 9.000003814697266"></path></g></g><g transform="translate(2291.6742515563965, 906.0001411437988)" id="classId-GraphExtractor-196" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-334.33929443359375 -120.0000228881836 L334.33929443359375 -120.0000228881836 L334.33929443359375 120.0000228881836 L-334.33929443359375 120.0000228881836"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-334.33929443359375 -120.0000228881836 C-92.63359121430378 -120.0000228881836, 149.0721120049862 -120.0000228881836, 334.33929443359375 -120.0000228881836 M-334.33929443359375 -120.0000228881836 C-93.57294717991812 -120.0000228881836, 147.19340007375752 -120.0000228881836, 334.33929443359375 -120.0000228881836 M334.33929443359375 -120.0000228881836 C334.33929443359375 -59.00987547949298, 334.33929443359375 1.9802719291976274, 334.33929443359375 120.0000228881836 M334.33929443359375 -120.0000228881836 C334.33929443359375 -43.95593645941652, 334.33929443359375 32.08814996935055, 334.33929443359375 120.0000228881836 M334.33929443359375 120.0000228881836 C87.82038294089202 120.0000228881836, -158.69852855180972 120.0000228881836, -334.33929443359375 120.0000228881836 M334.33929443359375 120.0000228881836 C128.45515814992478 120.0000228881836, -77.42897813374418 120.0000228881836, -334.33929443359375 120.0000228881836 M-334.33929443359375 120.0000228881836 C-334.33929443359375 32.07277773446178, -334.33929443359375 -55.85446741926003, -334.33929443359375 -120.0000228881836 M-334.33929443359375 120.0000228881836 C-334.33929443359375 51.479474202378825, -334.33929443359375 -17.041074483425945, -334.33929443359375 -120.0000228881836"></path></g><g transform="translate(0, -96.0000228881836)" class="annotation-group text"></g><g transform="translate(-58.68750762939453, -96.0000228881836)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="117.37500762939453"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 165px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>GraphExtractor</p></span></div></foreignObject></g></g><g transform="translate(-322.33929443359375, -48.00001907348633)" class="members-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="144.07144165039062"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 206px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-_model: ChatModel</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="168.9553680419922"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 236px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-_extraction_prompt: str</p></span></div></foreignObject></g><g transform="translate(0,36.00000286102295)" style="" class="label"><foreignObject height="24.000001907348633" width="147.6428680419922"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 209px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-_max_gleanings: int</p></span></div></foreignObject></g></g><g transform="translate(-322.33929443359375, 47.999996185302734)" class="methods-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="475.294677734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 582px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+<strong>call</strong>(texts: list[str], prompt_variables: dict) : GraphExtractionResult</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="408.1607360839844"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 482px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-_process_document(text: str, prompt_variables: dict) : str</p></span></div></foreignObject></g><g transform="translate(0,36.00000286102295)" style="" class="label"><foreignObject height="24.000001907348633" width="585.9910888671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 669px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-_process_results(results: dict, tuple_delimiter: str, record_delimiter: str) : nx.Graph</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-334.33929443359375 -72.00001907348633 C-120.31884733743132 -72.00001907348633, 93.7015997587311 -72.00001907348633, 334.33929443359375 -72.00001907348633 M-334.33929443359375 -72.00001907348633 C-166.51265640355513 -72.00001907348633, 1.3139816264834963 -72.00001907348633, 334.33929443359375 -72.00001907348633"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-334.33929443359375 23.999996185302734 C-108.79376521576634 23.999996185302734, 116.75176400206107 23.999996185302734, 334.33929443359375 23.999996185302734 M-334.33929443359375 23.999996185302734 C-171.50100607434618 23.999996185302734, -8.662717715098609 23.999996185302734, 334.33929443359375 23.999996185302734"></path></g></g><g transform="translate(1311.9085807800293, 541.000093460083)" id="classId-Pipeline-197" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-128.78126525878906 -84.00001525878906 L128.78126525878906 -84.00001525878906 L128.78126525878906 84.00001525878906 L-128.78126525878906 84.00001525878906"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-128.78126525878906 -84.00001525878906 C-30.67388149480118 -84.00001525878906, 67.4335022691867 -84.00001525878906, 128.78126525878906 -84.00001525878906 M-128.78126525878906 -84.00001525878906 C-70.86885776236073 -84.00001525878906, -12.95645026593239 -84.00001525878906, 128.78126525878906 -84.00001525878906 M128.78126525878906 -84.00001525878906 C128.78126525878906 -46.318425436227294, 128.78126525878906 -8.636835613665525, 128.78126525878906 84.00001525878906 M128.78126525878906 -84.00001525878906 C128.78126525878906 -22.772892455916555, 128.78126525878906 38.45423034695595, 128.78126525878906 84.00001525878906 M128.78126525878906 84.00001525878906 C27.254117439063023 84.00001525878906, -74.27303038066302 84.00001525878906, -128.78126525878906 84.00001525878906 M128.78126525878906 84.00001525878906 C62.08088890207311 84.00001525878906, -4.619487454642837 84.00001525878906, -128.78126525878906 84.00001525878906 M-128.78126525878906 84.00001525878906 C-128.78126525878906 28.941442245987247, -128.78126525878906 -26.11713076681457, -128.78126525878906 -84.00001525878906 M-128.78126525878906 84.00001525878906 C-128.78126525878906 38.30659230206558, -128.78126525878906 -7.386830654657899, -128.78126525878906 -84.00001525878906"></path></g><g transform="translate(0, -60.00001525878906)" class="annotation-group text"></g><g transform="translate(-30.67857551574707, -60.00001525878906)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="61.357147216796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 110px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Pipeline</p></span></div></foreignObject></g></g><g transform="translate(-116.78126525878906, -12.000011444091797)" class="members-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="178.3928680419922"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 249px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-workflows: list[Workflow]</p></span></div></foreignObject></g></g><g transform="translate(-116.78126525878906, 35.99999237060547)" class="methods-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="202.88394165039062"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 272px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+run() : Generator[Workflow]</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="127.58036041259766"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 190px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+names() : list[str]</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-128.78126525878906 -36.0000114440918 C-52.28464373910222 -36.0000114440918, 24.21197778058462 -36.0000114440918, 128.78126525878906 -36.0000114440918 M-128.78126525878906 -36.0000114440918 C-37.380779819143655 -36.0000114440918, 54.01970562050175 -36.0000114440918, 128.78126525878906 -36.0000114440918"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-128.78126525878906 11.999992370605469 C-57.92444667746862 11.999992370605469, 12.932371903851816 11.999992370605469, 128.78126525878906 11.999992370605469 M-128.78126525878906 11.999992370605469 C-75.70041978612738 11.999992370605469, -22.61957431346569 11.999992370605469, 128.78126525878906 11.999992370605469"></path></g></g><g transform="translate(908.327091217041, 906.0001411437988)" id="classId-WorkflowFunction-198" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-370.1696472167969 -75.00000762939453 L370.1696472167969 -75.00000762939453 L370.1696472167969 75.00000762939453 L-370.1696472167969 75.00000762939453"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-370.1696472167969 -75.00000762939453 C-160.2779523107503 -75.00000762939453, 49.61374259529629 -75.00000762939453, 370.1696472167969 -75.00000762939453 M-370.1696472167969 -75.00000762939453 C-134.96739349151792 -75.00000762939453, 100.23486023376103 -75.00000762939453, 370.1696472167969 -75.00000762939453 M370.1696472167969 -75.00000762939453 C370.1696472167969 -30.049631990101872, 370.1696472167969 14.900743649190787, 370.1696472167969 75.00000762939453 M370.1696472167969 -75.00000762939453 C370.1696472167969 -36.558853939609264, 370.1696472167969 1.8822997501760028, 370.1696472167969 75.00000762939453 M370.1696472167969 75.00000762939453 C188.81314362468504 75.00000762939453, 7.456640032573205 75.00000762939453, -370.1696472167969 75.00000762939453 M370.1696472167969 75.00000762939453 C205.66790805258066 75.00000762939453, 41.16616888836444 75.00000762939453, -370.1696472167969 75.00000762939453 M-370.1696472167969 75.00000762939453 C-370.1696472167969 23.643898161543937, -370.1696472167969 -27.712211306306656, -370.1696472167969 -75.00000762939453 M-370.1696472167969 75.00000762939453 C-370.1696472167969 29.796392605244876, -370.1696472167969 -15.40722241890478, -370.1696472167969 -75.00000762939453"></path></g><g transform="translate(-39.58482360839844, -51.00000762939453)" class="annotation-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="79.16964721679688"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 133px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«interface»</p></span></div></foreignObject></g></g><g transform="translate(-69.625, -27.000003814697266)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="139.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 186px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>WorkflowFunction</p></span></div></foreignObject></g></g><g transform="translate(-358.1696472167969, 21)" class="members-group text"></g><g transform="translate(-358.1696472167969, 51)" class="methods-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="646.7142944335938"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 736px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+execute(config: GraphRagConfig, context: PipelineRunContext) : WorkflowFunctionOutput</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-370.1696472167969 -3 C-193.84268901301263 -3, -17.515730809228387 -3, 370.1696472167969 -3 M-370.1696472167969 -3 C-216.01866875853025 -3, -61.86769030026363 -3, 370.1696472167969 -3"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-370.1696472167969 21 C-148.7032753779581 21, 72.76309646088066 21, 370.1696472167969 21 M-370.1696472167969 21 C-150.69372855861002 21, 68.78219009957684 21, 370.1696472167969 21"></path></g></g><g transform="translate(963.4308891296387, 1259.0001735687256)" id="classId-WorkflowCallbacks-199" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-266.6942138671875 -135.00001525878906 L266.6942138671875 -135.00001525878906 L266.6942138671875 135.00001525878906 L-266.6942138671875 135.00001525878906"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-266.6942138671875 -135.00001525878906 C-114.71650620564597 -135.00001525878906, 37.26120145589556 -135.00001525878906, 266.6942138671875 -135.00001525878906 M-266.6942138671875 -135.00001525878906 C-132.97572142612066 -135.00001525878906, 0.7427710149461859 -135.00001525878906, 266.6942138671875 -135.00001525878906 M266.6942138671875 -135.00001525878906 C266.6942138671875 -75.22757117656204, 266.6942138671875 -15.455127094334998, 266.6942138671875 135.00001525878906 M266.6942138671875 -135.00001525878906 C266.6942138671875 -78.62980325176196, 266.6942138671875 -22.259591244734835, 266.6942138671875 135.00001525878906 M266.6942138671875 135.00001525878906 C58.10685338671425 135.00001525878906, -150.480507093759 135.00001525878906, -266.6942138671875 135.00001525878906 M266.6942138671875 135.00001525878906 C95.60224680432574 135.00001525878906, -75.48972025853601 135.00001525878906, -266.6942138671875 135.00001525878906 M-266.6942138671875 135.00001525878906 C-266.6942138671875 30.860522683065668, -266.6942138671875 -73.27896989265773, -266.6942138671875 -135.00001525878906 M-266.6942138671875 135.00001525878906 C-266.6942138671875 75.67969500443921, -266.6942138671875 16.35937475008936, -266.6942138671875 -135.00001525878906"></path></g><g transform="translate(-39.58482360839844, -111.00001525878906)" class="annotation-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="79.16964721679688"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 133px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«interface»</p></span></div></foreignObject></g></g><g transform="translate(-73.20982360839844, -87.0000114440918)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="146.41964721679688"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 191px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>WorkflowCallbacks</p></span></div></foreignObject></g></g><g transform="translate(-254.6942138671875, -39.00000762939453)" class="members-group text"></g><g transform="translate(-254.6942138671875, -9.000007629394531)" class="methods-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="308.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 380px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+workflow_start(name: str, instance: object)</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="303.7054138183594"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 374px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+workflow_end(name: str, instance: object)</p></span></div></foreignObject></g><g transform="translate(0,36.00000286102295)" style="" class="label"><foreignObject height="24.000001907348633" width="217.41964721679688"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 282px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+progress(progress: Progress)</p></span></div></foreignObject></g><g transform="translate(0,60.00000476837158)" style="" class="label"><foreignObject height="24.000001907348633" width="436.1785888671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 506px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+error(message: str, cause: Exception, stack: str, details: dict)</p></span></div></foreignObject></g><g transform="translate(0,84.00000667572021)" style="" class="label"><foreignObject height="24.000001907348633" width="255.66964721679688"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 324px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+warning(message: str, details: dict)</p></span></div></foreignObject></g><g transform="translate(0,108.00000858306885)" style="" class="label"><foreignObject height="24.000001907348633" width="220.9910888671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 285px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+log(message: str, details: dict)</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-266.6942138671875 -63.00000762939453 C-116.73886213219666 -63.00000762939453, 33.216489602794184 -63.00000762939453, 266.6942138671875 -63.00000762939453 M-266.6942138671875 -63.00000762939453 C-93.53844764515924 -63.00000762939453, 79.61731857686902 -63.00000762939453, 266.6942138671875 -63.00000762939453"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-266.6942138671875 -39.00000762939453 C-121.38272150438462 -39.00000762939453, 23.92877085841826 -39.00000762939453, 266.6942138671875 -39.00000762939453 M-266.6942138671875 -39.00000762939453 C-124.66714215863936 -39.00000762939453, 17.35992954990877 -39.00000762939453, 266.6942138671875 -39.00000762939453"></path></g></g><g transform="translate(1611.6396217346191, 906.0001411437988)" id="classId-WorkflowCallbacksManager-200" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-283.14288330078125 -120.00001525878906 L283.14288330078125 -120.00001525878906 L283.14288330078125 120.00001525878906 L-283.14288330078125 120.00001525878906"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-283.14288330078125 -120.00001525878906 C-120.69580494053656 -120.00001525878906, 41.75127341970813 -120.00001525878906, 283.14288330078125 -120.00001525878906 M-283.14288330078125 -120.00001525878906 C-112.623624586262 -120.00001525878906, 57.89563412825726 -120.00001525878906, 283.14288330078125 -120.00001525878906 M283.14288330078125 -120.00001525878906 C283.14288330078125 -59.994388372097426, 283.14288330078125 0.011238514594211324, 283.14288330078125 120.00001525878906 M283.14288330078125 -120.00001525878906 C283.14288330078125 -42.98574971534836, 283.14288330078125 34.02851582809234, 283.14288330078125 120.00001525878906 M283.14288330078125 120.00001525878906 C150.95120956324448 120.00001525878906, 18.7595358257077 120.00001525878906, -283.14288330078125 120.00001525878906 M283.14288330078125 120.00001525878906 C133.70345425795946 120.00001525878906, -15.735974784862321 120.00001525878906, -283.14288330078125 120.00001525878906 M-283.14288330078125 120.00001525878906 C-283.14288330078125 45.689957755152946, -283.14288330078125 -28.62009974848317, -283.14288330078125 -120.00001525878906 M-283.14288330078125 120.00001525878906 C-283.14288330078125 57.296960003871455, -283.14288330078125 -5.406095251046153, -283.14288330078125 -120.00001525878906"></path></g><g transform="translate(0, -96.00001525878906)" class="annotation-group text"></g><g transform="translate(-106.10714721679688, -96.00001525878906)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="212.21429443359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 259px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>WorkflowCallbacksManager</p></span></div></foreignObject></g></g><g transform="translate(-271.14288330078125, -48.0000114440918)" class="members-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="252.2232208251953"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 318px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-_callbacks: list[WorkflowCallbacks]</p></span></div></foreignObject></g></g><g transform="translate(-271.14288330078125, -0.00000762939453125)" class="methods-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="282.90179443359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 350px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+register(callbacks: WorkflowCallbacks)</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="308.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 380px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+workflow_start(name: str, instance: object)</p></span></div></foreignObject></g><g transform="translate(0,36.00000286102295)" style="" class="label"><foreignObject height="24.000001907348633" width="303.7054138183594"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 374px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+workflow_end(name: str, instance: object)</p></span></div></foreignObject></g><g transform="translate(0,60.00000476837158)" style="" class="label"><foreignObject height="24.000001907348633" width="217.41964721679688"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 282px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+progress(progress: Progress)</p></span></div></foreignObject></g><g transform="translate(0,84.00000667572021)" style="" class="label"><foreignObject height="24.000001907348633" width="436.1785888671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 506px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+error(message: str, cause: Exception, stack: str, details: dict)</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-283.14288330078125 -72.0000114440918 C-89.60342213439 -72.0000114440918, 103.93603903200125 -72.0000114440918, 283.14288330078125 -72.0000114440918 M-283.14288330078125 -72.0000114440918 C-103.34096246481252 -72.0000114440918, 76.46095837115621 -72.0000114440918, 283.14288330078125 -72.0000114440918"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-283.14288330078125 -24.00000762939453 C-164.87700013231398 -24.00000762939453, -46.61111696384668 -24.00000762939453, 283.14288330078125 -24.00000762939453 M-283.14288330078125 -24.00000762939453 C-134.63303306676838 -24.00000762939453, 13.876817167244496 -24.00000762939453, 283.14288330078125 -24.00000762939453"></path></g></g><g transform="translate(391.5893211364746, 1564.0002059936523)" id="classId-FileWorkflowCallbacks-201" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-273.5848388671875 -96.00001525878906 L273.5848388671875 -96.00001525878906 L273.5848388671875 96.00001525878906 L-273.5848388671875 96.00001525878906"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-273.5848388671875 -96.00001525878906 C-65.76076539791893 -96.00001525878906, 142.06330807134964 -96.00001525878906, 273.5848388671875 -96.00001525878906 M-273.5848388671875 -96.00001525878906 C-103.88433284516506 -96.00001525878906, 65.81617317685738 -96.00001525878906, 273.5848388671875 -96.00001525878906 M273.5848388671875 -96.00001525878906 C273.5848388671875 -46.04672631852438, 273.5848388671875 3.9065626217403064, 273.5848388671875 96.00001525878906 M273.5848388671875 -96.00001525878906 C273.5848388671875 -51.07727018036154, 273.5848388671875 -6.154525101934013, 273.5848388671875 96.00001525878906 M273.5848388671875 96.00001525878906 C110.7471866584643 96.00001525878906, -52.0904655502589 96.00001525878906, -273.5848388671875 96.00001525878906 M273.5848388671875 96.00001525878906 C154.01732157636746 96.00001525878906, 34.4498042855474 96.00001525878906, -273.5848388671875 96.00001525878906 M-273.5848388671875 96.00001525878906 C-273.5848388671875 25.11804670616759, -273.5848388671875 -45.76392184645388, -273.5848388671875 -96.00001525878906 M-273.5848388671875 96.00001525878906 C-273.5848388671875 36.545273172145244, -273.5848388671875 -22.909468914498575, -273.5848388671875 -96.00001525878906"></path></g><g transform="translate(0, -72.00001525878906)" class="annotation-group text"></g><g transform="translate(-86.99107360839844, -72.00001525878906)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="173.98214721679688"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 217px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>FileWorkflowCallbacks</p></span></div></foreignObject></g></g><g transform="translate(-261.5848388671875, -24.000011444091797)" class="members-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="210.16964721679688"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 276px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-_out_stream: TextIOWrapper</p></span></div></foreignObject></g></g><g transform="translate(-261.5848388671875, 23.99999237060547)" class="methods-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="436.1785888671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 506px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+error(message: str, cause: Exception, stack: str, details: dict)</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="255.66964721679688"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 324px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+warning(message: str, details: dict)</p></span></div></foreignObject></g><g transform="translate(0,36.00000286102295)" style="" class="label"><foreignObject height="24.000001907348633" width="220.9910888671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 285px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+log(message: str, details: dict)</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-273.5848388671875 -48.0000114440918 C-77.44651234269568 -48.0000114440918, 118.69181418179613 -48.0000114440918, 273.5848388671875 -48.0000114440918 M-273.5848388671875 -48.0000114440918 C-137.18722040815402 -48.0000114440918, -0.7896019491205379 -48.0000114440918, 273.5848388671875 -48.0000114440918"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-273.5848388671875 -0.00000762939453125 C-106.3711836190819 -0.00000762939453125, 60.84247162902369 -0.00000762939453125, 273.5848388671875 -0.00000762939453125 M-273.5848388671875 -0.00000762939453125 C-141.32625669520883 -0.00000762939453125, -9.067674523230153 -0.00000762939453125, 273.5848388671875 -0.00000762939453125"></path></g></g><g transform="translate(997.6473960876465, 1564.0002059936523)" id="classId-ConsoleWorkflowCallbacks-202" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-282.4732360839844 -87.00000762939453 L282.4732360839844 -87.00000762939453 L282.4732360839844 87.00000762939453 L-282.4732360839844 87.00000762939453"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-282.4732360839844 -87.00000762939453 C-89.30831488287376 -87.00000762939453, 103.85660631823686 -87.00000762939453, 282.4732360839844 -87.00000762939453 M-282.4732360839844 -87.00000762939453 C-157.72187067768402 -87.00000762939453, -32.970505271383644 -87.00000762939453, 282.4732360839844 -87.00000762939453 M282.4732360839844 -87.00000762939453 C282.4732360839844 -37.653984252725245, 282.4732360839844 11.692039123944042, 282.4732360839844 87.00000762939453 M282.4732360839844 -87.00000762939453 C282.4732360839844 -50.97990828775098, 282.4732360839844 -14.95980894610743, 282.4732360839844 87.00000762939453 M282.4732360839844 87.00000762939453 C168.7647239089511 87.00000762939453, 55.05621173391785 87.00000762939453, -282.4732360839844 87.00000762939453 M282.4732360839844 87.00000762939453 C134.64010520429107 87.00000762939453, -13.193025675402225 87.00000762939453, -282.4732360839844 87.00000762939453 M-282.4732360839844 87.00000762939453 C-282.4732360839844 38.28325270580174, -282.4732360839844 -10.433502217791045, -282.4732360839844 -87.00000762939453 M-282.4732360839844 87.00000762939453 C-282.4732360839844 38.979391230677045, -282.4732360839844 -9.041225168040441, -282.4732360839844 -87.00000762939453"></path></g><g transform="translate(0, -63.00000762939453)" class="annotation-group text"></g><g transform="translate(-104.76786041259766, -63.00000762939453)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="209.5357208251953"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 251px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>ConsoleWorkflowCallbacks</p></span></div></foreignObject></g></g><g transform="translate(-270.4732360839844, -15.000003814697266)" class="members-group text"></g><g transform="translate(-270.4732360839844, 14.999996185302734)" class="methods-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="436.1785888671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 506px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+error(message: str, cause: Exception, stack: str, details: dict)</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="255.66964721679688"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 324px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+warning(message: str, details: dict)</p></span></div></foreignObject></g><g transform="translate(0,36.00000286102295)" style="" class="label"><foreignObject height="24.000001907348633" width="220.9910888671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 285px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+log(message: str, details: dict)</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-282.4732360839844 -39.000003814697266 C-69.42547324456581 -39.000003814697266, 143.62228959485276 -39.000003814697266, 282.4732360839844 -39.000003814697266 M-282.4732360839844 -39.000003814697266 C-122.11522947260119 -39.000003814697266, 38.242777138782 -39.000003814697266, 282.4732360839844 -39.000003814697266"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-282.4732360839844 -15.000003814697266 C-168.7812942605504 -15.000003814697266, -55.08935243711642 -15.000003814697266, 282.4732360839844 -15.000003814697266 M-282.4732360839844 -15.000003814697266 C-140.97858945050461 -15.000003814697266, 0.5160571829751461 -15.000003814697266, 282.4732360839844 -15.000003814697266"></path></g></g><g transform="translate(1550.1295585632324, 1564.0002059936523)" id="classId-ProgressWorkflowCallbacks-203" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-220.00892639160156 -96.00001525878906 L220.00892639160156 -96.00001525878906 L220.00892639160156 96.00001525878906 L-220.00892639160156 96.00001525878906"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-220.00892639160156 -96.00001525878906 C-109.15844777998879 -96.00001525878906, 1.6920308316239812 -96.00001525878906, 220.00892639160156 -96.00001525878906 M-220.00892639160156 -96.00001525878906 C-48.15960266793522 -96.00001525878906, 123.68972105573113 -96.00001525878906, 220.00892639160156 -96.00001525878906 M220.00892639160156 -96.00001525878906 C220.00892639160156 -57.20840896714321, 220.00892639160156 -18.41680267549735, 220.00892639160156 96.00001525878906 M220.00892639160156 -96.00001525878906 C220.00892639160156 -33.86319188068194, 220.00892639160156 28.273631497425185, 220.00892639160156 96.00001525878906 M220.00892639160156 96.00001525878906 C59.38053389830938 96.00001525878906, -101.2478585949828 96.00001525878906, -220.00892639160156 96.00001525878906 M220.00892639160156 96.00001525878906 C63.10162129145948 96.00001525878906, -93.8056838086826 96.00001525878906, -220.00892639160156 96.00001525878906 M-220.00892639160156 96.00001525878906 C-220.00892639160156 52.54939987379828, -220.00892639160156 9.098784488807496, -220.00892639160156 -96.00001525878906 M-220.00892639160156 96.00001525878906 C-220.00892639160156 50.35149686440468, -220.00892639160156 4.702978470020298, -220.00892639160156 -96.00001525878906"></path></g><g transform="translate(0, -72.00001525878906)" class="annotation-group text"></g><g transform="translate(-107.89286041259766, -72.00001525878906)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="215.7857208251953"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 257px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>ProgressWorkflowCallbacks</p></span></div></foreignObject></g></g><g transform="translate(-208.00892639160156, -24.000011444091797)" class="members-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="273.90179443359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 340px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-_progress_stack: list[ProgressLogger]</p></span></div></foreignObject></g></g><g transform="translate(-208.00892639160156, 23.99999237060547)" class="methods-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="308.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 380px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+workflow_start(name: str, instance: object)</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="303.7054138183594"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 374px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+workflow_end(name: str, instance: object)</p></span></div></foreignObject></g><g transform="translate(0,36.00000286102295)" style="" class="label"><foreignObject height="24.000001907348633" width="217.41964721679688"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 282px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+progress(progress: Progress)</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-220.00892639160156 -48.0000114440918 C-114.03509231989207 -48.0000114440918, -8.061258248182583 -48.0000114440918, 220.00892639160156 -48.0000114440918 M-220.00892639160156 -48.0000114440918 C-67.25466033022715 -48.0000114440918, 85.49960573114726 -48.0000114440918, 220.00892639160156 -48.0000114440918"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-220.00892639160156 -0.00000762939453125 C-114.39919318150928 -0.00000762939453125, -8.789459971416989 -0.00000762939453125, 220.00892639160156 -0.00000762939453125 M-220.00892639160156 -0.00000762939453125 C-107.50051079763766 -0.00000762939453125, 5.007904796326244 -0.00000762939453125, 220.00892639160156 -0.00000762939453125"></path></g></g><g transform="translate(3407.559383392334, 541.000093460083)" id="classId-PipelineStorage-204" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-214.99110412597656 -147.00003051757812 L214.99110412597656 -147.00003051757812 L214.99110412597656 147.00003051757812 L-214.99110412597656 147.00003051757812"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-214.99110412597656 -147.00003051757812 C-102.96323475700615 -147.00003051757812, 9.064634611964266 -147.00003051757812, 214.99110412597656 -147.00003051757812 M-214.99110412597656 -147.00003051757812 C-121.05493377477457 -147.00003051757812, -27.118763423572574 -147.00003051757812, 214.99110412597656 -147.00003051757812 M214.99110412597656 -147.00003051757812 C214.99110412597656 -59.7917550112289, 214.99110412597656 27.416520495120324, 214.99110412597656 147.00003051757812 M214.99110412597656 -147.00003051757812 C214.99110412597656 -59.997971034737716, 214.99110412597656 27.004088448102692, 214.99110412597656 147.00003051757812 M214.99110412597656 147.00003051757812 C56.272653151576066 147.00003051757812, -102.44579782282443 147.00003051757812, -214.99110412597656 147.00003051757812 M214.99110412597656 147.00003051757812 C63.215300517003215 147.00003051757812, -88.56050309197013 147.00003051757812, -214.99110412597656 147.00003051757812 M-214.99110412597656 147.00003051757812 C-214.99110412597656 48.0579990367378, -214.99110412597656 -50.88403244410253, -214.99110412597656 -147.00003051757812 M-214.99110412597656 147.00003051757812 C-214.99110412597656 82.3590845175505, -214.99110412597656 17.718138517522874, -214.99110412597656 -147.00003051757812"></path></g><g transform="translate(-39.58482360839844, -123.00003051757812)" class="annotation-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="79.16964721679688"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 133px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«interface»</p></span></div></foreignObject></g></g><g transform="translate(-60.46428680419922, -99.00002670288086)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="120.92857360839844"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 168px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>PipelineStorage</p></span></div></foreignObject></g></g><g transform="translate(-202.99110412597656, -51.000022888183594)" class="members-group text"></g><g transform="translate(-202.99110412597656, -21.000022888183594)" class="methods-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="345.51788330078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 413px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+get(key: str, as_bytes: bool, encoding: str) : Any</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="274.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 340px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+set(key: str, value: Any, encoding: str)</p></span></div></foreignObject></g><g transform="translate(0,36.00000286102295)" style="" class="label"><foreignObject height="24.000001907348633" width="140.94644165039062"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 201px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+has(key: str) : bool</p></span></div></foreignObject></g><g transform="translate(0,60.00000476837158)" style="" class="label"><foreignObject height="24.000001907348633" width="115.16072082519531"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 175px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+delete(key: str)</p></span></div></foreignObject></g><g transform="translate(0,84.00000667572021)" style="" class="label"><foreignObject height="24.000001907348633" width="54.687503814697266"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 111px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+clear()</p></span></div></foreignObject></g><g transform="translate(0,108.00000858306885)" style="" class="label"><foreignObject height="24.000001907348633" width="245.90179443359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 313px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+child(name: str) : PipelineStorage</p></span></div></foreignObject></g><g transform="translate(0,132.00001049041748)" style="" class="label"><foreignObject height="24.000001907348633" width="112.45536041259766"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 173px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+keys() : list[str]</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-214.99110412597656 -75.0000228881836 C-76.09270371772453 -75.0000228881836, 62.8056966905275 -75.0000228881836, 214.99110412597656 -75.0000228881836 M-214.99110412597656 -75.0000228881836 C-109.00946723357087 -75.0000228881836, -3.027830341165185 -75.0000228881836, 214.99110412597656 -75.0000228881836"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-214.99110412597656 -51.000022888183594 C-43.111563346895025 -51.000022888183594, 128.7679774321865 -51.000022888183594, 214.99110412597656 -51.000022888183594 M-214.99110412597656 -51.000022888183594 C-119.6141530899629 -51.000022888183594, -24.237202053949233 -51.000022888183594, 214.99110412597656 -51.000022888183594"></path></g></g><g transform="translate(2911.800395965576, 906.0001411437988)" id="classId-FilePipelineStorage-205" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-221.88172912597656 -144.00001525878906 L221.88172912597656 -144.00001525878906 L221.88172912597656 144.00001525878906 L-221.88172912597656 144.00001525878906"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-221.88172912597656 -144.00001525878906 C-56.88448202590297 -144.00001525878906, 108.11276507417062 -144.00001525878906, 221.88172912597656 -144.00001525878906 M-221.88172912597656 -144.00001525878906 C-82.21117013623609 -144.00001525878906, 57.45938885350438 -144.00001525878906, 221.88172912597656 -144.00001525878906 M221.88172912597656 -144.00001525878906 C221.88172912597656 -70.99504811397307, 221.88172912597656 2.009919030842923, 221.88172912597656 144.00001525878906 M221.88172912597656 -144.00001525878906 C221.88172912597656 -60.11353949668475, 221.88172912597656 23.77293626541956, 221.88172912597656 144.00001525878906 M221.88172912597656 144.00001525878906 C104.03909027369289 144.00001525878906, -13.803548578590778 144.00001525878906, -221.88172912597656 144.00001525878906 M221.88172912597656 144.00001525878906 C115.03407392272383 144.00001525878906, 8.186418719471106 144.00001525878906, -221.88172912597656 144.00001525878906 M-221.88172912597656 144.00001525878906 C-221.88172912597656 43.04498901887591, -221.88172912597656 -57.91003722103724, -221.88172912597656 -144.00001525878906 M-221.88172912597656 144.00001525878906 C-221.88172912597656 64.29345548757054, -221.88172912597656 -15.413104283647982, -221.88172912597656 -144.00001525878906"></path></g><g transform="translate(0, -120.00001525878906)" class="annotation-group text"></g><g transform="translate(-74.24553680419922, -120.00001525878906)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="148.49107360839844"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 194px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>FilePipelineStorage</p></span></div></foreignObject></g></g><g transform="translate(-209.88172912597656, -72.0000114440918)" class="members-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="95.14286041259766"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 156px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-_root_dir: str</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="105.83928680419922"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-_encoding: str</p></span></div></foreignObject></g></g><g transform="translate(-209.88172912597656, -0.000003814697265625)" class="methods-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="345.51788330078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 413px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+get(key: str, as_bytes: bool, encoding: str) : Any</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="274.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 340px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+set(key: str, value: Any, encoding: str)</p></span></div></foreignObject></g><g transform="translate(0,36.00000286102295)" style="" class="label"><foreignObject height="24.000001907348633" width="140.94644165039062"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 201px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+has(key: str) : bool</p></span></div></foreignObject></g><g transform="translate(0,60.00000476837158)" style="" class="label"><foreignObject height="24.000001907348633" width="115.16072082519531"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 175px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+delete(key: str)</p></span></div></foreignObject></g><g transform="translate(0,84.00000667572021)" style="" class="label"><foreignObject height="24.000001907348633" width="54.687503814697266"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 111px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+clear()</p></span></div></foreignObject></g><g transform="translate(0,108.00000858306885)" style="" class="label"><foreignObject height="24.000001907348633" width="245.90179443359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 313px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+child(name: str) : PipelineStorage</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-221.88172912597656 -96.0000114440918 C-92.65828030870904 -96.0000114440918, 36.565168508558486 -96.0000114440918, 221.88172912597656 -96.0000114440918 M-221.88172912597656 -96.0000114440918 C-104.48179991555554 -96.0000114440918, 12.91812929486548 -96.0000114440918, 221.88172912597656 -96.0000114440918"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-221.88172912597656 -24.000003814697266 C-132.0644360967672 -24.000003814697266, -42.24714306755783 -24.000003814697266, 221.88172912597656 -24.000003814697266 M-221.88172912597656 -24.000003814697266 C-106.3096951297662 -24.000003814697266, 9.262338866444168 -24.000003814697266, 221.88172912597656 -24.000003814697266"></path></g></g><g transform="translate(3407.559383392334, 906.0001411437988)" id="classId-BlobPipelineStorage-206" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-223.87725830078125 -132.0000228881836 L223.87725830078125 -132.0000228881836 L223.87725830078125 132.0000228881836 L-223.87725830078125 132.0000228881836"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-223.87725830078125 -132.0000228881836 C-131.58697909119059 -132.0000228881836, -39.29669988159989 -132.0000228881836, 223.87725830078125 -132.0000228881836 M-223.87725830078125 -132.0000228881836 C-113.19229864577896 -132.0000228881836, -2.507338990776674 -132.0000228881836, 223.87725830078125 -132.0000228881836 M223.87725830078125 -132.0000228881836 C223.87725830078125 -36.964259897708416, 223.87725830078125 58.07150309276676, 223.87725830078125 132.0000228881836 M223.87725830078125 -132.0000228881836 C223.87725830078125 -64.33652488140842, 223.87725830078125 3.3269731253667487, 223.87725830078125 132.0000228881836 M223.87725830078125 132.0000228881836 C130.8630962411077 132.0000228881836, 37.848934181434174 132.0000228881836, -223.87725830078125 132.0000228881836 M223.87725830078125 132.0000228881836 C63.529442912635375 132.0000228881836, -96.8183724755105 132.0000228881836, -223.87725830078125 132.0000228881836 M-223.87725830078125 132.0000228881836 C-223.87725830078125 59.865934092173845, -223.87725830078125 -12.268154703835904, -223.87725830078125 -132.0000228881836 M-223.87725830078125 132.0000228881836 C-223.87725830078125 66.27786464389469, -223.87725830078125 0.555706399605782, -223.87725830078125 -132.0000228881836"></path></g><g transform="translate(0, -108.0000228881836)" class="annotation-group text"></g><g transform="translate(-78.23661041259766, -108.0000228881836)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="156.4732208251953"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 202px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>BlobPipelineStorage</p></span></div></foreignObject></g></g><g transform="translate(-211.87725830078125, -60.00001907348633)" class="members-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="285.4821472167969"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 348px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-_blob_service_client: BlobServiceClient</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="155.63394165039062"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 219px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-_container_name: str</p></span></div></foreignObject></g><g transform="translate(0,36.00000286102295)" style="" class="label"><foreignObject height="24.000001907348633" width="120.06250762939453"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 182px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-_path_prefix: str</p></span></div></foreignObject></g></g><g transform="translate(-211.87725830078125, 35.999996185302734)" class="methods-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="345.51788330078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 413px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+get(key: str, as_bytes: bool, encoding: str) : Any</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="274.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 340px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+set(key: str, value: Any, encoding: str)</p></span></div></foreignObject></g><g transform="translate(0,36.00000286102295)" style="" class="label"><foreignObject height="24.000001907348633" width="140.94644165039062"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 201px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+has(key: str) : bool</p></span></div></foreignObject></g><g transform="translate(0,60.00000476837158)" style="" class="label"><foreignObject height="24.000001907348633" width="115.16072082519531"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 175px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+delete(key: str)</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-223.87725830078125 -84.00001907348633 C-76.42864751653326 -84.00001907348633, 71.01996326771473 -84.00001907348633, 223.87725830078125 -84.00001907348633 M-223.87725830078125 -84.00001907348633 C-47.07957860784862 -84.00001907348633, 129.71810108508402 -84.00001907348633, 223.87725830078125 -84.00001907348633"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-223.87725830078125 11.999996185302734 C-68.41916629943324 11.999996185302734, 87.03892570191476 11.999996185302734, 223.87725830078125 11.999996185302734 M-223.87725830078125 11.999996185302734 C-84.44831360289947 11.999996185302734, 54.9806310949823 11.999996185302734, 223.87725830078125 11.999996185302734"></path></g></g><g transform="translate(3911.7647972106934, 906.0001411437988)" id="classId-MemoryPipelineStorage-207" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-230.32815551757812 -108.00001525878906 L230.32815551757812 -108.00001525878906 L230.32815551757812 108.00001525878906 L-230.32815551757812 108.00001525878906"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-230.32815551757812 -108.00001525878906 C-127.37048047430703 -108.00001525878906, -24.412805431035935 -108.00001525878906, 230.32815551757812 -108.00001525878906 M-230.32815551757812 -108.00001525878906 C-123.60170529674137 -108.00001525878906, -16.87525507590462 -108.00001525878906, 230.32815551757812 -108.00001525878906 M230.32815551757812 -108.00001525878906 C230.32815551757812 -55.341725425814715, 230.32815551757812 -2.683435592840368, 230.32815551757812 108.00001525878906 M230.32815551757812 -108.00001525878906 C230.32815551757812 -30.571878130634317, 230.32815551757812 46.85625899752043, 230.32815551757812 108.00001525878906 M230.32815551757812 108.00001525878906 C57.43901340888891 108.00001525878906, -115.4501286998003 108.00001525878906, -230.32815551757812 108.00001525878906 M230.32815551757812 108.00001525878906 C52.19268405897651 108.00001525878906, -125.9427873996251 108.00001525878906, -230.32815551757812 108.00001525878906 M-230.32815551757812 108.00001525878906 C-230.32815551757812 63.210798881373265, -230.32815551757812 18.421582503957467, -230.32815551757812 -108.00001525878906 M-230.32815551757812 108.00001525878906 C-230.32815551757812 25.575363750140824, -230.32815551757812 -56.849287758507415, -230.32815551757812 -108.00001525878906"></path></g><g transform="translate(0, -84.00001525878906)" class="annotation-group text"></g><g transform="translate(-91.13839721679688, -84.00001525878906)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="182.27679443359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 231px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>MemoryPipelineStorage</p></span></div></foreignObject></g></g><g transform="translate(-218.32815551757812, -36.0000114440918)" class="members-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="162.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 226px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-_storage: dict[str, Any]</p></span></div></foreignObject></g></g><g transform="translate(-218.32815551757812, 11.999992370605469)" class="methods-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="345.51788330078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 413px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+get(key: str, as_bytes: bool, encoding: str) : Any</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="274.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 340px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+set(key: str, value: Any, encoding: str)</p></span></div></foreignObject></g><g transform="translate(0,36.00000286102295)" style="" class="label"><foreignObject height="24.000001907348633" width="140.94644165039062"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 201px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+has(key: str) : bool</p></span></div></foreignObject></g><g transform="translate(0,60.00000476837158)" style="" class="label"><foreignObject height="24.000001907348633" width="115.16072082519531"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 175px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+delete(key: str)</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-230.32815551757812 -60.0000114440918 C-85.92717853212537 -60.0000114440918, 58.47379845332739 -60.0000114440918, 230.32815551757812 -60.0000114440918 M-230.32815551757812 -60.0000114440918 C-77.11976481940772 -60.0000114440918, 76.08862587876268 -60.0000114440918, 230.32815551757812 -60.0000114440918"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-230.32815551757812 -12.000007629394531 C-46.72059677247688 -12.000007629394531, 136.88696197262436 -12.000007629394531, 230.32815551757812 -12.000007629394531 M-230.32815551757812 -12.000007629394531 C-56.3150273981347 -12.000007629394531, 117.69810072130872 -12.000007629394531, 230.32815551757812 -12.000007629394531"></path></g></g><g transform="translate(3407.559383392334, 164.00003051757812)" id="classId-StorageFactory-208" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-310.6071472167969 -96.00001525878906 L310.6071472167969 -96.00001525878906 L310.6071472167969 96.00001525878906 L-310.6071472167969 96.00001525878906"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-310.6071472167969 -96.00001525878906 C-149.89780655931511 -96.00001525878906, 10.811534098166646 -96.00001525878906, 310.6071472167969 -96.00001525878906 M-310.6071472167969 -96.00001525878906 C-171.4436166170162 -96.00001525878906, -32.28008601723553 -96.00001525878906, 310.6071472167969 -96.00001525878906 M310.6071472167969 -96.00001525878906 C310.6071472167969 -45.90868723023505, 310.6071472167969 4.182640798318957, 310.6071472167969 96.00001525878906 M310.6071472167969 -96.00001525878906 C310.6071472167969 -21.32333528869738, 310.6071472167969 53.353344681394304, 310.6071472167969 96.00001525878906 M310.6071472167969 96.00001525878906 C137.14049920008188 96.00001525878906, -36.32614881663312 96.00001525878906, -310.6071472167969 96.00001525878906 M310.6071472167969 96.00001525878906 C72.47076674338169 96.00001525878906, -165.6656137300335 96.00001525878906, -310.6071472167969 96.00001525878906 M-310.6071472167969 96.00001525878906 C-310.6071472167969 42.30978754751065, -310.6071472167969 -11.380440163767759, -310.6071472167969 -96.00001525878906 M-310.6071472167969 96.00001525878906 C-310.6071472167969 31.5327045964801, -310.6071472167969 -32.93460606582886, -310.6071472167969 -96.00001525878906"></path></g><g transform="translate(-32.90625, -72.00001525878906)" class="annotation-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="65.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 119px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«factory»</p></span></div></foreignObject></g></g><g transform="translate(-58.68750762939453, -48.0000114440918)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="117.37500762939453"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 164px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>StorageFactory</p></span></div></foreignObject></g></g><g transform="translate(-298.6071472167969, -0.00000762939453125)" class="members-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="208.56251525878906"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 275px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+storage_types: dict[str, type]</p></span></div></foreignObject></g></g><g transform="translate(-298.6071472167969, 47.999996185302734)" class="methods-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="400.65179443359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 471px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+register_storage(storage_type: str, storage_class: type)</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="538.5267944335938"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 615px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+create_storage(storage_type: StorageType, kwargs: dict) : PipelineStorage</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-310.6071472167969 -24.00000762939453 C-125.82217561683075 -24.00000762939453, 58.96279598313538 -24.00000762939453, 310.6071472167969 -24.00000762939453 M-310.6071472167969 -24.00000762939453 C-100.60796499566393 -24.00000762939453, 109.391217225469 -24.00000762939453, 310.6071472167969 -24.00000762939453"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-310.6071472167969 23.999996185302734 C-80.95932555419037 23.999996185302734, 148.68849610841613 23.999996185302734, 310.6071472167969 23.999996185302734 M-310.6071472167969 23.999996185302734 C-182.02583851737538 23.999996185302734, -53.44452981795388 23.999996185302734, 310.6071472167969 23.999996185302734"></path></g></g><g transform="translate(4426.534923553467, 164.00003051757812)" id="classId-ModelManager-209" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-366.1875 -156.00003051757812 L366.1875 -156.00003051757812 L366.1875 156.00003051757812 L-366.1875 156.00003051757812"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-366.1875 -156.00003051757812 C-162.6188987953752 -156.00003051757812, 40.949702409249596 -156.00003051757812, 366.1875 -156.00003051757812 M-366.1875 -156.00003051757812 C-75.54944670976914 -156.00003051757812, 215.08860658046171 -156.00003051757812, 366.1875 -156.00003051757812 M366.1875 -156.00003051757812 C366.1875 -91.83439881767305, 366.1875 -27.66876711776797, 366.1875 156.00003051757812 M366.1875 -156.00003051757812 C366.1875 -79.0288609903237, 366.1875 -2.0576914630692613, 366.1875 156.00003051757812 M366.1875 156.00003051757812 C198.24893679567447 156.00003051757812, 30.310373591348934 156.00003051757812, -366.1875 156.00003051757812 M366.1875 156.00003051757812 C119.57239724613783 156.00003051757812, -127.04270550772435 156.00003051757812, -366.1875 156.00003051757812 M-366.1875 156.00003051757812 C-366.1875 74.03468217540735, -366.1875 -7.930666166763416, -366.1875 -156.00003051757812 M-366.1875 156.00003051757812 C-366.1875 39.647111039123956, -366.1875 -76.70580843933021, -366.1875 -156.00003051757812"></path></g><g transform="translate(-40.92411422729492, -132.00003051757812)" class="annotation-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="81.84822082519531"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«singleton»</p></span></div></foreignObject></g></g><g transform="translate(-56.00893020629883, -108.00002670288086)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="112.01786041259766"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 165px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>ModelManager</p></span></div></foreignObject></g></g><g transform="translate(-354.1875, -60.000022888183594)" class="members-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="189.43751525878906"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 255px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-_instance: ModelManager</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="241.88394165039062"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 310px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-chat_models: dict[str, ChatModel]</p></span></div></foreignObject></g><g transform="translate(0,36.00000286102295)" style="" class="label"><foreignObject height="24.000001907348633" width="337.96429443359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 416px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-embedding_models: dict[str, EmbeddingModel]</p></span></div></foreignObject></g></g><g transform="translate(-354.1875, 35.99999237060547)" class="methods-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="230.8035888671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 297px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+get_instance() : ModelManager</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="458.4285888671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 538px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+register_chat(name: str, model_type: str, **kwargs) : ChatModel</p></span></div></foreignObject></g><g transform="translate(0,36.00000286102295)" style="" class="label"><foreignObject height="24.000001907348633" width="554.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 643px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+register_embedding(name: str, model_type: str, **kwargs) : EmbeddingModel</p></span></div></foreignObject></g><g transform="translate(0,60.00000476837158)" style="" class="label"><foreignObject height="24.000001907348633" width="556.2858276367188"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 636px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+get_or_create_chat_model(name: str, model_type: str, **kwargs) : ChatModel</p></span></div></foreignObject></g><g transform="translate(0,84.00000667572021)" style="" class="label"><foreignObject height="24.000001907348633" width="652.3660888671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 742px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+get_or_create_embedding_model(name: str, model_type: str, **kwargs) : EmbeddingModel</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-366.1875 -84.0000228881836 C-177.0483879473051 -84.0000228881836, 12.090724105389825 -84.0000228881836, 366.1875 -84.0000228881836 M-366.1875 -84.0000228881836 C-92.53096070019387 -84.0000228881836, 181.12557859961225 -84.0000228881836, 366.1875 -84.0000228881836"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-366.1875 11.999992370605469 C-187.6122441348738 11.999992370605469, -9.03698826974761 11.999992370605469, 366.1875 11.999992370605469 M-366.1875 11.999992370605469 C-93.2204948269752 11.999992370605469, 179.7465103460496 11.999992370605469, 366.1875 11.999992370605469"></path></g></g><g transform="translate(4426.534923553467, 541.000093460083)" id="classId-ModelFactory-210" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-299.7053527832031 -132.0000228881836 L299.7053527832031 -132.0000228881836 L299.7053527832031 132.0000228881836 L-299.7053527832031 132.0000228881836"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-299.7053527832031 -132.0000228881836 C-73.3786743345236 -132.0000228881836, 152.94800411415594 -132.0000228881836, 299.7053527832031 -132.0000228881836 M-299.7053527832031 -132.0000228881836 C-131.27095672879173 -132.0000228881836, 37.16343932561966 -132.0000228881836, 299.7053527832031 -132.0000228881836 M299.7053527832031 -132.0000228881836 C299.7053527832031 -39.21386351458355, 299.7053527832031 53.57229585901649, 299.7053527832031 132.0000228881836 M299.7053527832031 -132.0000228881836 C299.7053527832031 -54.220513731153545, 299.7053527832031 23.558995425876503, 299.7053527832031 132.0000228881836 M299.7053527832031 132.0000228881836 C100.14584703089258 132.0000228881836, -99.41365872141796 132.0000228881836, -299.7053527832031 132.0000228881836 M299.7053527832031 132.0000228881836 C77.38220736640298 132.0000228881836, -144.94093805039716 132.0000228881836, -299.7053527832031 132.0000228881836 M-299.7053527832031 132.0000228881836 C-299.7053527832031 53.265541184503064, -299.7053527832031 -25.468940519177465, -299.7053527832031 -132.0000228881836 M-299.7053527832031 132.0000228881836 C-299.7053527832031 75.15952512073854, -299.7053527832031 18.319027353293478, -299.7053527832031 -132.0000228881836"></path></g><g transform="translate(-32.90625, -108.0000228881836)" class="annotation-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="65.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 119px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«factory»</p></span></div></foreignObject></g></g><g transform="translate(-52.00893020629883, -84.00001907348633)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="104.01786041259766"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 153px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>ModelFactory</p></span></div></foreignObject></g></g><g transform="translate(-287.7053527832031, -36.00001525878906)" class="members-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="236.1160888671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 301px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+_chat_registry: dict[str, Callable]</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="285.0446472167969"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 355px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+_embedding_registry: dict[str, Callable]</p></span></div></foreignObject></g></g><g transform="translate(-287.7053527832031, 35.99999237060547)" class="methods-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="346.3839416503906"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 416px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+register_chat(model_type: str, creator: Callable)</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="395.3125305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 470px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+register_embedding(model_type: str, creator: Callable)</p></span></div></foreignObject></g><g transform="translate(0,36.00000286102295)" style="" class="label"><foreignObject height="24.000001907348633" width="427.3214416503906"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 504px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+create_chat_model(model_type: str, **kwargs) : ChatModel</p></span></div></foreignObject></g><g transform="translate(0,60.00000476837158)" style="" class="label"><foreignObject height="24.000001907348633" width="523.4017944335938"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 609px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+create_embedding_model(model_type: str, **kwargs) : EmbeddingModel</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-299.7053527832031 -60.00001525878906 C-159.86599852186043 -60.00001525878906, -20.02664426051774 -60.00001525878906, 299.7053527832031 -60.00001525878906 M-299.7053527832031 -60.00001525878906 C-74.97546623927872 -60.00001525878906, 149.7544203046457 -60.00001525878906, 299.7053527832031 -60.00001525878906"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-299.7053527832031 11.999992370605469 C-133.33937519728954 11.999992370605469, 33.02660238862404 11.999992370605469, 299.7053527832031 11.999992370605469 M-299.7053527832031 11.999992370605469 C-73.68785928134511 11.999992370605469, 152.3296342205129 11.999992370605469, 299.7053527832031 11.999992370605469"></path></g></g><g transform="translate(327.26788330078125, 541.000093460083)" id="classId-ExtractGraphConfig-211" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-319.26788330078125 -120.00001525878906 L319.26788330078125 -120.00001525878906 L319.26788330078125 120.00001525878906 L-319.26788330078125 120.00001525878906"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-319.26788330078125 -120.00001525878906 C-108.53241566276361 -120.00001525878906, 102.20305197525403 -120.00001525878906, 319.26788330078125 -120.00001525878906 M-319.26788330078125 -120.00001525878906 C-188.7278189798885 -120.00001525878906, -58.18775465899574 -120.00001525878906, 319.26788330078125 -120.00001525878906 M319.26788330078125 -120.00001525878906 C319.26788330078125 -42.40925898971696, 319.26788330078125 35.18149727935514, 319.26788330078125 120.00001525878906 M319.26788330078125 -120.00001525878906 C319.26788330078125 -25.378359270004836, 319.26788330078125 69.24329671877939, 319.26788330078125 120.00001525878906 M319.26788330078125 120.00001525878906 C157.28661764749344 120.00001525878906, -4.694648005794363 120.00001525878906, -319.26788330078125 120.00001525878906 M319.26788330078125 120.00001525878906 C160.6994597243679 120.00001525878906, 2.1310361479545463 120.00001525878906, -319.26788330078125 120.00001525878906 M-319.26788330078125 120.00001525878906 C-319.26788330078125 39.14804289565885, -319.26788330078125 -41.70392946747137, -319.26788330078125 -120.00001525878906 M-319.26788330078125 120.00001525878906 C-319.26788330078125 70.68859788800106, -319.26788330078125 21.37718051721305, -319.26788330078125 -120.00001525878906"></path></g><g transform="translate(0, -96.00001525878906)" class="annotation-group text"></g><g transform="translate(-76.00894165039062, -96.00001525878906)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="152.0178680419922"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 198px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>ExtractGraphConfig</p></span></div></foreignObject></g></g><g transform="translate(-307.26788330078125, -48.0000114440918)" class="members-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="100.93750762939453"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 163px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+model_id: str</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="85.81250762939453"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 150px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+prompt: str</p></span></div></foreignObject></g><g transform="translate(0,36.00000286102295)" style="" class="label"><foreignObject height="24.000001907348633" width="149.83929443359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 216px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+entity_types: list[str]</p></span></div></foreignObject></g><g transform="translate(0,60.00000476837158)" style="" class="label"><foreignObject height="24.000001907348633" width="142.75894165039062"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 206px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+max_gleanings: int</p></span></div></foreignObject></g><g transform="translate(0,84.00000667572021)" style="" class="label"><foreignObject height="24.000001907348633" width="100.05357360839844"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 162px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+strategy: dict</p></span></div></foreignObject></g></g><g transform="translate(-307.26788330078125, 96.00000381469727)" class="methods-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="538.5267944335938"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 622px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+resolved_strategy(root_dir: str, model_config: LanguageModelConfig) : dict</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-319.26788330078125 -72.0000114440918 C-157.9679038705021 -72.0000114440918, 3.3320755597770244 -72.0000114440918, 319.26788330078125 -72.0000114440918 M-319.26788330078125 -72.0000114440918 C-89.1506218580827 -72.0000114440918, 140.96663958461585 -72.0000114440918, 319.26788330078125 -72.0000114440918"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-319.26788330078125 72.00000381469727 C-170.77927028221526 72.00000381469727, -22.29065726364928 72.00000381469727, 319.26788330078125 72.00000381469727 M-319.26788330078125 72.00000381469727 C-129.7839486676822 72.00000381469727, 59.69998596541683 72.00000381469727, 319.26788330078125 72.00000381469727"></path></g></g><g transform="translate(327.26788330078125, 164.00003051757812)" id="classId-GraphRagConfig-212" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-208.73214721679688 -120.00001525878906 L208.73214721679688 -120.00001525878906 L208.73214721679688 120.00001525878906 L-208.73214721679688 120.00001525878906"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-208.73214721679688 -120.00001525878906 C-63.17554603043553 -120.00001525878906, 82.38105515592582 -120.00001525878906, 208.73214721679688 -120.00001525878906 M-208.73214721679688 -120.00001525878906 C-65.75791496351619 -120.00001525878906, 77.2163172897645 -120.00001525878906, 208.73214721679688 -120.00001525878906 M208.73214721679688 -120.00001525878906 C208.73214721679688 -66.73175886361727, 208.73214721679688 -13.463502468445498, 208.73214721679688 120.00001525878906 M208.73214721679688 -120.00001525878906 C208.73214721679688 -26.799356555461657, 208.73214721679688 66.40130214786575, 208.73214721679688 120.00001525878906 M208.73214721679688 120.00001525878906 C93.98876573337056 120.00001525878906, -20.754615750055763 120.00001525878906, -208.73214721679688 120.00001525878906 M208.73214721679688 120.00001525878906 C84.79904236637954 120.00001525878906, -39.1340624840378 120.00001525878906, -208.73214721679688 120.00001525878906 M-208.73214721679688 120.00001525878906 C-208.73214721679688 49.76013881018926, -208.73214721679688 -20.47973763841054, -208.73214721679688 -120.00001525878906 M-208.73214721679688 120.00001525878906 C-208.73214721679688 37.69710836830325, -208.73214721679688 -44.60579852218257, -208.73214721679688 -120.00001525878906"></path></g><g transform="translate(0, -96.00001525878906)" class="annotation-group text"></g><g transform="translate(-64, -96.00001525878906)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 175px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>GraphRagConfig</p></span></div></foreignObject></g></g><g transform="translate(-196.73214721679688, -48.0000114440918)" class="members-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="90.2589340209961"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 153px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+root_dir: str</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="182.7857208251953"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 248px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+chunks: ChunkingConfig</p></span></div></foreignObject></g><g transform="translate(0,36.00000286102295)" style="" class="label"><foreignObject height="24.000001907348633" width="256.5804138183594"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 325px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+extract_graph: ExtractGraphConfig</p></span></div></foreignObject></g><g transform="translate(0,60.00000476837158)" style="" class="label"><foreignObject height="24.000001907348633" width="329.46429443359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 408px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+community_reports: CommunityReportConfig</p></span></div></foreignObject></g><g transform="translate(0,84.00000667572021)" style="" class="label"><foreignObject height="24.000001907348633" width="165.00894165039062"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 232px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+output: StorageConfig</p></span></div></foreignObject></g><g transform="translate(0,108.00000858306885)" style="" class="label"><foreignObject height="24.000001907348633" width="153.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 212px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+cache: CacheConfig</p></span></div></foreignObject></g></g><g transform="translate(-196.73214721679688, 120.00000381469727)" class="methods-group text"></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-208.73214721679688 -72.0000114440918 C-55.27685854929848 -72.0000114440918, 98.17843011819991 -72.0000114440918, 208.73214721679688 -72.0000114440918 M-208.73214721679688 -72.0000114440918 C-46.7286523418895 -72.0000114440918, 115.27484253301787 -72.0000114440918, 208.73214721679688 -72.0000114440918"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-208.73214721679688 96.00000381469727 C-46.328181514952206 96.00000381469727, 116.07578418689246 96.00000381469727, 208.73214721679688 96.00000381469727 M-208.73214721679688 96.00000381469727 C-74.53690353981148 96.00000381469727, 59.65834013717392 96.00000381469727, 208.73214721679688 96.00000381469727"></path></g></g><g transform="translate(1992.146327972412, 1259.0001735687256)" id="classId-Document-213" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-62.45982360839844 -72.00000762939453 L62.45982360839844 -72.00000762939453 L62.45982360839844 72.00000762939453 L-62.45982360839844 72.00000762939453"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-62.45982360839844 -72.00000762939453 C-24.45239588286708 -72.00000762939453, 13.555031842664278 -72.00000762939453, 62.45982360839844 -72.00000762939453 M-62.45982360839844 -72.00000762939453 C-20.229409695995614 -72.00000762939453, 22.00100421640721 -72.00000762939453, 62.45982360839844 -72.00000762939453 M62.45982360839844 -72.00000762939453 C62.45982360839844 -33.868429394386304, 62.45982360839844 4.263148840621923, 62.45982360839844 72.00000762939453 M62.45982360839844 -72.00000762939453 C62.45982360839844 -20.883543316764722, 62.45982360839844 30.232920995865086, 62.45982360839844 72.00000762939453 M62.45982360839844 72.00000762939453 C15.180152848960198 72.00000762939453, -32.09951791047804 72.00000762939453, -62.45982360839844 72.00000762939453 M62.45982360839844 72.00000762939453 C15.04538276619089 72.00000762939453, -32.36905807601666 72.00000762939453, -62.45982360839844 72.00000762939453 M-62.45982360839844 72.00000762939453 C-62.45982360839844 17.722781650226203, -62.45982360839844 -36.554444328942125, -62.45982360839844 -72.00000762939453 M-62.45982360839844 72.00000762939453 C-62.45982360839844 20.19304471277927, -62.45982360839844 -31.613918203835993, -62.45982360839844 -72.00000762939453"></path></g><g transform="translate(0, -48.00000762939453)" class="annotation-group text"></g><g transform="translate(-39.11607360839844, -48.00000762939453)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="78.23214721679688"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 129px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Document</p></span></div></foreignObject></g></g><g transform="translate(-50.45982360839844, -0.000003814697265625)" class="members-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="61.80357360839844"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 123px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+text: str</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="48.46428680419922"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 108px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: str</p></span></div></foreignObject></g></g><g transform="translate(-50.45982360839844, 72.00000381469727)" class="methods-group text"></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-62.45982360839844 -24.000003814697266 C-29.235679639277222 -24.000003814697266, 3.9884643298439926 -24.000003814697266, 62.45982360839844 -24.000003814697266 M-62.45982360839844 -24.000003814697266 C-26.671880968132832 -24.000003814697266, 9.116061672132773 -24.000003814697266, 62.45982360839844 -24.000003814697266"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-62.45982360839844 48.000003814697266 C-35.46748771754534 48.000003814697266, -8.475151826692233 48.000003814697266, 62.45982360839844 48.000003814697266 M-62.45982360839844 48.000003814697266 C-35.21983774543139 48.000003814697266, -7.979851882464338 48.000003814697266, 62.45982360839844 48.000003814697266"></path></g></g><g transform="translate(2305.579372406006, 1259.0001735687256)" id="classId-EntityExtractionResult-214" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-200.9732208251953 -84.0000114440918 L200.9732208251953 -84.0000114440918 L200.9732208251953 84.0000114440918 L-200.9732208251953 84.0000114440918"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-200.9732208251953 -84.0000114440918 C-79.82813329947436 -84.0000114440918, 41.316954226246594 -84.0000114440918, 200.9732208251953 -84.0000114440918 M-200.9732208251953 -84.0000114440918 C-93.47405122181287 -84.0000114440918, 14.02511838156957 -84.0000114440918, 200.9732208251953 -84.0000114440918 M200.9732208251953 -84.0000114440918 C200.9732208251953 -39.59921452261004, 200.9732208251953 4.801582398871716, 200.9732208251953 84.0000114440918 M200.9732208251953 -84.0000114440918 C200.9732208251953 -43.62680027351163, 200.9732208251953 -3.253589102931457, 200.9732208251953 84.0000114440918 M200.9732208251953 84.0000114440918 C55.52445575447996 84.0000114440918, -89.9243093162354 84.0000114440918, -200.9732208251953 84.0000114440918 M200.9732208251953 84.0000114440918 C52.338103805533905 84.0000114440918, -96.2970132141275 84.0000114440918, -200.9732208251953 84.0000114440918 M-200.9732208251953 84.0000114440918 C-200.9732208251953 34.295549378610886, -200.9732208251953 -15.408912686870025, -200.9732208251953 -84.0000114440918 M-200.9732208251953 84.0000114440918 C-200.9732208251953 33.85878298997717, -200.9732208251953 -16.282445464137453, -200.9732208251953 -84.0000114440918"></path></g><g transform="translate(0, -60.0000114440918)" class="annotation-group text"></g><g transform="translate(-85.79464721679688, -60.0000114440918)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="171.58929443359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 215px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>EntityExtractionResult</p></span></div></foreignObject></g></g><g transform="translate(-188.9732208251953, -12.000007629394531)" class="members-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="204.98214721679688"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 275px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+entities: list[ExtractedEntity]</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="292.15179443359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 367px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+relationships: list[ExtractedRelationship]</p></span></div></foreignObject></g><g transform="translate(0,36.00000286102295)" style="" class="label"><foreignObject height="24.000001907348633" width="124.97322082519531"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 187px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+graph: nx.Graph</p></span></div></foreignObject></g></g><g transform="translate(-188.9732208251953, 84.00000762939453)" class="methods-group text"></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-200.9732208251953 -36.00000762939453 C-90.74007644994724 -36.00000762939453, 19.493067925300835 -36.00000762939453, 200.9732208251953 -36.00000762939453 M-200.9732208251953 -36.00000762939453 C-94.33777358066024 -36.00000762939453, 12.297673663874832 -36.00000762939453, 200.9732208251953 -36.00000762939453"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-200.9732208251953 60.00000762939453 C-61.45783274016242 60.00000762939453, 78.05755534487048 60.00000762939453, 200.9732208251953 60.00000762939453 M-200.9732208251953 60.00000762939453 C-60.70137073195045 60.00000762939453, 79.57047936129442 60.00000762939453, 200.9732208251953 60.00000762939453"></path></g></g><g transform="translate(2714.470027923584, 1259.0001735687256)" id="classId-GraphExtractionResult-215" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-157.9174346923828 -72.00000762939453 L157.9174346923828 -72.00000762939453 L157.9174346923828 72.00000762939453 L-157.9174346923828 72.00000762939453"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-157.9174346923828 -72.00000762939453 C-87.25770206407243 -72.00000762939453, -16.597969435762053 -72.00000762939453, 157.9174346923828 -72.00000762939453 M-157.9174346923828 -72.00000762939453 C-92.87868868069005 -72.00000762939453, -27.83994266899728 -72.00000762939453, 157.9174346923828 -72.00000762939453 M157.9174346923828 -72.00000762939453 C157.9174346923828 -29.912936303865095, 157.9174346923828 12.174135021664341, 157.9174346923828 72.00000762939453 M157.9174346923828 -72.00000762939453 C157.9174346923828 -25.45290320880479, 157.9174346923828 21.09420121178495, 157.9174346923828 72.00000762939453 M157.9174346923828 72.00000762939453 C52.18247405544395 72.00000762939453, -53.55248658149492 72.00000762939453, -157.9174346923828 72.00000762939453 M157.9174346923828 72.00000762939453 C54.43004033675659 72.00000762939453, -49.05735401886963 72.00000762939453, -157.9174346923828 72.00000762939453 M-157.9174346923828 72.00000762939453 C-157.9174346923828 29.635659453847367, -157.9174346923828 -12.728688721699797, -157.9174346923828 -72.00000762939453 M-157.9174346923828 72.00000762939453 C-157.9174346923828 33.42082881467421, -157.9174346923828 -5.158350000046113, -157.9174346923828 -72.00000762939453"></path></g><g transform="translate(0, -48.00000762939453)" class="annotation-group text"></g><g transform="translate(-87.12947845458984, -48.00000762939453)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="174.25894165039062"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 219px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>GraphExtractionResult</p></span></div></foreignObject></g></g><g transform="translate(-145.9174346923828, -0.000003814697265625)" class="members-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="128.5357208251953"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+output: nx.Graph</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="204.7053680419922"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 268px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+source_docs: dict[Any, Any]</p></span></div></foreignObject></g></g><g transform="translate(-145.9174346923828, 72.00000381469727)" class="methods-group text"></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-157.9174346923828 -24.000003814697266 C-81.55584917155133 -24.000003814697266, -5.194263650719847 -24.000003814697266, 157.9174346923828 -24.000003814697266 M-157.9174346923828 -24.000003814697266 C-48.30235688309509 -24.000003814697266, 61.31272092619264 -24.000003814697266, 157.9174346923828 -24.000003814697266"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-157.9174346923828 48.000003814697266 C-39.30266760590044 48.000003814697266, 79.31209948058194 48.000003814697266, 157.9174346923828 48.000003814697266 M-157.9174346923828 48.000003814697266 C-57.00609140243186 48.000003814697266, 43.905251887519086 48.000003814697266, 157.9174346923828 48.000003814697266"></path></g></g><g transform="translate(1927.5224266052246, 1564.0002059936523)" id="classId-Progress-216" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-107.38394165039062 -96.00001525878906 L107.38394165039062 -96.00001525878906 L107.38394165039062 96.00001525878906 L-107.38394165039062 96.00001525878906"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-107.38394165039062 -96.00001525878906 C-40.117597488493644 -96.00001525878906, 27.148746673403338 -96.00001525878906, 107.38394165039062 -96.00001525878906 M-107.38394165039062 -96.00001525878906 C-36.77398204022222 -96.00001525878906, 33.83597756994618 -96.00001525878906, 107.38394165039062 -96.00001525878906 M107.38394165039062 -96.00001525878906 C107.38394165039062 -50.78133510284746, 107.38394165039062 -5.562654946905852, 107.38394165039062 96.00001525878906 M107.38394165039062 -96.00001525878906 C107.38394165039062 -45.016629420086026, 107.38394165039062 5.96675641861701, 107.38394165039062 96.00001525878906 M107.38394165039062 96.00001525878906 C38.89540844576176 96.00001525878906, -29.593124758867106 96.00001525878906, -107.38394165039062 96.00001525878906 M107.38394165039062 96.00001525878906 C23.00893036759622 96.00001525878906, -61.36608091519818 96.00001525878906, -107.38394165039062 96.00001525878906 M-107.38394165039062 96.00001525878906 C-107.38394165039062 37.309164782146304, -107.38394165039062 -21.381685694496454, -107.38394165039062 -96.00001525878906 M-107.38394165039062 96.00001525878906 C-107.38394165039062 21.72594729817901, -107.38394165039062 -52.548120662431046, -107.38394165039062 -96.00001525878906"></path></g><g transform="translate(0, -72.00001525878906)" class="annotation-group text"></g><g transform="translate(-34.6875, -72.00001525878906)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="69.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 116px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Progress</p></span></div></foreignObject></g></g><g transform="translate(-95.38394165039062, -24.000011444091797)" class="members-group text"><g transform="translate(0,-12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="101.84822082519531"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 165px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+percent: float</p></span></div></foreignObject></g><g transform="translate(0,12.000000953674316)" style="" class="label"><foreignObject height="24.000001907348633" width="113.3839340209961"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 178px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+description: str</p></span></div></foreignObject></g><g transform="translate(0,36.00000286102295)" style="" class="label"><foreignObject height="24.000001907348633" width="112.50000762939453"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 176px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+total_items: int</p></span></div></foreignObject></g><g transform="translate(0,60.00000476837158)" style="" class="label"><foreignObject height="24.000001907348633" width="156.0803680419922"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 222px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+completed_items: int</p></span></div></foreignObject></g></g><g transform="translate(-95.38394165039062, 96.00000381469727)" class="methods-group text"></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-107.38394165039062 -48.0000114440918 C-51.50745193375316 -48.0000114440918, 4.369037782884305 -48.0000114440918, 107.38394165039062 -48.0000114440918 M-107.38394165039062 -48.0000114440918 C-28.206174573611364 -48.0000114440918, 50.9715925031679 -48.0000114440918, 107.38394165039062 -48.0000114440918"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-107.38394165039062 72.00000381469727 C-62.851628074456585 72.00000381469727, -18.319314498522544 72.00000381469727, 107.38394165039062 72.00000381469727 M-107.38394165039062 72.00000381469727 C-46.46591704887449 72.00000381469727, 14.45210755264165 72.00000381469727, 107.38394165039062 72.00000381469727"></path></g></g><g transform="translate(4244.981346130371, 906.0001411437988)" id="classId-ChatModel-217" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-52.88839340209961 -42.00000190734863 L52.88839340209961 -42.00000190734863 L52.88839340209961 42.00000190734863 L-52.88839340209961 42.00000190734863"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-52.88839340209961 -42.00000190734863 C-16.139843886702884 -42.00000190734863, 20.60870562869384 -42.00000190734863, 52.88839340209961 -42.00000190734863 M-52.88839340209961 -42.00000190734863 C-17.5736233783191 -42.00000190734863, 17.741146645461413 -42.00000190734863, 52.88839340209961 -42.00000190734863 M52.88839340209961 -42.00000190734863 C52.88839340209961 -20.879612976949666, 52.88839340209961 0.24077595344930103, 52.88839340209961 42.00000190734863 M52.88839340209961 -42.00000190734863 C52.88839340209961 -15.77241813566135, 52.88839340209961 10.455165636025932, 52.88839340209961 42.00000190734863 M52.88839340209961 42.00000190734863 C30.134240448855902 42.00000190734863, 7.3800874956121945 42.00000190734863, -52.88839340209961 42.00000190734863 M52.88839340209961 42.00000190734863 C15.682290666533262 42.00000190734863, -21.523812069033085 42.00000190734863, -52.88839340209961 42.00000190734863 M-52.88839340209961 42.00000190734863 C-52.88839340209961 9.804833142797001, -52.88839340209961 -22.39033562175463, -52.88839340209961 -42.00000190734863 M-52.88839340209961 42.00000190734863 C-52.88839340209961 14.339934595943227, -52.88839340209961 -13.32013271546218, -52.88839340209961 -42.00000190734863"></path></g><g transform="translate(0, -18.000001907348633)" class="annotation-group text"></g><g transform="translate(-40.88839340209961, -18.000001907348633)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="81.77678680419922"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 132px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>ChatModel</p></span></div></foreignObject></g></g><g transform="translate(-40.88839340209961, 30.000001907348633)" class="members-group text"></g><g transform="translate(-40.88839340209961, 60.00000190734863)" class="methods-group text"></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-52.88839340209961 6.000001907348633 C-18.508058929826767 6.000001907348633, 15.872275542446076 6.000001907348633, 52.88839340209961 6.000001907348633 M-52.88839340209961 6.000001907348633 C-12.073503309481538 6.000001907348633, 28.741386783136534 6.000001907348633, 52.88839340209961 6.000001907348633"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-52.88839340209961 24.000001907348633 C-16.480564682118924 24.000001907348633, 19.92726403786176 24.000001907348633, 52.88839340209961 24.000001907348633 M-52.88839340209961 24.000001907348633 C-21.00473486700707 24.000001907348633, 10.87892366808547 24.000001907348633, 52.88839340209961 24.000001907348633"></path></g></g><g transform="translate(4426.534923553467, 906.0001411437988)" id="classId-EmbeddingModel-218" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-78.6651840209961 -42.00000190734863 L78.6651840209961 -42.00000190734863 L78.6651840209961 42.00000190734863 L-78.6651840209961 42.00000190734863"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-78.6651840209961 -42.00000190734863 C-32.61380197724004 -42.00000190734863, 13.43758006651602 -42.00000190734863, 78.6651840209961 -42.00000190734863 M-78.6651840209961 -42.00000190734863 C-22.881626863420408 -42.00000190734863, 32.90193029415528 -42.00000190734863, 78.6651840209961 -42.00000190734863 M78.6651840209961 -42.00000190734863 C78.6651840209961 -12.056701554058819, 78.6651840209961 17.886598799230995, 78.6651840209961 42.00000190734863 M78.6651840209961 -42.00000190734863 C78.6651840209961 -11.465173835341574, 78.6651840209961 19.069654236665485, 78.6651840209961 42.00000190734863 M78.6651840209961 42.00000190734863 C15.846520172347802 42.00000190734863, -46.97214367630049 42.00000190734863, -78.6651840209961 42.00000190734863 M78.6651840209961 42.00000190734863 C31.74800570605059 42.00000190734863, -15.169172608894911 42.00000190734863, -78.6651840209961 42.00000190734863 M-78.6651840209961 42.00000190734863 C-78.6651840209961 13.238734158735124, -78.6651840209961 -15.522533589878385, -78.6651840209961 -42.00000190734863 M-78.6651840209961 42.00000190734863 C-78.6651840209961 18.685305929853627, -78.6651840209961 -4.62939004764138, -78.6651840209961 -42.00000190734863"></path></g><g transform="translate(0, -18.000001907348633)" class="annotation-group text"></g><g transform="translate(-66.6651840209961, -18.000001907348633)" class="label-group text"><g transform="translate(0,-12.000000953674316)" style="font-weight: bolder" class="label"><foreignObject height="24.000001907348633" width="133.3303680419922"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 183px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>EmbeddingModel</p></span></div></foreignObject></g></g><g transform="translate(-66.6651840209961, 30.000001907348633)" class="members-group text"></g><g transform="translate(-66.6651840209961, 60.00000190734863)" class="methods-group text"></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-78.6651840209961 6.000001907348633 C-29.694695388906062 6.000001907348633, 19.27579324318397 6.000001907348633, 78.6651840209961 6.000001907348633 M-78.6651840209961 6.000001907348633 C-35.78615000229016 6.000001907348633, 7.0928840164157805 6.000001907348633, 78.6651840209961 6.000001907348633"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-78.6651840209961 24.000001907348633 C-46.26583858527988 24.000001907348633, -13.866493149563667 24.000001907348633, 78.6651840209961 24.000001907348633 M-78.6651840209961 24.000001907348633 C-42.61527110939779 24.000001907348633, -6.565358197799483 24.000001907348633, 78.6651840209961 24.000001907348633"></path></g></g></g></g></g></svg>