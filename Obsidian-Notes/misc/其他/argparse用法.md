关于命令行，想必大家都很熟悉吧。不知道有没有思考过，关于命令行的参数有几种呢？let's dive into it.

cp a b
ls xx
tar -xzvf xxx
command --xxx xx


其实可以为分成这么几种：

- 位置参数：仅靠位置决定参数是哪个。这也是最常见，最常用的参数类型。通过命令后的参数的位置决定参数的用途，比如cp 后面第一个就是源文件/文件夹，第二个就是目标文件/文件夹。
- 可选参数：在[官方文档](https://docs.python.org/3/howto/argparse.html)中，它指出了一类参数：可选参数。但是我感觉这么分类略感奇怪。可选意味着选不选都行，但是很多时候我们开发的时候这类参数却是必选的参数。用关键字参数称呼更合适。关键字参数就是要指定参数的名字的参数，类似：pip install xxx --proxy 127.0.0.1:7890 -i http://xxx.xxx.xxx 这条命令制定了pip在安装包的时候使用本地7890端口上的代理，并且制定源为xxxx.

然后在这两种参数的基础上，要注意：
- 要有help信息，也就是说使用xx.py --help之后要输出help信息
- 两种参数都要加入默认参数的支持



位置
名称

当你定义命令行参数时，`argparse` 会根据你定义的参数名称来创建属性，而不会自动为短选项创建属性