# 1. 文件配置
1. 解压src压缩包，其内容为：
```
src
├── chatcode    #chatcode是我们用的自己的package的代码
│   ├── askdoc
│   └── searchimg
├── data        #data用于存放pdf，处理后的文本等文件
├── requirements.txt      #除了progai本地安装的包之外，所有需要安装的包
├── startflow.py          #主程序
└── vector.py             #调用azure api相关的函数
```
把src下的东西放到ou-baby-mfggpt/src下，合并后目录里结构为：
```
src
├── README.md
├── mfggpt
│   ├── config
│   ├── custom_ipa_endpoints
│   ├── steps
│   ├── test_all_exe.py
│   ├── test_hello_world.py
│   ├── textdata
|   ├── utils
│   └── ...
|    
|   #以上为原目录内容，下面为刚合并进的内容
├── chatcode
│   ├── askdoc
│   └── searchimg
├── data
├── requirements.txt
├── startflow.py
└── vector.py
```

**执行startflow.py也应在src目录下执行。**

# 2. 环境安装

python版本：3.9

```
# ocr需要安装的gpu相关的库
apt-get update && apt-get install ffmpeg libsm6 libxext6  -y

# 记得切换虚拟环境
pip install -r requirements.txt
# 如果pip 安装过慢，可以使用
#pip install -r requirements.txt --proxy http://yourproxyaddress:port
```

## 2.1  apt换源
如果apt安装过慢，可以尝试切换apt源：搜索ubuntu+ubuntu版本号 apt换源。比如ubuntu22.04 apt换源：
https://juejin.cn/post/7258119112370208823

## 2.2 pip代理
如果pip 安装过慢，可以尝试代理到本机端口：

1. 使用ipconfig找到wsl的网卡地址，我的是***********
![[Pasted image 20240530205553.png]]

2. 打开clash 的allow lan
![[屏幕截图 2024-05-30 205630.png]]

3. pip安装的时候使用：

```
pip install -r requirements.txt --proxy http://***********:7890
```


