

# chapter 3

这章都是一些老命令，这里挑几个值得记忆的

## 链接文件

create a linkfile pointing to sourcefile:

```
ln -s sourcefile linkfile 
```

create a hardlink file pointing to source file:

```
ln sourcefile linkfile
```

对于hardlink，二者根本就是同一个文件。可以通过查看inode来确定。ls -i 会显示inode

## chapter 4

## ps

ps命令在不同os中曾有两个版本，unix style和bsd风格。后来linux整合了一下，顺便加入了gnu风格。unix风格：短横线 like this ：ps -ef , bsd style, no dash, like this: ps aux,  and gnu style is using full spelling, like "ps --forest". gnu风格可以和bsd风格或unix风格同时使用。 

### ps 输出详解

#### Unix风格

Unix风格选项源自贝尔实验室开发的AT&T Unix系统中的ps命令。这些选项如表4-1所示。

![[Pasted image 20240424010531.png]]

```
$ ps -ef UID PID PPID C STIME TTY TIME CMD 
root 1 0 0 12:14 ? 00:00:02 /sbin/init splash 
root 2 0 0 12:14 ? 00:00:00 [kthreadd] 
root 3 2 0 12:14 ? 00:00:00 [rcu_gp] 
root 4 2 0 12:14 ? 00:00:00 [rcu_par_gp] 
root 5 2 0 12:14 ? 00:00:00 [kworker/0:0-events] 
root 6 2 0 12:14 ? 00:00:00 [kworker/0:0H-kblockd] 
root 7 2 0 12:14 ? 00:00:00 [kworker/0:1-events] 
... 
rich 2209 1438 0 12:17 ? 00:00:01 /usr/libexec/ gnome-terminal- 
rich 2221 2209 0 12:17 pts/0 00:00:00 bash 
rich 2325 2221 0 12:20 pts/0 00:00:00 ps -ef $
```

·C：进程生命期中的CPU利用率。·STIME：进程启动时的系统时间。·TTY：进程是从哪个终端设备启动的。·TIME：运行进程的累计CPU时间。·CMD：启动的程序名称。

![[Pasted image 20240424010805.png]]

#### bsd风格

![[Pasted image 20240424010908.png]]

在使用BSD风格的选项时，ps命令会自动改变输出以模仿BSD格式。下面是使用l选项的输出。

注意，尽管上述很多输出列跟使用Unix风格选项时是一样的，但还是有一些不同之处。
·VSZ：进程占用的虚拟内存大小（以KB为单位）。
·RSS：进程在未被交换出时占用的物理内存大小。
·STAT：代表当前进程状态的多字符状态码。

很多系统管理员喜欢BSD风格的l选项，因为能输出更详细的进程状态码（STAT列）。多字符状态码能比Unix风格输出的单字符状态码更清楚地表明进程的当前状态。第一个字符采用了与Unix风格的S输出列相同的值，表明进程是在休眠、运行还是等待。第二个字符进一步说明了进程的状态。

·<：该进程以高优先级运行。
·N：该进程以低优先级运行。
·L：该进程有锁定在内存中的页面。
·s：该进程是控制进程。
·l：该进程拥有多线程。
·+：该进程在前台运行。

从先前展示的简单例子中可以看出，bash命令处于休眠状态，但同时它也是一个控制进程（会话中的主进程），而ps命令则运行在系统前台。

#### GNU长选项
GNU开发人员在经过改进的新ps命令中加入了另外一些选项，其中一些GNU长选项复制了现有的Unix或BSD风格选项的效果，而另外一些则提供了新功能。表4-3列出了可用的GNU长选项。

![[Pasted image 20240424011305.png]]

可以混用GNU长选项和Unix或BSD风格的选项来定制输出。作为一个GNU长选项，--forest选项着实讨人喜欢。该选项能够使用ASCII字符来绘制可爱的图表以显示进程的层级信息

![[Pasted image 20240424011330.png]]

这种格式可以轻而易举地跟踪子进程和父进程。

