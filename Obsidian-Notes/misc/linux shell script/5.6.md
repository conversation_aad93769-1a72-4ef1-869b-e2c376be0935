## 进程列表

将命令用分号隔开，然后用小括号括起来，就能让其在子shell中运行。我们称之为进程列表。

```
$ (sleep 2 ; echo $BASH_SUBSHELL ; sleep 2) 
1 
$
```

好处就是可以在子shell中各种修改各种东西，比如环境变量，路径等。回去后并不会改变父shell的环境。

书上说，进程列表和&后台一起用可谓是一种创造性的用法！可以放到后台，创建子shell运行，很方便。我感觉也就那样吧（抠鼻）

协程跳过

## 内建命令&外部命令

对于一个shell程序而言，其执行的命令往往有内建命令和外部命令之分。内建命令就是编译在shell程序中的，而外部命令就是保存在文件系统中的某个应用/脚本等。使用外部命令的问题就是会使用fork创建进程，which使用内建命令不需要。常见的内建命令有cd echo等等。使用fork会消耗时间，资源。

使用type命令可以帮你检查某个命令是内建还是外部。有的命令不仅是内建，也会有外部实现，-a参数会显示所有的种类。

## alias

为命令创建别名。比如非常骚的alias cd=rm。要注意：

1.alias尽在当前shell生效
2.=前后没有空格，which means，xx=xxxxxx一整个是一个参数。


第五章，算是over了，忽略了一些感觉没啥用的知识点，比如协程。