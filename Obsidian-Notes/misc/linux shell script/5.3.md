# 结束进程

经典kill命令，杀杀杀！实际上是向进程发送了信号。除了kill，还有pkill和killall都可以用名字来杀进程。

# 挂载

mount -t type source destination
 
 -t可以忽略，挂不上再自己指定。

卸载： umount [设备 | 目录]

## 空间显示

df默认以1k block为单位计算磁盘空间使用情况。因为传统就是这样，1k不大不小比较合适。

==***还在用du -h --maxium-depth=1? 快来试试du -sh \* 吧！简单多噜(～￣▽￣)～***==

## sort命令

sort 排序

sort -n 按照数字排
sort -h 有储存单位，按照储存单位排
sort -M 月份开头，按照月份排
-r 倒序
不错的 sort -t ‘ ’ -k x shit.out 以引号中的东西为分隔符，用第x个分割后的元素作为索引排序

## gzip

我说为什么linux上为什么一直用gzip呢，半天g是gnu的g啊，本来就是要用来代替unix的compress的gun的工具之一。

## tar

操蛋tar命令，每次都忘参数位置。

tar czvf archive.tar.gz file1 file2 dir1

tar xzvf archive.tar.gz

就是傻逼，别人参数位置都是源，源，..., 目标。你他妈先制定目标。之前还以为ln命令也和你一样，是我错怪ln了，ln帮助界面写的是 ln  target linkname,看到target还以为又是把创建的目标放在第一个参数位置，实际上target指的是创建连接的源文件。


# over！第四章结束！