# bash
## 登录shell

- 登录shell相关的配置文件有：
```
/etc/profile
$HOME/.bash_profile
$HOME/.bashrc
$HOME/.bash_login
$HOME/.profile
```
除了第一个是主启动文件，其他的文件都是用于个性化配置不同用户的bash。并且那些个性化配置的文件并不是都有，比如我们最常用的ubuntu发行版下的~/.bashrc


- /etc/profile 是bash的主启动文件。不同系统的/etc/profile文件会干不同的事情。ubuntu和centos就不同。比如我的ubuntu在里面执行了/etc/bash.bashrc文件，但是centos中并没有。centos中甚至还导出了一些其他的环境变量。**但是**两个发行版都会循环执行/etc/profile.d里面的脚本
- profile.d中的文件很多与应用有关。很可能我的kde的konsole使用的语言不一样就和这里的kde.sh有关。

- 其他的配置文件都是一个作用，正如我们上面所说的，用于不同用户个性化配置bash，比如设置环境变量之类的~~，你也不想你配置的花里胡哨的命令行被别的用户看到吧~~。大多数发行版只会用到其中的一到两个文件。

- 书上说，会按照顺序执行：
- ![[Pasted image 20240529153038.png]]
- 找到第一个后其他的会被忽略。但是我的并没有。书上还说，.bashrc通常是被其他文件运行的，但是我这也没有上面的这几个文件，怎么运行的bashrc呢
## 交互式shell

刚才说的那些配置文件，都是登录shell，但是如果是我们常用的调用alt+ctrl+t或者是终端中输入bash调起一个新的shell，那么他的运行逻辑和上面是不一样的。他不会访问/etc/profile等，只会检查.bashrc文件。

## 非交互式shell

非交互式shell不会执行那些配置文件，不过它一般都是从交互式shell中执行的，所以可以继承父shell的环境变量。

### 环境变量持久化

全局？当然就是/etc/profile了，但是这不是好主意，尤其是升级发行版的时候，这个文件就被更新了。最好是在profile.d中设置一个.sh文件，设置环境变量。个人环境变量一般放在.bashrc中，交互式的环境变量最好设置好BASH_ENV环境变量

## 数组变量

环境变量具有数组特性，但是不常用，不推荐。