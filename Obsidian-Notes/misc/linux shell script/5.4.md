# shell !!!

## /etc/passwd

还记得/etc/passwd中的各个字段的含义吗！让我们来回顾一下！

我随便截一个哦：

```
root@lavm-8o9xgigeo6:~# cat /etc/passwd | head
root:x:0:0:root:/root:/bin/bash
daemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin
bin:x:2:2:bin:/bin:/usr/sbin/nologin
sys:x:3:3:sys:/dev:/usr/sbin/nologin
sync:x:4:65534:sync:/bin:/bin/sync
```

username:passwd:userid:groupid:sometips:loginpath:defaultshell

## 关于默认登录shell和默认系统shell

linux关于默认shell，可以分为两种类型。一种是默认登录shell，也就是/etc/passwd中看到的那些，同时也是你平时登陆上去之后看到的那个shell界面，这两个是同一个东西。

而系统中还有一个默认系统shell，就是/bin/sh。这是一个符号链接文件，指向了/etc/shells文件中的一个shell，或许是bash，ubuntu上多是dash。这个shell平时接触的机会都很少，它用来系统启动的时候执行一些脚本。

## echo $0

使用echo $0，可以看到当前使用的shell是什么shell。比如我echo一下：

```
cps@cps-PowerEdge-T640:~$ echo $0
-bash
cps@cps-PowerEdge-T640:~$ dash
$ echo $0
dash
```

we got -bash when we using default shell, it's important to mention that the dash, '- ', before bash means that the bash is your default login shell.

