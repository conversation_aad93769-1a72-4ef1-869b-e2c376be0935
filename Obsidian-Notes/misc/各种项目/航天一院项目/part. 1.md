
# 知识图谱

即，知识图谱是由一些相互连接的实体和他们的属性构成的。换句话说，知识图谱是由一条条知识组成，每条知识表示为一个SPO三元组(Subject-Predicate-Object)。知识图谱是由一条条只是组成的，这些知识的形式是spo，也就是主谓宾形式

在知识图谱中，我们用RDF形式化地表示这种三元关系。RDF(Resource Description Framework)，即资源描述框架，是W3C制定的，用于描述实体/资源的标准数据模型。RDF图中一共有三种类型，International Resource Identifiers(IRIs)，blank nodes 和 literals。下面是SPO每个部分的类型约束：

1. Subject可以是IRI或blank node。
2. Predicate是IRI。
3. Object三种类型都可以。

IRI我们可以看做是URI或者URL的泛化和推广，它在整个网络或者图中唯一定义了一个实体/资源，和我们的身份证号类似。

literal是字面量，我们可以把它看做是带有数据类型的纯文本，比如我们在第一个部分中提到的罗纳尔多原名可以表示为"Ronaldo Luís Nazário de Lima"^^xsd:string。

# SPARQL查询

### 1. PREFIX

`PREFIX` 在 SPARQL 查询中用于定义命名空间的缩写。在 RDF 数据中，资源和属性通常用 URI（统一资源标识符）来标识，这些 URI 往往非常长。为了在查询中便于引用这些 URI，我们通常通过 `PREFIX` 来为它们定义一个短的别名。例如：

`PREFIX : <http://www.semanticweb.org/vv/ontologies/2024/3/unmanned_system#>`

这行代码定义了一个前缀 `:`，它是 `<http://www.semanticweb.org/vv/ontologies/2024/3/unmanned_system#>` 的缩写。在之后的查询中，我们可以使用 `:` 来代替这个长串的 URI 前缀。

### 2. rdf:type

`rdf:type` 是一个非常常用的 RDF 属性，用来指明一个资源的类型（即类）。在 RDF 里，资源可以属于一个或多个类，而 `rdf:type` 用来标明这种从属关系。例如：

`?drone rdf:type :侦察无人机 .`

这里，`?drone rdf:type :侦察无人机` 表示查询所有的资源 `?drone`，这些资源属于 `侦察无人机` 类。在这个例子中，`rdf:type` 是用来筛选出所有标记为 `侦察无人机` 的实体。

### 3. SELECT 和 WHERE

- **SELECT** 关键字用来指定查询结果应该返回哪些变量。在我们的例子中：

`SELECT ?drone ?maxFlightTime`

这表示我们想从查询结果中获取两个变量：`?drone` 和 `?maxFlightTime`。

- **WHERE** 子句用来描述查询的约束条件，它定义了查询中必须满足的模式（pattern）。在我们的例子中：


`WHERE {   ?drone rdf:type :侦察无人机 .   ?drone :最大飞行时间 ?maxFlightTime . }`

这里的 WHERE 子句包括两个模式：第一个模式筛选出所有 `侦察无人机` 的实例，第二个模式获取这些无人机的 `最大飞行时间` 属性。


