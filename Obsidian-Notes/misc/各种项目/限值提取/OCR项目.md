
| 名称            | 简介                                                                                         | 发布时间       | 地址                                                                                       | 备注                   |
| ------------- | ------------------------------------------------------------------------------------------ | ---------- | ---------------------------------------------------------------------------------------- | -------------------- |
| PaddleOCR     | - 轻量级模型，执行速度快<br>- 支持中英文识别，能识别倾斜、竖排文字<br>- 可通过PaddleHub使用或训练自己的模型<br>- 支持多种部署方式，包括移动端和服务器端 | 2019年      | [https://github.com/PaddlePaddle/PaddleOCR](https://github.com/PaddlePaddle/PaddleOCR)   | 还行                   |
| EasyOCR       | - 支持80多种语言<br>- 易用性高，准确率好<br>- 适合快速集成到各种项目中<br>- 支持实时视频流处理                                 | 2020年      | [https://gitcode.com/gh_mirrors/ea/EasyOCR](https://gitcode.com/gh_mirrors/ea/EasyOCR)   | 不知道为啥，上次不行，这次也不好..   |
| Tesseract OCR | - 内置100多种语言数据包，支持用户自定义训练<br>- 开源与跨平台，支持Windows、Linux和macOS<br>- 高度可扩展，支持多种输出格式             | 2006年      | [https://github.com/tesseract-ocr/tesseract](https://github.com/tesseract-ocr/tesseract) | 太老了                  |
| Umi-OCR       | - 完全免费、可离线使用<br>- 支持截屏识别文字、批量导入图片、横/竖排文字<br>- 能自动忽略水印区域<br>- 适用于Win10操作系统                  | 2022年3月28日 | [https://github.com/hiroi-sora/Umi-OCR](https://github.com/hiroi-sora/Umi-OCR)           | 一个gui软件，也用了paddleocr |
| RapidOCR      | - 运行速度快，支持多平台多语言<br>- 支持中英文，默认其他语言需自助转换<br>- 提供了丰富的API接口和SDK，支持多种编程语言                      | 2023年5月30日 | [https://gitcode.com/gh_mirrors/ra/RapidOCR](https://gitcode.com/gh_mirrors/ra/RapidOCR) | 这个也挺好                |
