1. leetcode
2. workflow框架
3. ocr,llm等模块
4. 小测...测试
5. 英语
6. music
7. flutter
8. hardware
9. ml dl

描述

我是Barry,一个研二学生

目前主要的技术是python,会使用一些pip, poetry,conda.会过c,但是忘了好多了.
偶尔练练打字,
最近有看设计模式,unix环境编程,practical vim,刷leetcode
理想主义,但是并不过于理想,更相信直面现实
也想学学英语,期待能有好的做研究的环境.但是现实并没有
梦想出国并探索前沿的真正的科技(被传言中国外美好的学习环境所吸引
自学过一阵子flutter,但是感觉..孤军奋战?我也不知道是什么原因,搁置了.命名我感觉做安卓是非常有趣的,但是不知道为什么..
硬件,ml,dl,music,都是我想学会的
每周一开组会,由于周天赶工作,导致一般周一都无精打采