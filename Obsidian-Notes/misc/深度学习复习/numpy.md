### 数组创建和初始化

- `np.array()`: 创建数组
- `np.zeros()`: 创建全零数组
- `np.ones()`: 创建全一数组
- `np.full()`: 创建一个全部为指定值的数组
- `np.empty()`: 创建未初始化的数组
- `np.arange()`: 创建等差数列数组
- `np.linspace()`: 创建等间隔数列数组
- `np.eye()`: 创建单位矩阵
- `np.random.rand()`: 创建均匀分布的随机数组
- `np.random.randn()`: 创建标准正态分布的随机数组
- `np.random.randint()`: 创建随机整数数组

### 数组操作

- `np.reshape()`: 改变数组形状
- `np.flatten()`: 展平数组
- `np.transpose()`: 转置数组
- `np.swapaxes()`: 交换数组的两个轴
- `np.concatenate()`: 连接数组
- `np.hstack()`: 水平堆叠数组
- `np.vstack()`: 垂直堆叠数组
- `np.split()`: 分割数组
- `np.tile()`: 重复数组
- `np.repeat()`: 重复数组元素

### 数组索引和切片

- `np.take()`: 从数组中按索引抽取元素
- `np.put()`: 按索引放置元素到数组中
- `np.where()`: 条件筛选
- `np.extract()`: 按条件提取元素
- `np.nonzero()`: 找出非零元素的索引
- `np.argwhere()`: 找出满足条件的元素的索引

### 数学运算

- `np.add()`, `np.subtract()`, `np.multiply()`, `np.divide()`: 基本数学运算
- `np.power()`: 幂运算
- `np.sqrt()`: 平方根
- `np.exp()`: 指数函数
- `np.log()`, `np.log10()`, `np.log2()`: 对数函数
- `np.sin()`, `np.cos()`, `np.tan()`: 三角函数
- `np.arcsin()`, `np.arccos()`, `np.arctan()`: 反三角函数

### 统计函数

- `np.mean()`: 均值
- `np.median()`: 中位数
- `np.std()`: 标准差
- `np.var()`: 方差
- `np.min()`, `np.max()`: 最小值和最大值
- `np.percentile()`: 计算百分位数
- `np.histogram()`: 直方图统计
- `np.corrcoef()`: 相关系数矩阵
- `np.cov()`: 协方差矩阵

### 线性代数

- `np.dot()`: 点积
- `np.matmul()`: 矩阵乘法
- `np.linalg.inv()`: 矩阵求逆
- `np.linalg.det()`: 矩阵行列式
- `np.linalg.eig()`: 特征值和特征向量
- `np.linalg.svd()`: 奇异值分解
- `np.linalg.solve()`: 线性方程组求解
- `np.linalg.norm()`: 范数计算

### 随机数生成

- `np.random.seed()`: 设置随机数种子
- `np.random.shuffle()`: 洗牌
- `np.random.choice()`: 从数组中随机选择

### 广播和向量化

- `np.vectorize()`: 向量化函数
- `np.broadcast()`: 创建广播对象
- `np.broadcast_to()`: 广播数组到新形状

### 其他常用函数

- `np.unique()`: 找出数组中的唯一值
- `np.sort()`: 排序
- `np.argsort()`: 返回排序后的索引
- `np.meshgrid()`: 生成网格点坐标矩阵
- `np.clip()`: 裁剪数组元素
- `np.diff()`: 计算差分
- `np.cumsum()`, `np.cumprod()`: 累加和累乘
- `np.isclose()`: 判断两个数组是否近似相等

以上列出的只是 NumPy 中的一部分函数，实际