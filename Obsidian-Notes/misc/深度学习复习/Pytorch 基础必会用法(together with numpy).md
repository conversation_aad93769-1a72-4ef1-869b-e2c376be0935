## 数据生成
	
### torch.linspace(-1,1,100). 注意开头结尾都包含
### torch.squeeze(input, dim). 在数据的size上加上一维

```
>>> c=torch.randint(1,5,(5,5))
>>> c
tensor([[3, 2, 2, 2, 2],
        [3, 3, 4, 3, 3],
        [1, 2, 2, 3, 1],
        [4, 1, 3, 3, 2],
        [1, 4, 4, 1, 4]])
>>> torch.unsqueeze(c,0)
tensor([[[3, 2, 2, 2, 2],
         [3, 3, 4, 3, 3],
         [1, 2, 2, 3, 1],
         [4, 1, 3, 3, 2],
         [1, 4, 4, 1, 4]]])
>>> torch.unsqueeze(c,1)
tensor([[[3, 2, 2, 2, 2]],

        [[3, 3, 4, 3, 3]],

        [[1, 2, 2, 3, 1]],

        [[4, 1, 3, 3, 2]],

        [[1, 4, 4, 1, 4]]])
>>> torch.unsqueeze(c,2)
tensor([[[3],
         [2],
         [2],
         [2],
         [2]],

        [[3],
         [3],
         [4],
         [3],
         [3]],

        [[1],
         [2],
         [2],
         [3],
         [1]],

        [[4],
         [1],
         [3],
         [3],
         [2]],

        [[1],
         [4],
         [4],
         [1],
         [4]]])
```

### torch.tensor
torch.tensor()
## 矩阵运算
### 点乘

直接乘就完了。不过也可以广播。
### 乘法

torch.mm()
## dataloader
## optimizer