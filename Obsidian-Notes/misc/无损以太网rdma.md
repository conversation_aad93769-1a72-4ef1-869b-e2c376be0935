# 概念
### 什么是无损网络？

无损，顾名思义即是“零”损耗，这里的损耗指的是在网络传输过程中的协议包转发、响应时间、处理时间以及设备吞吐量等主要指标。答案很明显，无损网络就是能够实现“零丢包、低时延、高吞吐”的网络环境，其目标就是“延迟越低越好，效率越高越好“。因此，比起曾经丢包、高时延的“有损”网络环境来说，无损网络在拥塞控制、流量控制、分组转发、路由选择等方面进行了改进与创新，满足[数据中心](https://so.csdn.net/so/search?q=%E6%95%B0%E6%8D%AE%E4%B8%AD%E5%BF%83&spm=1001.2101.3001.7020)海量算力和海量数据的高效存储需求，极大提升了用户体验。

**无损以太网，就是延迟超级低，带宽超级高的网络**

### 什么是RDMA？

以太网RDMA指的是在以太网上使用RDMA(远程直接内存访问)技术。普通以太网主要用于传输数据包,而RDMA则允许服务器直接访问其他服务器的内存,大大提高了数据传输效率。RDMA可应用于需要大量数据传输的场景,如大数据、云存储等,以获得更高的性能。

### 关于RDMA的实现

RDMA(Remote Direct Memory Access)原生协议是InfiniBand。

InfiniBand是专为RDMA应用场景设计的一种高速网络互连标准,由英特尔、IBM、思科等公司共同制定。它提供了极低延迟、高带宽、硬件可靠性保证等特性,非常适合RDMA技术的应用。

InfiniBand使用了与以太网完全不同的网络架构和协议栈,包括链路层、网络层和传输层协议。它采用了先进的切换技术而不是传统的网络交换,可以提供微秒级延迟。

总的来说,InfiniBand是一种专用的高速低延迟互连网络,最初就是为支持RDMA而设计,是RDMA技术的原生协议。它性能卓越,但由于采用专有技术和设备,部署成本较高。

之后,业界将RDMA技术移植和融合到经济实惠、广泛部署的以太网络上,形成了RoCE(RDMA over Converged Ethernet)等技术方案,使RDMA功能走向主流。但InfiniBand仍是RDMA性能的上限参照。

1. RoCE (RDMA over Converged Ethernet) RoCE是将RDMA技术运行在无损以太网之上的方案。它使用无损以太网的PFC来确保RDMA传输中不会丢包。
2. RoCEv2 (RDMA over Converged Ethernet version 2) RoCEv2是RoCE的增强版本,它使用更先进的拥塞管理机制,结合了ECN和PFC,性能更优。

关于这两个版本：

RoCE(RDMA over Converged Ethernet)

1. RoCE是将RDMA(远程直接内存访问)技术与无损以太网(Lossless Ethernet)相结合的技术方案。
2. RDMA允许服务器直接访问远程服务器的内存,无需经过操作系统和网络堆栈,大幅提高了数据传输效率。
3. 但RDMA原生协议(InfiniBand)需要专用网络,成本较高。RoCE将RDMA运行在以太网上,利用无损以太网(如PFC)来确保数据传输的可靠性。
4. RoCE既保留了RDMA的高效传输特性,同时利用了以太网的经济性和通用性。
5. RoCE主要应用于对低延迟、高带宽有严格要求的场景,如云数据中心、AI加速器集群等。

RoCEv2(RDMA over Converged Ethernet version 2)

1. RoCEv2是基于RoCE的增强版本,在传统RoCE的基础上做了很多改进和优化。
2. 引入了优先级感知的流量控制和拥塞管理机制,如优先级流控制(PFC)、明确拥塞通知(ECN)等。
3. 支持更大的MTU(最大传输单元),从4KB扩展到了64KB,进一步降低了传输开销。
4. 支持更好的多租户隔离和统计能力。
5. 与传统RoCE兼容,但性能更出色,带宽利用率更高,时延更低,在高负载场景下表现更佳。
6. RoCEv2需要交换机、网卡等全链路都支持RoCEv2协议。

总之,RoCE/RoCEv2技术为以太网赋予了高效的远程内存访问能力,成为了在商用以太网上实现高性能和低延迟数据传输的关键技术之一。

### **无损以太网的两个关键—PFC和ECN**

随着云计算、大数据、人工智能以及5G的兴起，网络数据呈现爆炸式增长，对数据的处理性能、数据中心建设都提出了更高的要求。当前，在HPC（High Performance Computing，高性能计算）、分布式存储、AI人工智能等业务场景下，采用RDMA协议来降低CPU的处理和延迟，提升应用性能，成为算力时代数据中心网络的发展方向。其中，RDMA网络正是通过在网络中部署PFC(Priority-based Flow Control，基于优先级的流量控制)和ECN(Explicit Congestion Notification，显示拥塞通知)功能来实现无损保障。**rdma是一种新兴的技术，可以借助无损以太网实现。**


PFC是基于队列的反压技术，保证对链路上RDMA专属队列的流量进行控制，并在交换机入口(Ingress port)出现拥塞时对上游设备流量进行反压。在单机场景下，PFC可以快速、有效的调节服务器速率来保证网络不丢包。但是在多级网络中，就会出现不公平降速、PFC风暴、PFC死锁等问题。因此，在数据中心开启PFC，需要通过对Pause帧进行严格的监控、管理，以保证网络的可靠性。

ECN是一种基于流的端到端流控技术，保证实现端到端的拥塞控制，在交换机出口(Egress port)拥塞时，对数据包做ECN标记，并让流量发送端降低发送速率。ECN效果上会优于PFC，但是也存在如下问题：  
• ECN需要接收端生成反压报文，反馈路径周期比较长；  
• 随机性标记，会不公平；  
• 水线设计比较复杂，需要结合网络架构和业务特点来设计；

从无损网络设计看，为充分发挥网络高性能转发，ECN和PFC组合配置时，需通过专家经验来调整ECN和PFC的buffer水线门限，让ECN先于PFC触发。即网络还是持续全速进行数据转发，让服务器主动降低发包速率。如果还不能解决问题，再通过PFC让上游交换机暂停报文发送。这样虽然整网吞吐性能有所下降，但是不会导致丢包。

#### 流量与速率挑战下 ECN＆PFC的“远交近攻”

在 RoCE网络中，构建无损以太网要支持如下关键特性：

- PFC：逐跳提供基于优先级的流量控制，能够实现在以太网链路上运行多种类型的流量而互不影响。
- ECN：设备发生拥塞时，通过对报文 IP 头中 ECN 域的标识，由接收端向发送端发出降低发送速率的
- CNP(Congestion Notification Packet，拥塞通知报文)，实现端到端的拥塞管理，减缓拥塞扩散恶化。

其中，ECN的最大难点是水线设置比较复杂，需要结合网络架构和业务特点来设计。然而现网中的流量复杂多变，导致基于专家经验的静态ECN水线门限功能并不能覆盖所有流量场景，无法保障无损业务达到最优性能。AI ECN则是借助AI算法来实现无损队列的水线调整，通过AI训练的流量模型，可实时预测网络流量的变化趋势，动态调整ECN的水线门限，从而实现对无损队列的精确调度，保障整网的最优性能。

# RDMA提供商

### 阿里云
阿里云通过超级计算集群提供RoCE网络服务：[# 超级计算集群实例规格](https://help.aliyun.com/zh/ecs/user-guide/overview-40)

同时，阿里云提供了eRDMA服务，关于其特性以及配置规格可以参考:[eRDMA文档](https://help.aliyun.com/zh/ecs/user-guide/erdma-overview)

## 华为云
华为云下的超高性能计算型ECS和裸金属服务器都有提供RDMA，后者支持RoCE:

[裸金属服务器 BMS](https://www.huaweicloud.com/product/bms.html)
[裸金属服务器BMS文档](https://support.huawei.com/enterprise/en/doc/EDOC1100267512?currentPartNo=k002&togo=content)

## aws
  
  AWS通过Elastic Fabric Adapter（EFA） (就是aws的一种网络适配器) 充分利用RDMA的强大性能。EFA是AWS致力于提供尖端网络性能的明证，专门设计用于在Amazon EC2实例之间的通信中提供更低的延迟和更高的吞吐量。

这项技术对于那些需要强大网络性能的应用程序尤为有利。EFA使得实例之间的数据移动更加迅速，这对于高性能计算（HPC）应用程序、数据密集型分析和机器学习工作负载至关重要。通过促进更快的实例间通信，EFA确保这些应用程序能够更高效地执行复杂的计算任务，使其在注重网络速度和效率的场景中变得不可或缺。

实质上，AWS通过EFA整合RDMA代表了优化云计算能力的重大进展，为用户提供了更轻松、更高效地处理数据密集型任务的能力。

对AWS服务的影响

在亚马逊网络服务（AWS）的背景下，通过将增强网络与EFA集成，可以在服务如弹性计算云（EC2）实例中获得可观的性能提升。这一改进在高性能计算（HPC）应用程序中尤为显著，其中EFA的能力至关重要。EFA利用远程直接内存访问（RDMA）技术，促进了大量数据集在EC2实例之间的迅速高效移动。这是通过绕过操作系统的网络堆栈，允许从一个EC2实例直接访问另一个EC2实例的内存来实现的。RDMA技术使EFA能够提供比传统TCP/IP网络更低的延迟和更高的吞吐量。

这种延迟减少和带宽增加对于HPC应用程序至关重要，这些应用程序通常以对高速网络的需求为特征，以支持密集的计算任务，例如并行处理或大规模数据分析。EFA所实现的直接、低延迟的通信对于这些应用程序至关重要，因为它最小化了节点间数据传输所需的时间，从而优化了整体应用程序性能。此外，EFA对流行的HPC通信框架（如消息传递接口MPI）的支持确保与现有HPC工作负载的无缝集成。这种增强的网络能力使得AWS能够有效地满足基因组学、计算化学、金融风险建模和地震成像等领域的要求，其中快速数据处理和移动至关重要。

## Azure

RDMA能力的VM和Azure HPC

Azure的RDMA能力的虚拟机（VM）经过优化，具有低延迟、高吞吐量的连接，这对于要求快速数据交换的应用程序尤为重要，例如科学模拟、工程和数据分析等。

此外，这些VM支持各种HPC网络协议，包括InfiniBand，这是一种在HPC环境中广泛使用的高速通信协议，以其出色的带宽和延迟特性而闻名。InfiniBand与Azure的RDMA能力的VM集成，实现了并行计算任务的卓越扩展性和性能。这使得Azure成为运行传统上专用超级计算环境的复杂HPC应用程序的可行平台。

支持RDMA的网络体系结构

Azure支持远程直接内存访问（RDMA）的能力基于一种复杂的网络体系结构，其中的关键组件是InfiniBand技术。这种体系结构经过精心设计，以满足RDMA在带宽和延迟方面的高要求。
[RDMA：彻底改变 AWS 和 Azure 中的云网络基础设施 (cloudpunk.blog)](https://www.cloudpunk.blog/post/rdmavstcp#viewer-a0csb)

