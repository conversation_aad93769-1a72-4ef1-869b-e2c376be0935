括号的用法：对于纯字符串而言，single quote and double quote is the same. but when you contain a shell var like: 'a is $a', 双括号会展开，单括号不会。

source命令 : source xx.sh: this will execuate the script in this shell

shell 中: there are two vars remember the last error code and the last arg of last command: $? and $_

!!: 上一条命令

||： execute the first one, if the first one didn't work, execute the second one
&&: only if successfully executed the first one, then the secocnd one will be executed
; : semicolom, no matter the error code, execute the next command

var=$(pwd), var will equal to current working dir
<(ls) 将ls的输出作为文件


### 关于shebang行

shebang行的意思就是，如果执行文件的时候开头是：#!，那么系统就会将#!后面跟的东西作为解释器执行这个文件。比如文件tes.sh的开头是#!/usr/bin/sh，那么直接执行这个文件的时候相当于：
/usr/bin/sh test.sh

比较有趣的例子是：如果文件test.sh的开头是#!/usr/bin/cat，那么直接执行这个文件的效果就相当于/usr/bin/cat  test.sh, 然后test.sh的内容就都会被输出出来



The Wikipedia article [about the shebang](https://en.wikipedia.org/wiki/Shebang_(Unix)#History) includes a 1980 email from Dennis Ritchie, when he was introducing kernel support for the shebang (as part of a wider package called _interpreter directives_) into Version 8 Unix (emphasis mine):

	The system has been changed so that if a file being executed begins with the magic characters `#!`, the rest of the line is understood to be the name of an interpreter for the executed file. 
	
	To take advantage of this wonderful opportunity, put
	
	```
	#! /bin/sh
	```
	
	at the left margin of the first line of your shell scripts. **Blanks after `!` are OK.**

### 关于shell的字符串

shell在执行一个命令之前，shell会做两件事：

1. the shell looks for **variables, globs, and other substitutions** and performs the substitutions if they appear. 
2. The shell passes the results of the substitutions to the command.

单引号括起来的内容会被当成一个完整的字符串。于是乎如果你需要：不要替换任何东西 或者是 空格被包含在输入中，那么就用单引号括起来吧！相对应的，还有双引号，双引号也差不多，区别在于双引号会替换所有的变量，也就是$1这种美元符号开头的东西
