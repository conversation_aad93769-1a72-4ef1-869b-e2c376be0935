
🔹
Guidance Agent（引导者）
入口Agent，负责首次对话，诊断学习目标、背景、动机，分配后续Agent
轻量、快速、决策型，不保留长期记忆
🔹
Subject Agent（学科专家）
负责某一领域的深度教学（如：Python、线性代数、神经网络）
深度知识库，专注短期教学任务（1-3次课）
🔹
Practice Agent（练习教练）
出题、批改、纠错、带做项目、模拟面试等
强交互性，注重反馈和迭代
🔹
Mentor Agent（导师）
长期陪伴，跟踪进度，调整计划，激励心理
记忆长期轨迹，情感支持，战略规划


这是一个辅助用户学习的应用. 首先,用户在guidance agent聊天, 表达学习意愿, 然后guidance agent会根据用户的知识评估用户的水平,用户想要学习的程度,询问几轮对话,然后

1. 生成一个task list,task list内容就是用户的学习路径规划,也就是一个todo list, 每个task对应着一个subject agent
	- task list本身可以组织为一种类似列表的结构, 每个列表中存储一个task, 并且记录当前task的阶段.
2. 生成第一个subject agent, subject agent就是对应着todo list上的单个task.目的就是为了教会用户当前task的内容.我们期望用户在和subject agent聊天的过程中能够学会知识.

针对用户的提问, subject的回答, 我们采用下面的数据存储结构方案:

- 用户的知识通过多个json存储, 每个json代表了和每个subject对话的聊天记录.考虑到知识实际上是通过和很多subject agent学习的, 内部应该存储多个和subject chat的聊天记录,并且是这样的结构(具体文件,文件夹名字你来确定):
	- chat-history
		- task-xxx
			- subject-xxx
				- xxx.json
			- subject-xxx
		- task-xxx
			- subject-xxx
- 在每次和subject agent对话结束后, 总结当前对话的所有知识点, 打上所有知识点的tag.我们期望每个subject的对话历史能够映射到: 那个subject 的task name, 以及多个关联知识点.

后续, 每个subject agent教学结束后会有一个practice agent, 来给用户出题, 解析等.同时, practice agent 也会有更多功能,这里暂时不说.

功能描述暂时先到这里,接下来是界面描述:

整体上是一个类似即时通信的界面. 左侧和右侧比例大约2:3,左侧是一个list, 代表了所有的task.刚开始是一个task都没有的, 所以可以留白. 右侧就是点击某个task之后的聊天界面. 设置一个guidance agent的入口, 这个入口代表了用户开始提问的界面.

guidance agent应当有以下工具:

- 查看用户当前知识掌握结构
- 创建task

一旦第一个task创建, 那么这个task的第一任务就要创建一个subject agent.你应当追踪task list的状态, 当前到哪个task了? 是正在学习还是已经学习完成了? 是否可以创建下一个task 对应的agent了?