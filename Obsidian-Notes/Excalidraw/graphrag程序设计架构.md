---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data

## Text Elements
first of all, i have to say that graphrag is orgnized as a package.  ^rd00ZEIW

typer层 

控制命令行界面的交互,包括帮助信息, 自动补全等 我愿称之为路由汇聚层,仅仅起到一个汇聚路由的作用,仅仅关注命令行传来的参数的信息, 比如补全, help等, 关于函数本身(比如index, init)的功能, 没有一点关系, 都是定义到别的文件中.   ^eDDbP6ja

service层

提供了index，init等功能的实现。但是这里也只是框架，负责这些service上的整体流程的控制。比如开始前的日志，配置，信号量，等等。核心处理函数还在下面  ^bkqc2Ec1

api层

这层实现了核心服务的核心功能的实现，是真正做操作的地方。 ^vSJYz7Ld

utils层

这层是我自己抽象的，是对于数据库，log等基本功能的封装，是系统本身的基本组件 ^oqQYgLQ7

%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQB2bQBWGjoghH0EDihmbgBtcDBQMBKIEm4IUmIABmqALQBRAEkAdVSSyFhECqgsKHbSzG5nHgAOau1RgGYATgAWADYearmp

uZ4ptf5SmGGeBamEubmARmqk0dPL5bntyAoSdW4FudHtOfiZpKSpnj+FpJLO5SBCEZTSbg8JLA6zKYLcarA5hQUhsADWCAAwmx8GxSBUAMQnBDE4kDSCaXDYNHKVFCDjEbG4/ESFHWZhwXCBbLkiAAM0I+HwAGVYPCJIIPLzkaiMS1HpJIUiUeiEKKYOL0JLysC6eCOOFcmgTsC2JzsGpdsbasDacI4E1iEbUHkALrAvnkTKO7gcIRC4GEBlYCq4

E68ukMg3MZ1+gOFAQIBDEbgneJzb6jE5TarxYGMFjsLjG075pisTgAOU4YlT8QW8W+a2mgeYABF0r0U2g+QQwsDNMIGQ1gplss63cChHBiLgu3XXtUTlCVvFPsCiBw0b7/fgN2xqcnuL38P2E9A+hUBSwoKg2HzUAQ96hCKhJLhGKgoGxUMxcDAv3fW9aVwOBJHIZQX2YO9SGUDhCCMZNH2g3BUHNNFogQbR0F1SgABVLwka9kTvB8n2oF83w/BA

vx/P8APUOdUBAsCIKgmC4IQpDQkfNCqQw5QsJw88+U4KBhS4yFEREsSADFcH0QUrVQaFz16TAoAAQSIOCKmCPl+jLUgoHMAhtLBYt0G/OBeT0bJcCDJgfTQOM93PPEwSDAgCI0q9CBvUjHwDSj30/b9f3/QCmJY8DonYvFOMQ4hkN49DMOw3lcCEb8ACVwkktAUSEBAN0cgAJUFwX6Y1tChQoAF9tmKUpygkE56BmQhMAAVTYAAFU8AEc+r6pIAB

l4mcAB9GAAGkADVeS6cR0ECbAog4OEkGBIY0GcHM4mqaZqimNNqh4eJqgbBZgWU5w00OXMkhmZcPkzZ7bnPB5iCeNB6wWbQThmKFTp4OZc2WKZgUkSqIRLVSOggWFNWkxGZVVJk8UJUkSW289KWpO16UZHEsdZcgOA5LkskMkTBRFMUVogbUU2VWUEHlH7FTQPhz3RjF1U1ZmcR1c89UkaNnRNdzzUtVMbXPImHSdfJ3REr0EGc1BXMDYNdvQXAe

AjIdiEl7hmtKZbuCmBNGr5pNu1QZdRgWGYVgWBZpcRgsK0sqYkl573yyLasOFrY1/cu6YpizVsO2Cecez7Er8ZNkcMhpicEyKbPOngJn1NpjoWsdjA2zbTQ+oWAArXAIGz+qEzVxHp1nROnfTT5RgDhtcxu89N23Fzd33Q9HZPMIGqa89WvQBBy8rmu6+BK3WUvHa9hmAGkn+8G1hmYGPih887pOBY3iel6wcbC4PuBb7ftQG/Ac+dNu/9k5AUD0

oYbBOGnf9jCTaKM2YY1JiydARJcZkgHPxImDJMYQOgBTKm3Ii6lAFEKQWTMWbShVHKBUSo+b4LVIzCouDdTCH1IaVMppZawHlqjUoStHQTmbhgjWWsdYzz1qGHgi1KHEzNsPeMaMHapmBi8WorsZhGV9twUYLZzw+xDjWFaJxVjXymOmWRM92ydiPEnU8KdEaDmJunMcORVZThnHOQxHcPgzHfpHDMpUtw7lEaUXEY9jzJxXoRKy+dSCACEdVAAA

dDgETADlxoANiVAC+KoAE7lAAyEYAGVdABG6YAELdAAlcoAJLlqCAFBlQAN0aADo9QAlUqAEP5QA9gYUUAFeBgAKpUAKYRgALRUAJLeqBACIRoAfkNAAPnoAaTlABccoAe9jACMroAcRtABaAcE6ggBRuWmYAd1jAAMSoAADkImACo5CZIyMmAB15QAFK4zOmYAZ0VAAXNokpJgACeUAKbmGTABDyoAB1MMlVIooAFetABBms0iiMN8BwBaRRA5g

A4uUAL+KtzAA05oAariIkAApXlBmIFgCiQY1AAEoMmAHylQAvwEUUAIU2gBIcyWYATocDmAG+fCigBfhMAPRmgAs7UAJJyCzADUShkwA4aaADe5QAtHLYWEojcgFAfLVUCYgEJ4TIkcFiac9J2S8lFLKY81A9TmltK6X0oZYzJn7MWUs9Z4zNm7P2cc05lybn3Ola895b4gjfN+YCkFoKoUvJhXCl88EoDIvRVi3FBLiWoHJdSuljLWXst5KJbIE

lEJSQ9HJBSSlnj+I0uZXSEgxDZCYLyAsJl3CxsshARSxBiDwmBHZKIjlSBawgO1TqPV+pDRGuNSaM0Fq8g8v4byAToBBNCRE6J8TklityQUkpFTqkysaa0jpPSBkjImVM2ZaqNVar2bM3VyT9V3IeQO41TSPlmp+agf5QKwU2rtZgeFjrnUYtQDi/FRLSWUppfS5lbLUActKFlXK+UQ2FVIMVNxCAKp/15ScbQ/sp6FAtmUUu+hup4XOmNdYmIAD

iaIACKLQAAa6IYCyVknAauS184VDWhtLavJ9YjC3gBgOmwfjzE2CcUYt1hiJGBhsHgL1RjxBdisUsX1CF/QuJMc4JwNGsddoor2P9YZ/s2JMdYV1FiNhmDHWj55kYrSYQIEhiDsbQLxqYuBkYSbMh6CgzkaCA302weQkWrNiHs05o/b+an2bmYlJZ42fgJY0ONHQqkctrSqYgCwlWaBJzqwUprR23DEYHtDFMVzUYPOoAtnnboaAbYdDtmI+x2Ya

OKMUc9ORRZuBJAVkHQsVY1GQleAsc4SRP5xwMePPxqdzGjkzvkbOiWksF3XrnUDFRNBokGtgHgDRsDhgbk3GxbdMtvyXBsKrgJj6I0Hh4tyS2DwYga8YoDJQQOzwgP1wbw3Rs4eS1ZbriNiNjESFVrebsroB2XPWOjaA5jaAPqxk6n8lxHS+PZiAD9uZP1OABoGowwY8Bo42aG4n5aKcRsphEoCMQaYkFAnGvICY0j0yjqyRnqY8g9GZshzmpRI4

5tx3gZOnNahc4I6hMZaEy28ww3zto6TKzYR6Th4WR48NhfrJGcxYum3ixF0oYR7HNhdjmQByjg6cG4AfRbpQVFlbDitX4kNweicgIQfRCd7ETxMaUMxw4WvjmseeVudjHZpkXMuKYLxxh5gHkGIe2tedrZ8UYs8iNC7kKYGYMQwT20cEAAvGgBs+UAGFyB7AAw/wiqALT0UZMAHnagAG50AEAMgAxeTJYATfjAAziYAfTlABXymSwAYhaADbzWPg

B8WMAMSxufADZcmEUgQeECACg5DJgAXU0AMrygBBWwiYAaC8MmxIz68wAAPqAGnNQAskoZMAKemgB1/Vj4AWUTAB2/rH8pgB35UAPOJseWktIz4ADgtADD+oAEE1ABgLkCwAG/GAAp1QA0HJpMfZALlPKA8t/MAgEPQrI8x71vHx1Se0VU9M8c8C8S9y8q869G9m9W8O8e9e8h8R9x9p859F9V919t9d999j9z8r878H8A0xJg0Vplgw1sh5JFJ8

BlJ+4/c+h00KgE1eh8QjJU0zIdIM0s0c1tNSh80HIDQi0wMIMoMYN4MkNUM0R0NMNsNTRSBPIOAm1fIJRA939P8Ilv848E8ACgDs888i9S9K8a968m8lCxBYC+8ECYlR8XlJ8Z8F9l819N8d899D9T8L9bkb979H8kZso2A8pWA30vwP1jdIBNxv0YcapAMSh0sc5ItS4ABNAAeU0jRDKiEAaExCMGqCEEwByn0AaDqByiED5FgxSBXlwwkHwwRw

3j2mYzeEPhenBiOk+HWARh2FTGB3WBjmqAPk9j+H9mV3uAp0bDeHGBq0E3rCcVjnPF/iqlTEk2uBkwbGegUyAUIzQD835ixHAU03R1gUJmxy2PJnZGMxplMywWJxp1J2s1VFs0Bz+w2Op2FkuM5SoXcwZ08yZwtBZydmK2YXZ1YUt0Rk9FCy4U9xal4QkFwBKLFhNmEQS2zhbVO1S0iKRHEWNCK3OnBn2BdxK3kTQGehaMgFVw4FDnDhUnIz+E/g

JLKH1wQHbiNwHDTnNysSC3ax61XjO18juBLgqHoGFAAClYijB4gxoUxxsOh2FIBrd247dxhXoBMnEdcIBlsRFVsvF1tDdk5ttojuSJBeSBShSRSTsutOTzxiMgYZhkhejAQd4Tpag/tlIz5EhLhXh1hmMXpgZgZ74KdAQ/Npj/5Jj4dgEVMycccIA0dcYMddMTZQy2RKZjiCc6YziNQcFacriCEuYiE0YSEHiKFoS3NYTFSzRmcHSfjIAAtOcQtv

QedPFddwSDYFhhdYSxdEx7Et4+jRgnFsSVd5dLJlxcx8s1dSSPpThHsqS9d45aSNTjEGTmsM4LcgsJSIApTpt7cwZ5SAyvE3cVtR4NtfFpy1Jm1QJCAVCOBc9gl08o9j9ABYc0AEKlDJY/ZPdPWPMlQAfHdABja0AC0FQAWZMtkMlAAGdUAE7TDPCMfCQ8uAY80PM8i868u8h8wAp818z8n8/8oCggoNAqXgPzQNKAcgyNNAagy2Wgtg+gmmJNZg

0yfAOgiQDg3Nc8HgwtYtBIpIlItIjIrInIvIgoooqExGBtLyfAF/CE8Ck8qCtPS8o/W8+8o/R8tPZ8987838wC4CmEbw3wjCoqIIpU8qMIp2MjLU3bUuZgWI0YSsJoQaZQaoTQBoYgHAPXXAKafQekTEI0vDBAdaSo004YA6SYIGF4eIM+ATAOT6RGZSMYAGNMZ3djJcc+OHUoAHbgIYvjUY14cYkTaHX9WYw4eYl4RY+TRRFYkBNMzYgzVHHGGB

fGKM4mGMvHEzQnJMoWXMrMmzCnO47M84x40WZ4/M+LQs+hEsvzcsgEjhYE6s1U2s/nUMeIRs+LRLBEjXW2FEzLBsVYb4I6fohgHs54GrAc4k8rfC2oF2AKxU8c+rPc33E3Rkuc5kl0Vk4uTrHoc7YuXrCQNgQaBDWI5QMaBDKasUkoRc5c23GbOUqORU5Uj3GspU9UzbSeZE4DGeUuV696z676lyteE0i7PYVjSYNcI6ATNceIR3Y4Z7XgA+bQLo

l6M4PK7MMHL0jMv6MYN7c0sHF6JcL+dKmY60WKyABHNYkMg4yBMqrgikSqhBfm5BI4/HdBSATBBmZMizJ48XEhG4zMxWxzdqxq0ocWAsrzT4/qtne0f4hcrnEa7cvnEMCE0Yaat48Gsa5mVEp2MYMYM+FYdaoktoomuXUrXa9XZ4RYU4DYHeOrA3aGzS03YgCxVrI2q3WxaUoGsGF4T+YKzc9xFUncqc86zoZtbKQUZgES4JMldpGpQAR91ABeo0

AEMYjJZ8wAT+0/lblAA7Y0AGS9WPXEZQFpQAL/VgVk9ABAHUAFGI58wlQAfb8wUMkO7AARvwiSZRAu5SzpMlPDzoLuLvLsrrJRrvrqbpbvbs7sAN7v7qHtBRHuBVHsntIPEgwpIJkjIIjUoKjQPJjWIvjVIqYM9pYMovvvQBoqFogHor4OLSMpMrMosqspsotD/AcqcvrRkMbQEpnpzvnsLtLorurtrsbubrYFbo7u7r7rJUHuHrHuPqU1UtfRWg

0q/R/Q5t0oiLACiIMoqD6lg1iJgDqGsExG6niOrgcu6heE9GFAaD6lRtWjcoI1ooxr2k2Fey3nODmC+GmEWKTsgDumWAtPPicWYx3nrHOl+FpsfjB3/WzC3ldI7L+H2HZv/ikTJrWADnJqXDPgIu5qDMRyKtDPDPKp0z2OjLFtjNQROLqtloatTKauuJaqp3Vv8c1peO1o+J82+IGr+MCxdEXKBKrNNsizrKRhmCtudFmvZKRKocWsdhmHiH2HBm

+CpLdutFKZ7JJPUVmAuHPmY3WpOuDrOtDsussQrJbhjpXNlPjrB1mDcXd2bMhu91QCNwanAGbiRjgDgFFDsXNkKGgBhkyD0gyu2AYEIAQAoAACERb9MyZIE+QDnDmBgv6RA0Emheh9BRR2YnHBbjnsBTmaZzmMhtm3GqqPGarvH5n7nSAzmLnZIic5aSdOrIBvnfmMgrnAm6bKcvmHnsgnnLm2rAWLjgWTmfnHmLmcpwmerVnQX0WMh4i+rGEcXY

WoB4XZJw0KCqDiW0W4W/nCCz6mFUWwX9AeUqL0B9J0EmW8WEWuQtIfm2AKAYZcBRrqXmWGgGRNJ+XBWQhS4uRUQqBRXuXJX5W8IyjVoTZjnmBsBUQhRkNnhzh3hpMHtCngYaNVmtWdX8BYjngCbJgIcPgArFg1hv4IAjA2ADA5nvYCBioERGaCbTotSuXaWMhMWhF4tKgNXVnaQSAiDQ15no3iBRQEAbI8So2ZDiAABZNgWFcV3ATQYIEOtNkgHH

EDTZnEUuWCSkCFCHPMXgNMCiGtiiCYJIRFXkPKZQf0LkPDZQKtjYREXgHMBtwd1AZt1twN3FoNEhAlkyTgWMUE/kDWPKYMGQzaT10oLIPNgt7gEhuiogFNgIz9c8OQpZ99Q93i7KEI7dwIwNuwauQR5gYUOQuALNnNuQ/N9O43JGdaQgRgPCd1/ANd+6xQ4Ib9hXPNIQZEAwVV07QZ7xXcn3TS/AUILSb939/9lbMZ3JugTBcIc2RueqIAA=
```
%%