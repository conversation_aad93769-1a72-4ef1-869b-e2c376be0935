---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data

## Text Elements
user  filename  hashvalue ^Fw9A7UYH

filename file_hash ^XK4E6m01

%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQB2bQAWGjoghH0EDihmbgBtcDBQMBKIEm4IAGZMSQAVAEUYADEATmYADgBlAFkAcQARSQB5SoAtCgBBAGFUkshYRArA7CiO

ZWDZ0sxuZwBWdvbtADYABniARnOjo5aWnnOD/lKYHcrKpISTlvjKnhPd3bXJInI5PSAUEjqbiVXaVBLnJLneI8SpHX7tc58QqQSQIQjKaTQ+KHHjtXbIk7tHi7c4tWFgiDWdbiVAnBnMKCkNgAawQUzY+DYpAqAGJzghxeLNpBNLhsNzlFyhBxiPzBcKJJzrMw4LhAtlpRAAGaEfD4TqwDYSQQeQ0crm8gDqkMk3Cxcwg9p5CAtMCt6Bt5QZSoJH

HCuTQ5wZbF12DUL0jJzZ2IgiuEcAAksQI6g8gBdBlG8iZLPcDhCM0MwgqrAVXAnQ1KlVh5g54oehYsyrYgC+7IQCGI3Ae512LQOCIZjBY7C4kZ4LSnTFYnAAcpwxND2mjiUj2lXmP10lBB9wjQQwgzNMIVQBRYKZbI5/MMoRwYi4E9DyPxJItVFHFcSTxP8DJEBw3JlhW+BgWw8qnmg574GEhR9oU7aQOUEhNBQLQTPEACqACaAAShqdhUJ6YFAh

rbGgeykto7R3AcbxJru7rPK8ILJDwiJvJU7EYtSDIQsQUJoHx5xMZU47nL8vxJPcjwpri+KEpJuwMky/rJh6Xq8mqQqipKEpIFecoKk2qoCsZmrkBwOp6lkNGFqa5qWiynoCkGKYGQgzria6knspy3q+v63m2sGwihuGw7RrG8bDkmDJpm+WbPgWKZFrgJbfqg5aVim1bELWEi4DwjY3sQLY5kVMF+QOBV8UcFztEkglLjOnDcLsemlNOK4cOuHC

bpJJx/g8SnbgeR7BF+Z4XggV41feGQufV0Gvu+n4IagSJ/gBtK/l8YHVpBaANbB8EFUhl4plRNESEIYSkKgqAmsEHB5QgH2SKEkj0AQQjmSm5AULUWDPegr1MB9X1ZL9/2A8Dvhgx6RqcFAnSEEYLJ/IW2NNHlpoJqgWmPdDExEMoc7oMERquSm05QOYBA0/i9PQDGhp6NkuDVkwpZXdtKZCvi1YEFD1EVHD72faaSOZCjzBAyDGOlLgQhQGwABK

4R4yynKg+dYYkXiBIw9J1KoU8GFlAVEAfnjzC1NgABChDdC09SdH4cC1D9RxEeR8BecsqzMrROwotoPDKYBVJJoJnGQOTzjvCcnw/OSZwtF8lKiS6bpjtotzjvE8R3C0RztPElMempVtEiSZIUlSNJ0pU2lrLpoUOnytkaugYpmVKFnyulKpGSP0AOU5+rM5j7kRV5gZDgP3qBRJvBb7ya8VBv1V+JIdUJeLSWwClA2QOlmbZvk2WY8WCAi4VYse

qV5XoLglQn82eKaAMLzHDtCXs/Z9qYiOLsJS1J9jdWGm6C4iDZyjXGqgREGJAJ3DTmUQ8x59r3RWima8ypiDrUfDkJ+O0PyLR/EdZE9dLjkjNpdD+xUPSCluktZCCA7boRKk7AAGgAaSSLeI4+gTjnDDosTU0MY6RkOOcEE9d4Fkn/A3BkGdKjyU+OOG4lQOrxBgaCFMYld5IjhDwau24ET9VpPSVSlsNK8EblrPuLJb6ejCoZYeJlx6axlJZaeN

l1SUQXrqJehovoeT9OvHym8/J+ICiXEKKTB6H2tEkk+cVWwXw9DGOUyVEw+PvplGhOVX7v2uiVGsdFf5HAAbVIBHDGr6WasODEPS65JHMR6Ias5un7hZsuNBG4WQPCOHxAESIfGEAIQtIhy1VrkMoZtKCnDShvjoVA38/5rhONgQM0o4F2F1K4XBXkd1VlU1lhIRGP0VaIwAPoAzVo2SgMsYbGiVs8v6byPmuiJtkXG+M3Q+KxtkEm+gyZ9QZE9T

mdMKiM2XoNJgbN3DIu5rrOAfNsaCzDKQWpn9SgS38NLRRjz/nIyBYDQ02tdYG1YBCtAJsSFcKFhbdS1t467AESUB2WF0DKDYLsOAAArXoowQWPTAQo2WDJGmZxpNoX4CJkRXGrnXbRKZdFJl4vxNiZxhIePBOk1Aoym6uJhiJFMOlvH7yHhEiQY9TKGllFPays9InamiS5WJq9PJH1yc6newU96ZPCiGnJ0VwaxTPm0qMl8SnXzKWlJUD8sqFhqQ

VS5pRv6NMZPEFp59RbbIEF0yMjjERMLwUM3qaAe5jJ6iNSZw4WhJCpO0f4k4SpLIQPQ1AHK1l3gfJsitHSdm7WHYdQ5gEATAXiGwrZ07IDcJubwh6HZqXoC+ZDPdEBYnY3BQTKFxNSb4HJua6A1NabczRYaVm7N8A4sorzBk/MohCxJfmslkAKVS3wD8iojKdb60NmykdpBTYpnAggHlLdIz8tQuAZ+jI4BwAtHtbg7ZoC4kyKi3lmwGCEAQBQD2

oSfUBLdUaejDHSPYBEEvDMJ59AWkHr6t1pkJ6FAgMx0grH2NUe9TVbj6AtSOQDQaJ4AmWMuTYxkJowaEmhvjaUQTwmMice3pazi8mhOKfY7pg+saAxhv41p4zGQ9aJvLQdOT1nshKf0EMK+5NVEDUM9p/QTRL1wuvQiqzCmXPsf82Co2kKnOhagK5n576JBPpi0ZsLOmoikCgBMITbAKC4lwP+7ZPmbP6FvCqbLXI8shCdnqSrTHYuuYq7l2oCr0

DWVI8wbAXIzTCO4IBbODckxjkBKo+uK7+Ode6/gUOaB+m7HLrAloDwMQglsXJowbADB4ZZhrYcxwTikhRIKyAzm4vsbs+QhzEB2tycVCQM90X+N3eIBaBA+K0AeNTKQEg3Q2BlTK7gTQwRbl8Nu998JdlUAOw9gKJ2pBlCygABT3BXbwJE1A0eo+zrsAAlIaA2ygKx6iWAj3AyOuq8ApyiNkrJtC44gMd4rYLUnubZpwLaRXcqZANjWb7axtseiR

kD/ao6UzYCIO9mDcGPQAu4KLopOsENy9g/wkoaFyWaElQgFYzBOg/TgL9/7P1hcg53ZAOUbNGC1E2/gAXpQKLWnSCsYZX7Xq630C1+R7SbpbsQncrhoQsvO6tzbtdaGwDq7+cENsPYQA9iAA
```
%%