---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data

## Text Elements
[[Transformer]] ^CaL7Qi8X

- transformer 原理学习
    - rnn attenion
    - self attention
    - 参数量计算
    - mha
    - mla等变体
    - 手写transformer
    - build nano gpt
    - cs336 ^BPcq3t2b

## Embedded Files
898289cdf0bc71a889a4a03e67b59a12232891ee: [[Pasted Image 20250705142551_966.png]]

%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQB2bQAWGjoghH0EDihmbgBtcDBQMBKIEm4IAEEARwBrAFFlBGqAZQBZFvwAMRgAfS65YgBJACF61JLIWEQKwn1opH5SzG5n

HiSAZm0ATgAGbYAODYBGbcP43Z5j+KXIGFWkgFZH7R4ANl3L3beD7beTngbW4QCgkdTcR48F6PeLxR5JN5PDZQ7ZAwqQSQIQjKaTcN7vbQHN7bU6bDbxA48eLHYHWZTBbi7YHMKCkNi1BAAYTY+DYpAqrOszDguEC2QmpU0uGwtWUbKEHGI3N5/Ikgo4wtFWSgEsgADNCPh8C1YAyJIIPLqICy2RyAOpgyTcPjo62s9kIE0wM3oC3lYHynEccK5N

A011sEXYNT3MOfYFy4RwIbEUOoPIAXWBevImRT3A4QiNwMIiqwFVwuyt8sVweYacLxddYQQxG4xw2G1Ru3iSSSTNdjBY7C4aCpB2BQ9YnAAcpwxNwNo99n2fhPXYRmAARdJQVvcPUEMLAzTCRX1YKZbJpzPAoRwYi4PdtsNw47Ei67DZHN7AogcWoCyLfA/zYGV9zQQ98GPZsoigIQ0wgRBFVLZQrQNYJ8wkX5KV+bBiD1XZNGwalcAOX5cCSSsN

gQN54k0R5tlwY4eEBHhfmOBBFmbdxxHTdEwHDSZBPRLNXWwNk4CAo1CgAXyWYpSnKCQhAOGAAAUAHkKAALWcKA2B4bYjE0Ch8EeABpABFTSrKtaY+IgUU2SoYEVjQZwu12bQe0BT4nlhD9f1dWNUH7bQNieHgvw+di2PeYFQWIcEx0BZJHjo+JvyeR5TnhYFMWxXE0G2F5jl2JJ1iJN5rhY1jaQ4ek+IHSY3VtLkeT5CoAGJOL67jWqlGVEwVJVO

tVdB1U1MUdWzQ1jVNRy/TbZl3XtR1nVW9qvR9a0eX9V1A0kOs0yE0pI2lGN23jV0RuTVN8jE1qc1wPMX1QRsQI3Mt3PQXAeGrM9iBO7hFNKBznXReTmy496u0ecldiOF1WqnEc8X7ScmGnDg5w4Bc0AuViqXYlqlO3XcINQKCYMGoGLwybUbye0p70fZ92zfD9Pj+L8/1LQC0E+0DwPemmEGBOA2FLHJ8gEgphLAMnJl2ASWZKBXhNYrYnky7Lnj

ypJbhKZxSu0crKqSarauJtXRL/UIoG5fR9DUZ91Ol8UheA1bRSgEZS0cRrQYEjBL21LD0E5XAABl4iswgDgADV1fUwIQxlCS/TZtkBbZNgOfsoWNiBlFwKS0AOCKiSSUlotynmjdDg1MFbD2Zek/AoeBLJiADlDg+9ptWpZP3KlIFzMVwd7hddXvx8nkJ3qcie2Fcuf8FPCgqfFuSFNdPdMB1CRAF4NwBZnbyPIABVyA1PU+UyUgMwzdDOCgFpCC

MPjjir+FopJN4kJ1h/H+NmN+XRXqGlCo8YEh8oCVCIMoUc6Bgh6lmoOJgUBzAEAQdiZB0BIxWmUlHWO8dE4p1gfARycCrS/WcBlRI5USTUgRoXDY+JgShQ2N5b4ucvzUl5nCG4rokopVQO+KugCKq5TeB8Y4GVi6ukKjiY+vAtjXHiDVXWCMWJIwak1RkW0PTKi6hIXqCB+pWiGrKGsY0VQClvtNbU6F5o7UcpIaUGhAhWhtB6B0yUnRhiMRyNxF

RlqAz8MdEM7ZgQXWjLAa6ysIC2JBkPL6I9YbcAONSNEqNsbozDAlDBw5Zzzj4kcGEjFLhN1amzJ8VNrgyL+GcfYlISwU2CBzSCR4Ja3XlPdZmJ56bh2vHLYSYMlLL1UhpbSekDJGRMmZSyNk7LGymFQisq8qACVkvbV0/5BboEvjfIU99SCP2flaXkosDw9OBHobIuBSxMEjrPVqfJsSlgIFfLAqiIDn2OY4s5FyX4BkoD8o+FRAWnIfkwS5/Ngw

AAksQqPbK8GBrpcBCAMgAJXCJ/PirIhC9NaoHcsEhcAbAgHvQoYMyjLxGOpbA1QNhQB4JoeyGy1S/NoasGqWx4QHBYsiTYCJhGtVCvQwB5sYTw2eEjSEGLWqiMCWopICQzgVPrj2IVKNSjKOKrwRRrU6Q+iSb4jkJiJoQHMZYk80obFAytQ4oUIoZouKNKE80+0VqwXav4sReqBBrU9ItMJPqIlBmiUEiMUYrpxiSXdFMgzXQvTep3EsP0KwpADE

DVJH0fYwwaexYkXkMpYxKcg9YNTSho1KfjPiiqaoXHiEGsoHSEBdOpnc10p5RoMyvLLNJd4Hz1Peo0982wsqIgqkkg5Gb9lgQ5GLHtrUaESGcKgKawKmCoEAPnKgAwF0AGbagADOQADocFQFe1Am7SAcEvU+PcHARwXuvTe1AYR8B6lQI+7UL7L3Xs3YAIeVAAOpoAecTACF0YAdO9X2AdQPoDxsGr2bv0PgXAgBJb0ABvKgBleSQ++wA0kaAEzF

bdsLSB4c3ZoIQhpiAfWsGwVAyg4BQHI6gbAzBOxvGrOCnlG6t1AtI/u4956APIdQHeh9Mgsj/rfZuz937f3ZGk3B0DkGYMiffQh3ALHUMYZwyxojJHzlMBY5R6jtGOD0cY8x9Tm62McdftkD+X9nRJPvtkCBrt8DQNgb83BSCKioPQXk0gWD3B+fwQZKS9y35PODKQV5hb3mkE+Rwb5PH0CbsM4/QTp6WPiZ/ZJ59nAWNyYK0+rBxWbOoBU9B7Ti

Gqs6aw7hqrBn+NGbI1V0z+AaOpYswxpjLG7PsKtFi3F+LnNoCJSS0o/4EDIqKqo446K5LgCek5OAcATT1JDuDTEmQAsLYlAwQgCAKAjAdSNRUzqzF6lu3do72ARAzSGHufQJp2rXfQLaixA1ICPYnhHV753hq2M+9ARxbrnFLAgP957r2uiuLDd6y00PYeA4yO9vxG0xyo6e+jt7IavW+gjbjgH2QXsZBxcIKN9YYmFBh3j8nr3NJxoSQm0ncOMh

dHAZArzEIOf4+545glLmBdM4yBC+BiD8GBYe4zqAFOCdjy2VPGeiW/vy8V/URUC816q82S5OXZOFevd1xQK+XL0C2KO8wCSPJ8Ap0Jl+bQxIradk2JCLKNbrR26NAATTxDVZIuxGJXF+BVfEZ0IBGDYAYHbtaCDEsziwx45EaWlDR+L/QVPRr5uSUDI7coSBOb4tFaHRfiAmgQBXVASrIAV7aGwYgCBte4E0MEFd0Fpul2S3Y0xqA6UjB5MvUgyg

pQAAorg3F4NcagM/p/eUeAASitHi5QRZRQVFHxPvyc/d9qKZKgRfK/08a+N5jjkLOKsagXaUNNCA8VlmS4PAfc9Uvt6plN+5RAa9f9dKl/bSbUgYlWJbFWbbgKbU/CAOwAAKwQGwByBaFSzgEb2b1bw/071pkgGlCwUYCvlj3wHj3WRmHNHSAQPRnuQQgMn0AtxIILWHhmyXR3lXRm0dkqHILwIIM7hWzAGhn1ENHCFBl2VkiAA=
```
%%