---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data

## Text Elements
#  cli.main -- typer发挥作用的地方,专注于命令行参数的设置


-> cli.index ^nj403lrk

# cli.index 专注于运行前配置的加载,验证,日志配置等

->api.build_index ^HvdpamGv

%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQB2bQAWGjoghH0EDihmbgBtcDBQMBKIEm4IAAkAJQBWIQBrAEkhAGUABmwAMwAxVoAOfAaALQB9WoBFQlSSyFhECsDsKI5l

YJnSzG5nAGZauPjanZ4ATlr+k/6kncP+UphtnmPtADYeWoBGQ6T4l/b4pI8PiFSAUEjqbgvJL9bQfHb9D79PYnG61O6QSQIQjKaTcJ4nBL9f4vL6IgEnE7xdEQaxrcSodrU5hQUhsBoIADCbHwbFIFQAxB8EEKhRtIJpcNgGspWUIOMQuTy+RIWdZmHBcIFsmKIF1CPh8K1YOsJIIPDrmaz2QB1cGSPFMllshBGmAm9Bm8rU2U4jjhXJoD7Utga7

BqB6B9qMkEQGXCOBNYgB1B5AC61K65Eyie4HCEBuphHlWAquHaOtl8r9zGTxVm0Hg9J2IIAvkyEAhiNxahSeEl2pS0THGCx2Fw0Ek4dSR6xOAA5Thibg7E5wpK1eK3GOEZgAEXSUE73C6BDC1M0wnlAFFgplssmCrMiiDSvN6dAsFAxaVyhIOAArfsdnwUgGggF82xfOt61/dAAEEOEqVo4DgzAABU0OGOCtCvAA1eJqg4ABVGA+XRV9G1LUhWSo

CCQXTGMhDgYhcEPLtA0OD4TiSHiPj+Y5qSIDgwLQPMCxjHkpSPNAT3wMJCkgkpoLKdj0AAoCQLA6k3wqQ9MC/aktjQXYfgSI5TnOS5ri3esI1QZw+IOHYfm4kkXheXtgXrMFiAhNBanXBIVyJeIPh4C52jealMWxXFI2jetaXdBLSktZ1FV5AURWFJBz0laVKwVblMpVcgOHVTUsgMmM9QNV13QgT0u0dK0EFtXz7TQLzUqddl6vfJqK2EX1/W4I

MYxDSVwzGqNqTjJjEwfBj60zXBs1UsT8ELYsjPQMsAHEhrlYhq2TTb22k1Awvco4zh2acmFncdUFqWoUsgGcxwXDglzQHZjj2DzTkLPcD0u2SzxjC9jpvDIqrO/MtsY5jWMur5Pm4nhQviVd3ogISRNQc6JLYKTVIhhBtM/AVUFQbAiG0fRcCLeznFQN9SEAReVAFJjQAdeUACldABC3QAGdUATtNqEAZDlAAubQA4uUAXxVABO5QAZCMAIeVAAd

TQXAD7owA7fwAHQ4fX9ecAA+OmGaLYgS29Sg0OpiR+Vp+nCEZ5mOFZ9nGy5vmhbFyXZcV1WNZ1w2DZcU2ne0C2rZqzgoFaQgjHpHg8a6WOejW/U7KHes9KgOCiGUZ78YQLpqvrEcoHMAh8+xIv9BIYh1mpPRsldpgcwkGp6maNpOl6AYhjGSZpmDUhsSLAhbf0mmzedpmWecNmOZ5gWRfF6X5eV9Wtb10OjfD82dp1XAhCgNhqnCBP6RZIRKYkos

EEqLEcS/QNtHeBS7mU2CIAAIRgByABNAACkkXCzg0L0HiJgYgDQeDOFaAAGWtE0HUOkJBLBWHSHUu1dhvW0DsREJwPLxB4C8Wobxs73G4KuWEwV2j9HIT8fonwXjUh8n5VAUICTEMnB8OE4UwpUIxM/OKV1ATJH+Juc48QIqUOpElekeM0rsgysqdAgocqijylKea8o1G6TKhVLUZdSi1UNMaAa3IvQxhUW1O0DpbG9RdJYiog1vTDUkKdMawZQz

TXinNWUCYkz5GWmYrMCAO5E0RttS2u0aQ7COlWUaaBoJzEon9VsF1VIAzIXsHg41y6PTHMuD4wiGDFPnIuekfwfhMPKTufcwQ2LHlPHfes0Nry3nhvkKCL50kLBVNTcikAf6VHoMQDU+h9r0HAk+RSsw0mjNUhABCSEULoUwthTQeECLEVIt+AZ75NQ0TmbMBZSl+kqQqFYSoxBhi7mUIgzQTQNANAAPIciLBMUgPA0KHIbIMva1E2C0XmfRakTE

WItI4hjHi64gTA3vsJXMMSSZk1aXJBAn9CjfxWeMyZa0ZloIyegXOODtjOVqAQohJCyEUPeNSOyYUkjaCuDsDy5DMbwnCuwhxE5fjaBRJcLi/0eyomiqI1+qAbKlEUdwZRziDH22ytoqG+U9FFSVIYtUGoTE6nMf1Nx1jmpONau1Th3UBDOKNaaE1R0Ro1h8RNPxsAZp43msEpaGYIlROJjBI+EhcA7Fwkkk6KToniXrGES6gJ3gIj4lSYclTnrO

STUU0cVSfr0i+O5BhLCpzblBs08GbTzyXmILDO8ORUVRtKFC1Gql0ZcXhc5do/DBJFkJv60okl2TkzLTGcl9tZ6Rx2qgDegAF+KVoAWSVACyidrQWgACpUAL7x1BAAxWYAQejqCAFPTQA6/oLsAJLeIcTa4DgM7TQQh9TEFGFHLY1sKBT2lRAB2Ec70TtltO+di7V0bu3fuo9J7jZnovVe/AN670GtjvHROeIU5pwzvgLOVN9I10LhUYIpcdQVyr

vgNDdcG5NxjC3KID9SBRL/gAj4ICwEQKgTAuBCDkGoNHuPDgk87YaNHe+qds6F3LrXVu3dB7tbHtDqe892hL3XtvUfBRp9z6X1g2gG+7Te0PyfrFaVHx361BxZcmCKy1nIVQhhLCOF8KERImRIdpKaQgqoIZbYHxWUUg+O0LGSJk6EMZTGOyMIymvUYX2MhhxDhsJjBwzqV0Ei/EBkibiUZ3jtFqAgBy5SYov24KQ148Idgpfcv2ShEXEqrGSi1d

KxV1EvtVbldVujCrKrJUYvVVUDX6gsW6Kx5oKs2n5bwXrLiuvGp6zGH0XiI2FNKJNMMbqAkxk9YtUJPq1qRI2migNcTSwpA8cdbxqSXyAqTlk2xHZVLJ3eLwwcD1M3PVkTdp631fpXU+KQkkJwCkgyaQgGFqAKblpht0+8takb1gbb95t3EeL7CBCcTtKLRIbd7aTftmLIb1jgGwIsNa0CPlmHj2Y70SjtBfGEkoBOSg6c3C8eLK5+webeql9LIy

cs06RAVqEkV3gvFJxCiSoQoBcn0PXGQnZgFY+1Ijut1rNRQF/hbIsyhuBpIwEDqAFGu6NBaB0bofRBgjHGFMb8upSZCGTM4do2h2hxqOP0HGTwcaEI5eRWMZ7nVPl1IQTAYuJfSs2id+sWRiDy/lIr5Xh30jVoo//IBoDwGQOgbA+BSCUHG9TtgM3CqreEKRPsW3fZoRvHIS75QbvIwEPXKuF4AIiRJBeHb4RkA9Te+IOL7HIOA89Vl3BBzmJcDr

elxgeUPeaJ95WSc0FOoggXgoKWrF+nnyGYqPtNgwCRCtEkHAHgrRagJkINUK8Xzf7/i6CSoFEBMHyqc8ZZyVPLi/HiAwqyEq/PLmt2ZFzFx4jHEigFdNpQotIQVwrcBxH91wLh2V+hJUtMxo+J354QLg+J20GVyl5U0BFVWomsastE6sOkNVGsqsdVypWttQMwOtbUPR7VBsLVosrVGobVXE7VRt6xxt9srpfEpo5srpZoFsgkltccyddRfUB9Qc

fxA09pagw02CVd0FUBmxzlslYCW0OVZF7pk1bsxoacHsvpqluAEV2gAY1CYJi0fs590dShOlK01cHw+knxlkblcA7kHknkXk3lPlvlfl/kRkjsqJTk6JZhBDwc0ZOIod1wVwAp4du0kdIA+0zDsUSgFk8UHCnDHlnlXlGh3COAfk/kz9jkHMKVjJERs8Vw9gAp69ZVIA7I+wdNK84Q4R/o3gcZ/9QR+s8ZMsxENw2UylERXo68G8FEyslFBssDNF

sodQJQGsK0sDVRiDKpSCapyDGDKDmCepzV+s6C7EKDGoqCxtPE2CptIAZt/FuCPU+CQkBCVt1oQdYkSwg0XgpCI0ZDSUeBO8BAztuAG9ZEKQDDtDOA8QoD1DHtdDAwacisjhNwvswYB0sUAcuk4ZgcpdRDIAgim0Qj4UYd8tIjrj0VUcZJB0MdfcbCnwKcwAidSTSdyISSyTOiERPgWEyiG9ecAjBIBchcRc2I29JdI0kT6DZcQ9HBVhw8PdI8qp

o8qMaN496Mk8mNU8Xd09M9jJLcPM+xQpuJ4Rfh3McYeIS8y8ZUhV+EyicYKQOVXo4dDtm8fd29ETXih9g8FdBSDthS1cKMV819SAN8t8d898D8j8T809TdkwlT6j/pf8IpH9QCDjIBS84BuxkhNwvhwpPgUC5SvdLSuT/cFCnFu9e8QgRDqQg8R9QUx9fDJ98z8AZ84iF8kiJBJBhgeh2gYATgrx+hcjdJhkYxcEyF35hUXMtSPsyRyk7I9hLcbg

BxSjjgrhq8+UOoPjoCssupUDBiFVhjCCVUcDxj8CpjVzmtdU5jTEm9FjhsmCbFo1nEaDHFTzWotj3Fdi/AJsnVAwODZtmUeD6xFtziUxBDVorjESbj4lcBahQ1dtkkHzuTFC0Bv8wpE0PJfjU1YKnsc08loQkCDiygTDftVNYSrD4SccwLkZoVgi4VodwikgsTfycS4iUNn0KwbZOMIAoNsgYMk54Nsh0564kNuwqL8MKgxBsgmBsMmBK53BuKJB

65iBG5cDSgSM25yMVlXT19N9t9d8mh99D9CBj9T9WN/AONp4JBj4FML5WBlN2ZSBb54dH4pUxpdMFJwBloaQ4A4AjRUYhS5hMRMgMMYC7gGBCAEAKBf5NzjoRiuhgqQqNgIAM9qJRTDx9AjRMDtzsCxivKIqTEmhor/LJjAr4qZjjE2skqRAUroqehDyGobypL8qoqMhYrnRzyuo8rIrshUrKqGCjzliTzIBkqKr9Bqg9jJs6qCqMh3lXUXyUpwr

yqGrCqEN2LkNChRr6r1cJrGKr44M+rOqn0RL0BMNTFZr+qYqohSA84cz+9sSyq5rGr9Arxh9Drx98iVrxqMhCzH07NCowrmBsBWQDQAANSEdzeAikE4DzZyF/VKN67kfAQBN/RILUycGnZyfoU4f4LyowNgAwFyj6AgW+MaBfbazq7qvbCNC/CtMKmUEgJi5ama4m4gI0BAGM/yLyimgAWTYEtgutwE0GCGhPMNjDHi1RKlQGUl/m5BWVIGUAlAA

AoCkqReAvhqApbJbLdagABKHUC+ZQfMTURYEW3AcWzE3gHWp4RkBkbQRWs5BZbGxi5xQayuTgBGOtIQ1bC+YsMeB0vmmMLIVm9m7gTC4jIgGmkysymMdjdylTUytTQ40+ISa+EOrGuwf8BAZYZgVodjOARm5m9jNmyimayUSuRgNCZG/AVGnw00dIZYEpZuM3M+fQNCUlHtGIlHDO+sfAAXOCEunOvOjvBI8ARST3YIWsFsEAFsIAA==
```
%%