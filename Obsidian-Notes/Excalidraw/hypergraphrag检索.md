---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data

## Text Elements
开始 ^Y9XYx6eb

嵌入query并检索hyperedge ^W1mHnNUt

llm提取节点并检索entities ^jIzEftsF

- 附带超边相连的点
- 附带点附带的超边 ^LWZ5zdGk

Hypergraph-Guided Generation mechanism ^RfVI1FcY

使用传统rag检索chunk ^1l8irOaG

结束 ^VK1Da0LA

%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQB2bQAWGjoghH0EDihmbgBtcDBQMBKIEm4MAGsALVwABQApAEYAFVwANgAJAA02bH0ADmYAZQBRTABxVJLIWEQKwOwojmVg

6dLMbmcm9oBWbQBOI4P2g6SBniSeJoGB/lKYLZ54uKukprOAZk/dg8/43b3SAUEjqbi7AAMBwSTSu30+BwGSX+gMKkEkCEIymk3Ge7W0u3iTV23x4EM+TR4qJmEGsq3EqAhQIgzCgpDYlQQAGE2Pg2KQKgBiJoIEUi9aQTS4bCVZTsoQcYg8vkCiRs6zMOC4QLZCUQABmhHw+GGsDWEkEHj1rPZnIA6qDJLjmTaOQhTTBzehLeVmfLsRxwrk0E1m

Wwtdg1I8QxCmWiIHLhHAAJLEYOoPIAXWZ+vImVT3A4QmNzMIiqwFWqRj18sVgeY6eKNLmDM+aIAvi6EAhiNwmqSibDkczGCx2Fw0EcR0xWJwAHKcMTcJIvE6fAa/UvMAAi6SgPe4+oIYWZmmEitGwUy2XTBRmRTRpXKEgA8gBZABCdQmCAAagB9X96DqN9ukwABpSROh/ehqglUoWwqbV2SoR9O0fJsaWfdAAEUAEcYBw+gBkIfRdg4dokgAJWwf

BqhaIROnwfV4NmeAGVpUgUIgNCgQfe9IGwiAiyo/8mhTFplCgD9NAATS5KAXwhD9/3/T5WOgdikK4thUPvds0WzeMhDgYhcH3XsQ3iJJ2ghAYTl2Kl2j4eMiA4SpC2LfBmT5GUDzQI98DCQp0JKTCyks71MF/ABVSQ6lIZxOlGIQYA4fUYAAGXICYAEEuT1RCJEWZZ6T1TY0GcXZHO0DcXjsqkPhJUN42jVBtgGJoCXaU4qQhc4Lk6lyaRBYgwRD

ddtB4OqIWqnqkghZzqVKDEsRxNAfmZOkvTjGlXU5ZV+SFMVRSQU9pVlWslV5I61XIDhNW1LIoD1Q1jQ9L0WV5X1432hAHTGp00GG0o/o+jifV7P1hADIM+zDCMoz7WNmUTEzU1vIyaVzXB80iosS3jMtiArCRcAhGtz2IetG0fTT5g2jsu381Bdj2NmTnaFqaVHWcJ1QTb4158cFw4JcJt2K5OoOIkt13YILMPY8EFPKnLwyZ7bwwx82IZ9B90wF

6+MEyKIFkg5ulkzB2gQTQeP0vjMKfU3SAGZQ30yhB9QGIRmAOUZ9V/Zxt2UBAkm6ZQNKK9BkN0+2ZlCmYnZNiomhixw4Bi/8Jkg5wJnaKAcIoBVtw4R5jfpjjY70hPDOZEyzMVqybLsg5dnsngeHaHyyw8tACe81y+k5SLAuCkpE/CoTzct63bcKrS1SwI34wq9rISSKbYU+FcBgRH5JfiZk2u2eJ8TZ3qN9uabKWZUbxtQBbDmOJFrMpJooSSZl

VuxF7geWyA20GS7VBmyN0h1VToGFKdcU50ZRo0VBAio6oHpah1CvbGRoTRmght9KGv0wH2kdM6Ahtp3Q4IqJDSmfhJA03hvGcM0okYxhAZANGKY0z5CxqUHGeNPKEywuWNetImjULrHDfuXlmaRQ/iceIEIeAIiPkLGc45cR3BUWOeci4GRkgOP2CEOweAHDlnuFmY8VbxjPAqYg6trw5C4fXUy5kWZNGsrZeyhIdg7B7u5fhg8aS+RHkrIKljmz

LwqIAAH1ADTmjWSgLQIkSBia9TgUBhiECMLo1hBpUkADFcZGjagA6Ay9cpEGUPzCAYhshMD1KOKA5gCBlKxJU/QJBiBrGZHobIuAyxMALBaaKcUEpJRSmlDK2VcB5QKmGUgWIywEASYbKJsStpCCgGwKi4QMkMjZEIMJpQ3IIE6JiX+fYpq7BCvcKeps5z4CogAK2YMmUYXJhjEDnDwUYMBsAfgGDAN8wwqILz1hAEqQDypbE+JRWqXidinAONcA

YAJj5bFhNCH4Xd9F7HJAcRaINgTEImgMKaM05qUXxcUn+61UAbi2isHaLpCHchupAiA0CTp6ilPAq6SC7oajQc9V6WDwaULwdaZlAMH4EpZMy0VFpxXQxoXQkMCMmGwGRtk9hGNHHxl4QgAZqAB6liEUhFISrxENm4E7Su3A2wJ2kX2Nm/xpqQhMZovmdru4epFjovsLxoXfH7O6rCO4zGj2VqrGxdjNb5G1gJW1S9lkVyEnaJo+hOgcDnDFF6vF

433giqndOhBM7Z1zvnQuxcOCl3LjrRNMcdI1wno7OtQl8KEWIqRcilEaJ0QYkxFiFdo6cW4mhOuxlnFN1QG4lu9lnKfFjBowJvd/E+WHuY5WVzCg3IqGmjNWac0go4gbDBGwoW7C6v8K4SLDEAniEuh4aKkib0xacHYkIET4rvkS6dTRL0fH0YSIk5xkQPvRKcmlKL4wQrQNkv6fKoEnVgVYi6CDroqmQfdR66DhXvQoQqq0TKyFSqBrwIjbp5Xe

kVfGf0tCJHTrVZGDVLDUbyg4ZjHMeYDX4ykUTU1ZNdhiOpvR41v1uyRSxfZZEexpxaP5s5WTfNRbi0fp1W91xPimIVhu0JUaLxXljWgLMTjG6uPca3H4yKES+L7ka3jgT10Rt0/GE9FRAAyuoAU0U8L7NIDAQAbnoAB0OCAAGLQARL5BckOxQIxBQ5xIoEsv+6BPPeaYP5sLkXEDRdizmVJ6TMm4myfqPJBT8BFOZCe5pFSKjVP3AKWTDT3CVdae

0zp8ZulRD6aQQ1EA7mPOea895nzvm/P+YC4Fsz5kcEWYkpLXmfNpdCxlpgPZsvQfWZs7Z+W0B7IOZAI5Jy1qJa6lSLdYUiamxgB+Qgb5dj/goG+Oo259C/igPqSQEJKhNGcJIeIR6FgICWBC5ka9nDQohKSwkPVbLEiWqiyqlxwf3spAMWabjCRDW/YDPsk1XXyPJQtWH8ZqWJdsvSsqsHyMHVZcdGBZ0UM8qpgh6AWHBW6hzCK/DVHCOkLdCRkh

e05Wc6+tzmktGVUMYYYjZj06Ubxm1Zwoz3DID6sNaJwRJNhG4AGEJ8XNro72ubWJlmiJEQohloptRaBzgW+0WLBksIPiLRRx8LTCAp0WL07YgzN4433mTvWkpybW2mwecmIwAcci5Pjobv3weFhuw9l7H2fsA5BxDmHCOUdF4NtHQ7fNWFTbvi/D+ACQEQJgUgtBBAsEs+gurtHsABkZhK4gA3FxMjzNzpuEkNmNnV1Dz8k58ejfrnnYqKH8P+pI

9/aTaeyAIPMVTReLcWaksAMypPoo/YSPpqo7vRj+M99SOUjiFSI43wVw7HiCcENK0IOJchGTxlPOqcYYkBy06XLUO8up/y1BT02c9UOdPRcERdQFiMf0ZUwYhcqElVYYrVVVJd1U2oP4tU2MdVFdONcZuN+91dSYY4DgdcRN7NQZxNscbIeojgkhb9IBhZOBuBkUbcs0/U0B4gXgiR6pNMiYw1tMh9dsIBrF9MNYfdMCJ1TMO9Z024/gP5lFl0/F

JEBFDlHMQkTwXMZsIBjR9BAAF40ADXlQAIKDABOh0CxC1C2ejUEIHCDiwSwqE0N0MML8zCzMIaUsJy2yDyyyVcKgHyTaVK3BHK1KXKUqRq1qXq0aXwCawqDaWIA6TpxpHa16UDC6wuyuxuzuweyexezew+y+x+z1H5Em2m2WQkFsP0KMMcOyHMJcLWw2S2VYC21QB218WOXv3OROxj34mdgqE0F/ABBgFIDwhimUBMGwGYC5FwEkC5H/G6HiEyhn

3QHBQZViLPUqn7HxDqmMURH6naGRWKU3w+G0HaHXB7wuAvTbgpBlSP1xEhAhwuDPmhSJB2O/haJDGfQJCJBJE+DJHOOKRg0ZEpxZTf0Q1py/wZxsSZxQWwyFXZzwxALFTAIEElUgP+Mo2Fx+lFxhjowQIlxpEYSYxQNlxpHlw4z1S41VxIMEn4xjlyiIKxL12zwNxHyN0igGEohOHxVkNKDoP5j/TAwYFUVtxU2fWRDcWDVd3d0jSsTVm9wcSM3z

wQmz0Dzn06IkEyjtGqF2CMGIAmA8jzVjwTSEigAaFwBwmGC5C5F2GzWwFkiSDwghGqAoC5EyhfEHTrWHXr11KTjjwkHbSIhIjIgomolonokYmYlryrkbQb0TjO31ML0/G/D/EAmAlAggighgjgiHQVPdP0nHRpDbynRnQ8TkSOGqj7wUICSUMHxUIQFOw6JThVLVI1K1I8nKwVNc2By2COFqkuApGv0+ORxoIgBPjcTiFfWxQ/TxQU0Px/TcXxGu

EREOPsh2EMV7yJ2eNZmyV+Lg2ZSZw/2QxpG5UukZ1/31hZwALnwNGAM+lgJf3+iRJvJROvPROVXo25lKFxOYRlzQKTAwIzBbxVx40UIpI1yQg/BpPTDV1INcURX7GhQvSYNxHeCYOUwZEcn6hA3JDFJ01UP3KlOEJlN/JM3b39UkI8UOOKTclswgr22UICglPCSKPQGcFQEABE0wAMj1ABQ2MAE74wAD7dAA9+MABC3AwoLJitigwti/iriqw9Qk

SjinigSoSlwFi1isS1iiSzilJNwnZArTw7wwpPwtQw2CIiQYIurTRBrJpQIyIlrJYyAeIzrbrbo3o/owY4Y0Y8YyY6Y2YibfwQoxLCAGSriviwS4SpSlStSvUXAdbWorS7bUgfZJog7M5EMC5GsndCQKiQOZMJoXJK0uYxUyFFYxaWFYDF4K/E4W4OHadElWMZ9aqC+AnKkTHB+d4RIY4HvJ3H4P4KDGkYnK4p/YBf4ncpDGygQ7/I8wE5nAVM83

DbBWEgjNE8A3nO8gXMhB86jJ8+A9MV8yAd86XVA1jb8hXAikk7AskwCsoSk2kbcMC3AyCmRH4SiPYC4bavkuTBgl6rk5C7gfRZ9SgikTCvgz3GNEQ463MydMzEi5FSkcildMstdSs2i5zeivyzoKLOUXAOASQPOIQEgHsVAH8RI8yccVATIbASQawQgZgfQKShiiAVGzLdGzG7G3G4gfGrIJgImzgEmgHcmjgSm6mzw9w7SvVYrHwtqXkirSy4or

2c8+pMIoy/WcMPUOyxIs68snauZHy/AawiQempgRmrGiYHGkmVmgmjmhpLm0m3m/miKqKzbXZOK/g/bNc47S5dotK9AGoHCSQO0fQXKPCXoeIWSKAXKF8bcCYfQOoBAUCls0FBYsqdsqyLqWEaaTuLuNmRq1qNFNg7QTFF4a4doa/bY7q0oS44lCHPHeqylJ4w7bgUnaDRY7gLcshIa4EuBQ8sE48ya//HDaE2aq89axaohLHYGZEmAweyAMXF8x

jD8/auXdAo64zE6vhOGvjYCsmUYG6tAOkvWBkqMlkMgkMbxG4KEeyeCmMGVT6lg2lNuc4Ng6zbg+WN3LC/gwQr3PCrWPUgtYdNsr09AJofAEiUgF8KZSMltGM5BI0k0s0i0mKK0m0u0h0p0l0hNN0iMsdZvQi/M8zCc74QaUsuzc6oJF+1KsfCQABoBkBqYWO49CJROqqzeFHLxdxC4LmF4Sq0+EcxyfOsq4u4pMu39cHKQwDY+z4zqGupK9c/qp

uwaru3ckag8tDcE083uoAmEge+E2VCAkesje88ezRqerEl63a/Er89GRev80kgC9Wi69emOKPC1YTLEqig+szT4vYfRQxc+6ddhn1AU1seRPeb4CEDkwSHg5+wGyU6NaU4ksG8Q4i2yXByEEkAhlx4hyJ5GioQAf3lAAKV0AAJ5QAfb9yBlAgswsyaFRmyaN4l1DcnCnimymNA/FBaYreBCtRa9K0BilJaWlqtnoQizL5apb0AoiYjlbUkEj+lTY

vafa/aA62Ag6Q6w6I6o6Y6GFNaFltaan8minogGmKnbaaj7buBGjXI+lEqaVXbSGC9493ZPZvZfZ/ZA5g5Q5w5I4aHtJuJ6HnAFpoQvFIQu4NxJZTgOG3Fwd+w94ocjgkUS7CUdHXjccwc95ERkQYWpA1z3gYRkRLg24/0iQBzNzZGJr5GQSO7EEu6ITWdzy3p+7QCFqETtHpUx65quc6WwUMTxdjGpdTGDrzHYmeErHbqgL8DaRwIt7CGbGwhXF

aqe8URQnXrPVgYuCeZ+TmC7dlxOoXhtjhxH7w0qygaYndU4miLm5En8UoR71UnySNCaLUAPd4w4A2Ayx8K7wZgXWZhWESgIRHwlcwA3WShO5tAU6WSUd3goWbgwNPXvW+I/WwAA2g3tj+p19w3jZvmupgNPjqCL0RSDgo3HwY34W6pEWEQkQUQU2MX03sWs28XvWczDlQgoAeR9A2kZAew6hHXdRV6BdtRpJiYyxI5t66Z0h7FutmAhl4pEpkpUp

0osocp8p4IclsBfYm6Ehr14hvhz8fgoRHI+IEwMbcQK7V95pq66ZDRMBW323EsY2upyQuZCQFF5E12PgCVPXc706F1FENxbIu5712ga2HV4wshiArtFQ+3rVB3pTutLtrtbt7tHtntXt3tPtvtfsd2itF30xEcz5nJFpJZAWbJ+wd3lA920BqrYxyQ990dobUPCAz3iA22nXfd7xnBwcb3iR5FngQmKR9EzhjZwdzhrhhSVwf2e970kg/32iETu3

cpG0MRcBrHmRAPpOUJZPTZ68FP8AzwKAWYdsrnlT/7i1S0c5DaC4i4S4y48q1PV4tgfm3jiQFFtjqobIByhyYV4VEUr5Bpb4pydGERMX1xfgWSxGqPVza7OnEgBPFF12S3UWCWbzW7OV26lHyWVGoS1GaW4TWW/o+dR69HmXUT8ENrMStqZ69qCTSgiTDX+XTr5O17hXcAvKaMqZxcXHJWZFjFZo77GC/HuSlXOSVWvq0AnqQnQ2Aa9WomhD7E+X

IA8yIbTXsOZY5WKLBXrWEbbW6LSgHWnXP7XWdYPWwAvX7wfWr3oQ6on3z8dgGrePc37xjviqzvpCKVJyBJCQt44QouUW3bMG82dZfP02Nx5ygvM7nvwuU74RkWURxOwAW98B63G3m2LJ6OO3xWmVu3gPHAVgwOC0h3noR2x2RlJ3xkZ2pk53UO+gl3KoWPZpgNccg2SR9FCPiPWZqPaPEfL2dZr3oU2P73OOn3ePkgZZafqfC2GSoemYAPFQ0fQO

B2seIPbl7knkXk3kPkvkfk/kAUgV520PyfGRtBYwt9jF3gZyVx+pqpevyvGeAFlcaPz2GPZSBJmPdfOe72OPH3uOv5dv+fr5MUzhcXKIBhIf97WQpOZOQgauaRFOQ+5OPm451PNPtPHbdO6z0Ai94zS8kyK9Uzq90yXNMyIyvnrhEgyPFoP4fgYcgfH14cP5aohomG0cd8mrSMARA2rgz5KQF0bJHiQvJHDjkhXVbgWSHiYvG6Kc4u5HhqSWkuJq

KXpq+61rNGsvlqh7yE8vHzShDHiukC8TNUeX2NKvlcBXO2nxLrcA5wxWWvD7p1qDC725Nxuu67vVlW5MBvPzLh5EERilKan7xSkbSg37gbnWW8M3CQnNz0QWtTm8hZHgPmCSI1sKG3C9ttxKAxs9uB3L7jdx+6JAvE64Yvo5B3iltdu13HbgJH+C2csBt7RRB92Ng98pY7cW4IXRuAAgCBiAnWE3xTqt9FEWxTvgJCoF99aBg/T7iUEzC1s9ssPA

wPDxt5I8WuUQUgD2xA4Y9pe4fWXhUCg6pFYOGRBDtkWQ6a8ye6YB3rGHhD6CDBFIFvgzzgD6UC0p7cQWzwEgc9b27HB9lx2fb7dPehgwwWJ0O5i9w+EvXtnINQA2pse2QByj0V2B9EBiQxZgCMTGITEpiMxLQeh2XaxhwWUXMkKwzbgAhLeu7UwZ02Z6WDGOMwXQQBjYLEgjgCidcAijN4vtqCfwOyAulbjtw9gPAAPij2kFKddIKnZbhH2U6h9o

+VAWPrpHj77JE+hab0gRF9JdoAyvaYMgOgs558rO8Oa4LZ0N79gb45fSACfE7IbgsU76XFF+m84Pxr8mLP9EsKGhUp0WGA44OcIuE8cG65OP4qPyJbj9EuP+Kfil0AKYJ1GtLArkv2y66MVqFGfRqy3X70IcSXLbfvPUOpTcDQB/SAXgU1wvgz+VrVrsuAXQfxFyxSLkghSQrX0S+ncdmIcVG4wDX6uFSbnv1bzg1gBUIM1jLF5JLdD+1FVbnaxp

CbcQafrJAVd0O7RsfuJ3O9vjmPYCQUBAgjkUQK5GV0j2hOe3iuAJCXCpRbg1AYQPvD7DgMhwwHpbzADfMzhUoi4TKIEFCCNCIgptmoAR4XtluQfaQZLx8F+DFBEgXrArwGzK9hsavMbLEO15gtKIeOL3o5B95uITBZgzBCzyNF297wNgrni7wcF8924+vT0WfGN6/t3B/7TwUB28H9tfB4HPCoEKcqhDXKkQjyjENJ5xDYMuvQxOuCSENRTgkOZ9

hkJ9E8JredHf0RmB1j5CjghQ34Hig/ZlC+elQhdHvEXRtwWSjkRoQQmD6dCo+tIjAIqBaEUA2hZMGYQoLj6RQdO7tMhvrCgamlzSlpa0raXtKOlnS0wz5rMN/QkpqBzwPeF8WC40ghyEonvJsJxSfonupdH9BKOp5nBkQnVPAT1TXKU89BFHHfD8WH63Dfhr+W6ECQS705SW6GQCd3UhKvCqx7wjLp8PpZLUdGUBQXCvwnpstnyRjErtyzBG8sSR

/5ZbsTDq7jZGuNiZrgiIv6dRbircAcuiMG68kr6arYGAoi7iUQUW+Itbj/0lBEjDMoNUoEAISYUiyKJfS1kQxtYMi4BW3XIb6125sjZRTAgSIoju4rgC6RdTgfeH5FQ9BR94BSadyUm8NVJeQj8eRwvT75oajAqSQJAfEp0nx3wX4K+KY6GSF0xkyjm0Wbw6iYerIOHgaJyEjiTRMg9HkmItGpjTYjlYIc5TCERD3K0QhruYO0FbBKeiQ4tskKxS

Q5vRWQk9tWNZ6SSwA17Aoe8WKEftQmFQ8/NUO7F1C+xsYiTqOITGyCApKY4dlM2qDe1fa/tQOsHVDrh1I60dJ0RhwLEuDDBQ4VKUz3Sl+jbedY+3ixyd52Ceebvdsabj6nwgtRovOMaAkHGtCuhI4joWtOHE54Y+AHDTn0LnEJ8Fx1zCQCJDEgSQpIMkeSIpGUiqR1I7zKcbuJpBrwd4JKanocWxTLkH+FfdqLCAPEI586ewMUXeJ0YkhiqGxENn

wwkY0oNyv45uuAjH5t0QJk/cCdP1UZvD0u81OCVowQmMtcuGjAEey2nqb9Z6ZXNhAvQhF4SRxBEzXMMHhHnVERVkaaNsRuDXBvGD9R/kpmvrQpGozwEsjq14JjccK0TD+iSL4kmtW49QjOsJJsbpMhZ4k5kd9z5EySBRSs+8GDPWJHBIZ+kyNhVI0muQ9RYgmsaNMkGo9ExmPBQUFIqAIAag9QZoG0C6C9B+gQwMYJMG6lxTA25IRyZcG8R4p/m5

YojpkKGnmCMptYq9gWM2I+8Q2HBf4O2POA7w121+R3CuGoL9j4xZopMabOaGR8w+pQTaROPWk7Sehe02cccyOmMloyenCAJlBwgNAPwuwQgC+H0Avh4gcAeIIQCaBURcouSJILlCgCiIHp8xAHKVFazPTlwRVP5ocXqhv8XqQ5e9AkHNYrgjBMrLziNGnLzDFRf6ZUdDIfzfTAEcMwluBOJaPDxqqMl4VS0vIfCJUDLUjEhNWr/DsZgIxAsCOQKg

jCSFM3CVCJcY0ykIMUMVjvVbAeC7qfYc4GcBCbX40RKrceZiIYk+NngO8EUh/3Cbf9YBnEkWcSIDGVzmwrZOhn/QgC/hwITQbcOTEyjUkMGqso1tgxIo9Rr8MqGkdCIrLQD2Jw+SeIuPwWELiFEIUhXlV/pjyrcF45mTvEogHwVwILHvqAKXlDhmoFxH9L53OGnB+OVwegScNC68AfxNw+GQBLZTHzkZTws+VNXRnQTMZLLbGQv0QlMsCZj8omRh

JJmlczGu/UQtjC/lWsf5ZMX8PTIlYX9Xpu+eRB9SgXAw6J/XLEVcBvYlskFX/F+vq1FmOLeJZI/iXOhoWIoZZ8NJhWJNmDqFAAy36AB9cxpp+VslGlNJC0zJA6USsZWAyiHSGZVI+mplR/uZXCKVKRmo80oCrUmYVAa5dchuU3JbltyO5XcnuX3IHlrMCimzWmvkrWSHM6iDteKuAOaKqLLmE8cAFjFpBwA4ApoFxBbOgAYhMgNhWuvcAYAWEKAH

4Map3SJb6gzl5y9YFUhEDoJkw+4fQKaBbqIzgJzS65TjzuVHLQSZLZ4QYtS4vKuIbyjILkkvmwTLli7f5QELuUPLcZt8vZWCpuWQrkJli0Fa8ohUZAqI1ijfn8vhUZAXwIIljIUCuXgqoAtywFe018IkdYVKK4lXctyS5YilICQldiv0AJYFaGhGWsiqJUkr7lUgkOjnNuqMqAV+gUYGOL5WPTdpWKwVeOJaAKkggmnS5eEPZDGhug4ITqLnQag/

AE5FwE4HsoVW8h8AskcEF3CmhGCNi8ChcnsqMBsADAFs2ggQH2StF1wciGsgKtRX6B0VJE+jGhMVCXK5QJAIWoxL2W+riApoBAEHOWgJg5kxAN8GwBJjCrcAmgYIBk3JkkAEM4UD8LyBdjKApQAACgL7UBeAXogtUfB167AAAlHqC2TKBiw2oBYFmtwC5qF0+a9gU2sbUlry1zquFc9ChWchcVFtB6Pyv1RbJywcyc0eL3jWJqy50yuIkQCDknMa

QU2bZbFSnVvl1kRySddWXaIQA7ADyYeSMCmxwBo1saqbAmsiUErpQDSRgC0CtX4AbVAeMIMECWBqIukvsDZMyuzxpNRJ63YQayFyiPrL116/xCFHAChQLywQRsAZHbBAA===
```
%%