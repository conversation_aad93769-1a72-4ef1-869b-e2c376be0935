---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data

## Text Elements
user message ^xc4dWznC

output

1. message
2. system info ^gaQQh6GL

core engine ^ZNOJirQg

1. 主动询问能力
2. 感知能力
3. 无 ^LSliNRg0

ToolManager ^JUcpvGja

## Embedded Files
ed2a3dfef2abb8010b8d039bb68f94ce89066a7b: [[tool_manager]]

%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQB2bQAWGjoghH0EDihmbgBtcDBQMBKIEm4IAHUAVQA1AFZNHmIADgBrABEATQB9aphMWoA5StxCTFSSyFhECsDsKI5lYMnS

ibRnHiSABm0ATj2AZhaANnq9k5P4i5aW/lKYbk2eFu1zgEZD7e2LpMP6+pJeL3SAUEjqbj1eKJd5JJIXeInI7Q7YnEFSBCEZTSbg7RItHYnF5JF7veLvHjAwqQazLcSobbo5hQUhsNoIADCbHwbFIFQAxO8EEKhatIJpcNg2spWUIOMQuTy+RIWdZmHBcIFsmKIAAzQj4fAAZVgKwkgg8OuZrPZlXBkm4fGpEGtbIQJpgZvQFvK6Nl2I44VyaEZz

rYGuwakeaHe33RMuEcAAksRg6g8gBddG68iZFPcDhCQ3owjyrAVXDbHWy+WB5hp4pTaDwemHakAXyZCAQxG4tySFKSRPq6MYLHYXBjn1HTFYnCGnDE3BOh3iPEOl3JJeYHXSUB73F1BDC6M0wnlAFFgplsmmClMitTSuUJAArQ5dPYdADSAHlqvQPBJrgABilQABIAAqQUIzDvmKpQzPSECaqyVBPp2T6Nk2L7oBySYAFLVLgJwABoSsQIFDOB9R

GNgMAADKVF0HIIdMLYVqQaEQBh1JZs6QhwMQuD7r2MbxPUpI8O8K4rnczpEBwbQFkW+DojyUoHmgR74GEhSYSU2FlGJ6A4EkxCVEYHCseiSEVPumBQDq6yoM4OyvLCJwtN81w7AcI7OtGrnvC0cRyYcRwnNsLzxIcIXomCxAQjGLzaJ8ewtHsPBQi0FIBU2kiYtiTloNl6K0l6oZNq67KKryAoisKSCnpK0o1gq3L1Sq5AcOqmpZE52YGsaprIT6

vZMiybp2klDqlZNNruqNFTjdWwgBkG3DvOi4aSlGW1xs6CaCSmd78U2Oa4HmJmFsWzqlsQ5YSLg7xrXKxB1mmt1qc6YRaag9QtPUhwDts0IzuOnBbVSTZjnOHALhwS5TtJlIAgO267sEomHseCCnuexBXhkA1neignCTj4mSfE8IAp81zqaWyloN96lsJpJk6SezoOSV6CwUwqCZPW0TNc65AUAAKlg/MQILpDC0GYs6rqnBQEahBGPSPBVaUavZ

CBV0GkF+WIbLACCRDKJO6DBLqg3OmOUDmAQVtYrb0DhjqejZGMgakPmrOqTtpBYqWBAy45FQK0rovKOLTa4EIUBsAASuEWv0iyQj4wppYIOBRU4jG2jZfp9xGbhEDvp+P7/oBwFgVBMFwYcOp2c9XFsFQ6IuW5QJl1sBKnHs8Sot8ZuQEFziwkkCQyQcOwgycMlEgl9qOts7xl18GWUl8PArvFzqFViJcMskMm5ZS2XRWSJJJOVSyVQtbp1cq6CC

k1ootVKx3yg/vZHqfUtSOwusND0XoXTcl9L9KatpN7zXgYtKBY1YETQlutSQn0to7QjPtGMh0mzHWTKmfI519a5gQEHVAbN7plhcihHgb1aybWDndaq3YTIhS2NsOE98Ibw2XPJWGs4JyI2RqgCKQ4djZW2vdHce5/rczzk2M871iY3hyBQ8mQkRL/TJDTOmSJgZOibIpFmdCQ4KQ5uyLmeN0RwDYKWHRaB7xTA8VMPW3inyUJKF4kousd7rh+KF

WK0Vj6iIfLsAcXkKRrnqPfWmWw/F8XUqEKAXJ9D6DUKJSCLjtQcJ+tVKIpAoAACEHqlmUCpThpQsjECqfKGp3BsKQHSNo2hVQ6iNGaO0bofQBjDFGOMBCeoOawW4LsQG7w9jnG+O8WElJcoKIfBAZQuA4CQhBJAfUmAewFNcfkJ8YBnC7G+KcFchwV4AjXGubYhxdlgF2LrPY0V+FLxeFFPYSQ0lTAMqUZkmooAW27hQQquAbo2KbI0sFaFIUmRQ

uCnUQQzwUBUXjCuhQq5IvwkREi5EoVURonRRizEbK8w4l3bifcnjZVeOPN5PAWU/G2C0GGDwngyTeL8tcSR0aIlkRvWaW1fnJHJDcvYRioT1C8uiM+xVpnPzpMqlB79OqfwgN/RqOoJT/3akA7qaoNRgNVpA5a5oMFWgQQgGayVeBv3ZGgla1q/TYNwTGfBe1YAHR8Rs2UZCybOkutdOpJTnyMIrO3d171PXWPqQIbhW0hzeTHoDPYQiJzcHHlm+

ci56R+VRJldcmNlEON0mo0oGjLzXlJrogS+iqaoCMZSOmLR/jvCnhASx4b2ac1xpWpxhS3HpifIEl5zzth+N2RO2e4qgSfAijKiS8rTnTofBmdJClMnZNyTIQ5I6+3wJBc0xwSw2lPgwHW7I3SagNCaK0TovR+iDBGGMCYuyJnYCmRsC5EUorwneUcL47KjhPyvZs7Z4kEjspZcOAcSzQpfv2Ye45xSOzokaWe1pxTJogvhT3RFx7YXykIxCkISL

UI91RfgdFmLK3YsMvdJFmyACKbHJAnAAOIMQ7tS9AfNnJPD8mXAkjyj5H3OHKtEgUniHG0EiWM8jcqhW2DlEVDrYS7DmRlWmHLrg8DHgq4u/NYz+oqvSf1NVOSaoaj/RO1bWoAI6kqYBJr+raiGoaF1VrLROrtUgx16rnWWu9G6rBfgcHsJbd6yMvqiH+tIadBtF1qG0PoThKNz0UixrYfWEjQLk2lUeTckkEVu1w2zWgHYeaEYFq2kDf4/DgaZs

UVjBAzbVEE00Te0dmY9GU0MRJNtFwKSCPzkpQrkANL2MHTzJsQmJDCFkCnAAOhwDbO847MDFhtuIqBmAwGZBkVApY1bVkoFHOWK24Drc2xwbbItdsJ329oQ7x39z6DOxwC72Z1aa21lvf7htjb4FNrZS21tPb23AaUZ2rt8DuxtvZb26JfZRALoHaFiaIC8nDhwSOssKi3fu1t97z29scAO0dk733ztsB1MnVOGdWBA7QDnKtM2C5F3PmZsu9QmO

PhwkigAWkMX8BFCCkDY7U2yAnoDE7pSlN4uVJJGdOMWjlsmmwz3qDvVcXasocsPhSTTc0W1gyvgcVEq8iSxl+SZvnjpu2WbVaUxaRqv6NV/s6fVbVCZe+gCA01A1zU+bCzA/zIXAuiuQR7t0vnwvR6bP6aLBWvVhgIQly3SXA0pfcf4vU6WccRsgA9J66BcD1FYR9GLmWiuGNXt8I+Aq6u4ieU7cR+akb0iXdJR+5wy3YwYwt6thMtH1sL4NgxPC

Ru0wuCDQEbWLHM2mz2uxo+ueK+jhIPQgRUBZH8I5yAktrsVH3wgQ/SwC6qwB1nYHIb1ZG1yeDnZvMocewqLDnUCP3DI89lTm2XR3Vn9iYAyxhVKHx38CJ133QEv2v2PyZxTnTkznZ1QE5yZkDF5yVVLnLhKEBVxQqAYiNCICGDTmUCrHl1mBVCV2dBcnhFeSuFiiBBeA3CWXRCCi+AUzHnmXmRuWhGPnNxTR3gOAyiOBCgJERDlSd1wNQAkhVVfh

jyDx1Saj1Wc0NTs2NV6lDy8xDQtU9HQRTyBVtXtQt3MRMNQUj1WljQ2gz1iyzx9SCnM3jHz3IWnxDRL3XwryYVwAqVr3jQbyTUMWkmhAHCPhX3h271tneTq0kR1mBgzWA2H06y3x61rRJlvFS1KApln2hhpiSRphZWiW5ymzw1sQHW0kcQ/zgIgG20AG45QACqVAAj6MADvUwAX4DABspTe1QEAHxDQAU/cuiNsFNUBAAD00u2lmJwkAaJaI6O6O

p3ewGKGI4BGPGJBw1gfxK3WJfxNnf0W0/xRwkB/whhdn/2h1R2AOdAxzAOx3X2gIjnwHP2mPeyaLaOWIOyWPmNWOQJZzQOzlIFziwMLlMy2gFyFyIIkAQAAEdCpAwBV+MaD0BCB9AVZldUASQTh9gvJh4M0u1Vxu0Z44od5oRaZmtt40ZERhDSpzhFMWVzh5kj4wYMpu1FUL4yQLkOUoR/gWCgQIMk4X4rMAsg9VQdDPM4dxQNDA8tDBMQ8xTw8R

pDCKhJBJQNBAgbVFozC8EY8k8o84FU8PUYs1koDs9nDiFSh2pAjIDgiTIgZHcu9IZbYpM4iGs0Al5l5MpIjIBcjm1W1EQx4KRpUjTy8lER8K0x9IBkt3D0wi8a0iY+tg1V8yiE0y8N9KjUBut9DghukeweBcBDhiBdQEBdRczNBNBvJYwyziBHk9hSyvJdRfkxBMpbdcB4hNArR3B6RAkgywB3ht0stHpfDKU0sroaFS98MoBf10BEAWkL10dWRo

NkzwSWMKgiJsA4B6BuNXxcAETkIhM0T4R55VwwZuSBwJJYpODuUNwy4KQb4R4bl+FO8mxEotN1w3hzgmzaZx5x44pZCL4ypnQ3cQwhTpTtUfcT8IB/cXNhTZSzVvMFToEbCY9NT49LDE9rCIt9Sot40gy8cTS/VXDEwC9ozswvDyj+zK8UIOgAj68rSXRitpFAQ4QBUbl29Spc17T4Z4ixVpV4hR4WL2ty15tt9YzJ8siPCmxvThsaYkRV0jh/Ve

0yLShZs0iai5YpY2BuQABZawMWPkP0K7KY9AdSrSnShOPSp/bIQHHWf1A2KAHYt/NAbtPmAA7/Is8UhgJgU4t2c4lUNHK40ArHCA3He4wnR4wyiAYy/AbSwnMyn41Atnf4wEybYE53PAwXAg8Ac6FCOAOAE0AxS9RCQqTIb/Z3e4BgQgBACgCpSU96FQ3Ueqhq1YCAH9LiAaJML7E0T3EC1Q33UoFqsBdqjIaqg1KUtzbQ0BMPMq/qtqr7ECAwhC

jCyAaa29Dq0woLCw5qkQAa1aqwxUvzPUparamajINOA0+wvWTa1qlajIX8PCxLKao666/QECZ/MHCHQoS67ajIF6yyzY3gC65aqAQa/Qa7Fyo4typqwG4GvK8pcjYjRSz646/QC8MjcFeGqvFFB6q6oGr7cjKWBXZgZU0gS4oFbAVkQ0UiXEU4VXWZIVSKdTPkgQMm7kfALoXEVEbQQ4W+Icc4dcLmrYMqowDS/QAq+HAgXOUE/hGSWEIXRGp606

uNGLCAdqJqmUEgKyx/UoNW4gE0BABcs2ANEgTStgR6FG3ATQYIMMqtQ21zLqVAIyCpbkJFUgZQCUAAClCOoF4DJC9s9svnqAAEodQM5lAixNQ5hXbcAPavhfaY7eA46Zkg7ZbAbOq3RbqXZOAvpIDi8RyM4yww4L00AjIshzbLbuBMCriiAFyK6mxCdiqOcATt8VtFJEqEBZa7BXwEAFhmAjRCc4BjbTbCcLaVLShJQXZGBIrRb2JES6LggFhs10

dYJU4QaBMgjUy5sqih0d1mQLZ56J6NL8Bw19JwADI9QDRwg2l2wQB2wgA===
```
%%