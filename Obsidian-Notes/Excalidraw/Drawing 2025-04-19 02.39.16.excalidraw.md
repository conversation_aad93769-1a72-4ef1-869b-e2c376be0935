---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data

## Text Elements
route ^7MFdK8oT

service ^xReSzCnr

utils ^TMyITu2a

%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQB2bR4aOiCEfQQOKGZuAG1wMFAwYogSbghmAFFSOABmAEcASQAGABVayVJ4gCViDgA1AH18ACskfhLYRHLA7CiOZWCU4shM

bmcAVh44gDZmgA4djeaeWoBGfbOATjOJyBh1i8SNrdrD84AWHg3DjbuICgkdTcK48M7aM5vK5XZrNeK1eIvPgFSCSBCEZTSbhnY7aUHNH7xHgfZpnHa1D7/ayLcSoZr/ZhQUhsADWCAAwmx8GxSOUAMRnBCCwXLEqaXDYFnKZlCDjETnc3kSJnWZhwXCBLKiyAAM0I+HwAGVYEsJIIPNqKkzWQgAOpAyTcZErK3MtnGmCm9Dmsr/GWYjjhHJoW4o

iBsdXYNQPEOw/7S4RwRrEYOoXIAXX+OvIGWT3A4QgN/0Icqw5VwzUtMrlgeYqaKLqmtNqKIAvgyEAhiNjajsrucrvseFd/owWOwuGhas1KWGx6xOAA5ThibHHYn7aEfeLF5gAETSUC73B1BDC/00wjllWCGSyqYLRbDQjgxFwR+7IfiH2OpI2Vw2C56TDIgOBZfNC3wf5uUlY80FPfAwgKdsCgbSAygkeIAFkADFiAAaX2NhWktJtyiPTAoEtNY0

G3HZtGaACeHhf9SVqWorh2f4Y1QZxIQ2bQPhuDZEXYxFLg+fZ/kBYhgTQDZaiSf8rniUFJJnHZ9kRf40QxLE0G+KkFi9YCXUZN0OS5Hl+WFIVxjDcVJQTWV5SspV0BVDg1Q1TIqKzfUjRNWkKi5X0w3Mm17Vkx0DIZa13SC8ofW7P1hADINsX+CMJWjbE4zDZykxTPJMzDbNcFzT9UEfKCwxLYgywkXBklSlzawfSCOzg1AzmuI4vmaWpRyYBdJ1

QKS5xGidlw4VcDOHHgdm2Ec6v3Q9uoQ88HKvYgb3SXyOqfF0XzfD9sW/X8yUGkShpAktwLQGroLYWCqs2hB/goqiJBlI8q0oVosG+9Bfvsl0dU4KBDUIIxaR4UySghrIcIq/UeL+MMvoAQSIZQxogYIdT8ybSCgcwCBxjF8agCNLT0LJcBLJg80ezqwx5DESwIQHKJmYQ/qpIQae6cIYdpJkhA+u7AwACXRTFgfBb5kImNDSiqqQACEAA0okuNg9

wATUuABVLD8AAcVOfB2QAeVI+BgtmeYaWo9YhKuPErg+C4YQpXr4gml0eL4o5BIRHYdlUs54ijvsMZdGS5N4Djw4pST4YOfsdPl/TUEHCElu3fZt2YwPQxdakTLiiyFWsiQBTskULwlKVq1cxVyPILz1U1YnwYCj0vRCi0a8ih0nTHhLPWC5KqzSyR2sy9nI1y2MEcgQrk1TDMsxzBAWeqtmXXqxr0FwWp57ajK0DQyAyKnNsuqq/YEVqZjGNul1

5wnHsuJJ0aM05qoEkpuISxIv4lEIGtYIZ14Jnili6S8Lk9p3myCVf4J13zdRjj+WEZIvicWdCUUCD0j5HRIS9Nkb0EEq1QnVDWMAAAKGxWgAC1Fz0FULbC2TCZbYGYFjHCOwmHdByJ9R2MwEBzCrmDEoNFeKAX2BCREsJvw8H2ApD4kD7iPGUXsEkjE+ykmaAY6SE80CgnBJCTcMI4QIiRDnPSitcT4kJMSUk5JZyV2MrSDerobR13chARutlLSO

TbjtIJXdVS918paPUBoh6z1CilcK8U7QWN4FPBAySkqpKvulOsy8XTZSjLAPK/it7FTQLvMq+9D5PTqqWBREBcAfCvjWG+qA77QEkY/FYKEzKdiqucN4OINEJxKD/Tg3AI7DXHEuFczYo7blhASXcB5YEbQQReHaqCDoYOfK+bBVVcGXXiL1KE0F7oQQoZAGC1CTy0OKEMwoDDygsmcEYTAtQEAsiEIMZgTCAAy7IhD9HqNrbotsmFYwdtMH60iX

ZLH+AozYFwVHHFjvsfYBICT/2DmuZoCRITDkuMSRaOwcTmOiiCMEEIoR2PhIiLYTiFbErxPDdxJIyQUiMq7NA/iIpsmiQ3WyzcHKt2cnKMVHlu7eT7gkweiUzQFJyVFZOxCBAZLyWq0eYZ/SL26RXEoZS149Xyi6apO9SrgwaVVJpJ8Wnlg2J0vo3TekP1QC2QZz8ey4tOGcQyADf60TOP4mZHAgG0jOB8D4RwNjblUps9aNDEKILFPs28hzal2p

KFguBPULr4PiINH8OiCa3NZvcgmVCdkZrocUNWGF0CYBFoaIw7IOC8gkYijyQM3a0U3AxRir8iQJu9j8bxJQeJfAYvDaEGwi7xFZd7WlydI5JGhN7eGZI9g4kJSUXSHKDJTMgLI7gwqMlypCRKuRkAIkyo7vXeVsSfJan8kk1V3p1XpIspqmK2T/02j1b+g1LojVLxDFlVeFT17xhlEVW1e8KoH0dcfKBLqmr7HddB8htVhk4LeMcHYdFK1Rrygs

wByyewzmuIBESqbtnpq2kg7N+17x3MIwWk5Rbzn4MjoxmdDzq0Eeeq9Z5GbPqDrNEwMwq4/QA1k96eT5gH0QCRlDMWTp/FaZRvoNG3Bz3QCBpTPG5RCb92mUwMm7hzPU1pv8emUQmakEaZhyAHN/DcxUxUNTimwy4CFmwEWrBYbcAlpm0Tstc6KyUk295J8NatCwjARorQhA8FwAi4KX0h0gJhBCZNbwKSTM0dxbE3tBLQnUcmglkcN1Ae0QkVSq

6lox3/EtFaLoT151OEkQOpjajTuHDOXsArq4gdFW5GyTcNNPvbrezyir4lfsCjPfJEGSgisyXS2K03ck/pHmFSDC98Omq83BniEaqlIe3kc+1aGPO1tPq03AVw8PdKdTtkZ2IhJJoOCSEzlG0B4uo9NWjtFsUzkYpd0oMCEBFvenslBOauM1p45AQtOCS2kjq4cG5YFuMSaefA6TmM/NC31OIw1yneYSGp4hBJkNoYRZDPo7YeDDjfnfiNrMkMDN

GfkjJyiDnLMICJpaMcdmKa40c3AOmkNGaBncxh2t3mub4B5sDCATPaeVxC2FnT5O2MkKZnLZx2IEuvNVh8iQhosY7GwDhK4TC9yUGIAAKSYVAC2UBbT7GwIbEFuWpEyN8QVzY7FtAjepexESpxGIiYgCHMEiltgzk0b7b8nEdxhiTkB3q8Nt2giT9SyEKe+vAyRNoal7wbinEHCny9Qqcm3tCXZcJ0qluzeVAquJn6yoqs2/q07O2MmAcnodsDJ2

0lnb8Ma4pMGV45Xg5au7iYHt5tQ5VEnzSGrvfha1Lpy+ekonvv0n1T9wp/a/ItHEq7TgQ9mQZEkL/o1Q56i8dOxcetQMR2R12W2jR043QUx0wT41xzwU8SXSTSJzIR+weXrVYwQESxbQ1h1ENBgCuAoAAEUPgqA+08tB00UnQM8I1yRvwyRvYDhIRKs0BnAAI68jhBo+w8UyNiR4dC9uAg5j04snQTNW86R28+90BO9JUkEe8okxDoAB8P1rNdQR

9h454NUsltUAlp4VC/0F8ilUx4dzV19btEMt8ak0x81dQHV99nVD9ywzgvsz8kD/NuobgloNECQKMppX8fUP8Y1eCyMY5BxqVmMkduootUdrx0dwDxNjlTpoDLofhBwVIEDrDKFJNTdotTMGd0B/oKAddygWcsg2c4Y9NBdUZ8B0ZRcoBxcJArNpdbNyZ8AaiPInMwwXMVdmZ1csdwxSBOYOBfNsiIBLRgthZRZ2dUBwjpYEBLdT0eobcwA3kMDy

g2BmhbRtZiAcJbQhAw9lRSCwx0U3gPhBtvwyN+xexLl+cwwQ5g1lFNFODDgKUtJ/9IAeCwd2V+shDI829DsO971u8nJe9O5+930lV1tZ9VDDsp8DszJdVjsITdCl99DYM18bsrUSgbVHtEYrCICD8z42lah+gHDDpuiwhuoxIvgk1Ljv4vCxpPDFlP9ZpaQS54YqVAIQii1Jj2NQC0FiTIC4izk8cCFhxyQUicSXRHkG0zd74/Ncj8iJBCjtNxj4

YBdkZyjKjKcxd5dygxAsgmB6jSZGjmiIBDNiBiBUU2jlc3ND4tZdZcB9YjZTZzYrZagbZ7YspeifNtcZTBZRjwtxZSBJYidpiBCQx5jFj7d0BGhFxqj+hsBNB9AdiB1eYyDGDi88QOJmShI4QYQloGDUB4RtBA1thyQI135NJ89E4sk+DUQQzeBPjBURCfjZCJCFtpCXJlt5DQTh9v1R9wNx8dUAN1CclwSdCSgoMTVkTylUTN8Xxt9zDd90NUj0

JsNz4dgiSlznCzkY4tJNJ406TRo5lfCv88Vs88Vep2SwiAzMjkFIiwDeTYjTlzoYCyQRIiQTNSENyJTUCqiCilM8jvSypWcTdeBSjVTDMKjjMqijS6iFlZcmitTlRWiXR2irSuj3S+iBjddhijcxj/TAypiZi84lYNh0CIyIBMA4B8JiAeBJBY5EysjFDyLHgNFCyy0fxA4E9I5348zAIIRKVxt8RERkiC8qz3jgYQ0fEGzr1a5my/iW4ASZCgS3

0e4FDlUeztDtsBzx59tgMYSLIRzNKIBxyz8DDrtKkTDZyzC6kns98xSsNbCmp4h1y7KBA78epOJSQPEbgP9DzQ0llGTsQLhyUthzzVotlQiqpOSs1uTc0YjjooCBTnyjhQQFJRS4q0iydUAUcNSsK/y5SciVTFSSjCqhcIKRccroLJdGKZdDSEKWjFdnNLTVcXtujNd+ivTBjsLfTgKoqYtgyrdQzlZXlwA7U2k4A4BjRsFuAGxoA0QMhLMrcJgG

BCAEAKBNY2zZVmydRtqdrRQIBsARA+5Ggjx9BjQZKlK715s9qDrSAjqTr1qFL2zZCVtB9rN9rDrfJjr0gcJlCUltt3rbrPqTqzrtKtUlqbq7r0gQatC/rx8AbIb9BuhzsJyCh4agb0hbYzKENUaIb0b9AcIyjwL1SShcasgvr8agKlSEY0ayaTqddKqpdwaPraaoaohSYsZbq2AKA0RcA0KcbmaoBybKg5QObmRuaQgNYNQxbrqBbybRaubWgr8j

Kdo9rmBsBmQDRtY8o40GIo5mTmJqUfYj0Kh1auR8BDZjMfhtAk040hxAJvhRIlqjA2ADBpq5wCBJYr0EgNFLgzhEsabBaTqkbr4z9laXI9rpQSBijdMlrI7iBjQEBFdyr0TejiAsI2AGphbcBNBghvzUa465U1ZNYuQNZSBlBxQAAKMEHcXgGOagWumuklDYAASktBFmUELA1BmHLtwCrunHrqTwHv7rpGttbv9tJqhgyUxrJk4HvKxLQxFlLF6I

WDdpdEyGztzsiyvOcyICTomO3rDH6PmrQF6vDCFlAjwrQNt1RrsDGDmGYENH6LgHTszv6JzslMzTaTmEIEYFaBdvwFXsmCVrCGCG/tmWcyEEZAMEVv7ScK/KkylIJlCGqO/t/v/u42QnACGU031HCGmtbBAFbCAA
```
%%