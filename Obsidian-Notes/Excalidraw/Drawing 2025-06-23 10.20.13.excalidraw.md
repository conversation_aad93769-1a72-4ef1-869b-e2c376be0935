---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data

## Text Elements
属性图策略:给边和节点加上时空属性

结果: 由于有的边和节点难以定义时空属性,
很难只关联一个时间到一个实体. 如果附带
多个时间呢?用关系来描述?. ^EI7mlYhd

1. 每个节点附加多个时空属性 并描述关系 ^BRU0LISn

2. 将时空信息独立作为节点建模,其他节点可以连接到这些节点上. ^H6mxyIOi

%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQB2bQAWGjoghH0EDihmbgBtcDBQMBKIEm4y7ABBIyTcAEUASQAZIwAOAFF4gHE2gFkABgAxAHkARxhUkshYRArA7CiOZWCp

0sxuZx4kgEZ+Usm0ZwBmAFYB7R5446Tj+NP9yAoSdW4kgb3CyEkEQmVpbg7ACcA0eEGsK3EqFBXwgzCgpDYAGsEABhNj4NikCoAYh2CHx+LWkE0uGwSOUiKEHGI6Mx2IkCOszDguEC2WJEAAZoR8PgAMqwVYSQQeTnwxEogDqL0k3D4sIlyIQgpgwvQovKYKp/w44VyaE+0wgbFZ2DUh1QOwGMONlOEcEaxANqDyAF0wVzyJkndwOEI+WDCDSsBU

AFpGTlUml65gu4rG2ZQ45fAC+YLCCGI8p2ADYkm0gTt4mDGCx2FxDQNjqWmKxOAA5Thibi5u4DEFtXNAoPMAAi6SgWe4XIIYTBmmENI6wUy2RdBWmRS+pXKEj6ACF8BujH3jjs+85TgArDo7IRVKoDY/8gASxNKSYqbMRVBX6ZXCeNa/QfUIACliHoUgAGljiqTRCCGTBbwAVSMbBnDaKpUQfGZ4ChcFSFfCB3y+D1YSEOBiFwIds0Ne4BnzU5cx

2HgbiSMEiA4JE/QDfAmLYclhzQUd8DCQoPxKL8ynI9A2kINp9ClAA1Y4Gw4DcZIADWaEY4FzGSDDgAAlTknwkF82CoMENiOY42gVY1LROD5LmuW57jBZ5iFeQ0rjBH4/gBNAa1hCF1VtUolRROksVxQkCSQCcyQpaNaQxcLGXIDgWTZLIoE5Hk+VVdU4QxLVFQRZUZVcuU0Cs4LipRXLMM1bNtWEXV9UBMFTTJC1ARtMF7SIp0FwI40vVwH0xP9Q

NYWDYhQ0MngoynYhY3jFdoAw+U0wzBAeN4D4gVzfN9pLWEy3rStUHiNpa3LRtmyhHZjgGeJrge4FewHYIyJHMcEAnBaZwyDKXXGjjCOI0jtuLc5qO2eIBiSI7jWY1i0GBzjuLEvjx1hOA2GDHJ8hXRclyC6YBhXQbpiJ6Y6IRpcTnJ/CmNCKB0X0fQ1DIgAFXGOTQKmwBpx4wHp6YKYEKJSCgDcpuDZQ2Im40smIaWaVl+WQeNeE2SgKpsOMn5cD

G9iwSV3XXwNsSsJwk38EnChtsxhBBP2ESfwgDpGnifR8AATUkBrYQM9Ah0wTLTM2HgeAeWEbP3JJ7JuO5o+NFy3NQO42mSYF4iBIE2jh3M2ic2EvP+TL3OT0oAqhEm4WqtFEoZdA8SiokYvJXqaTCpvoBStL2TD2FsoFIU6oKgPNfr0q08q8XJRVUeKnq+a/EkJbWthdrzVgLra96x1nXyMXuW9BBfRR43JpDMz0FwNoV5jFqL4V4KtrEnY6NzeJ

bh4IsrtO7gpwdiMWOnWCsTYOAtgojaAYQCkhJErpAQg/ZBwO2+r9akxB/pznxs/DWpQiIkU+hRKGBZf7XBokxYMyNUCo1hJidGX1+I/UDlgcu6BAB6OoAcgNAB/aoANW9ACmrsgQAm36AE74wAMSqACCgwAnQ6AAKlQAUHKADfTQAXl7cIADocA0YAZb9AA55mgQAjK6ADi5QAkOaABC3CRMjAB9aYAU7lABZ2oASTkVHcOoBowAEfqWMAFfKgBn

RUACoBgAAOUAFRyijAAvqYABiVAmADztQAyvLaFQIAIM0dGABE0wAZHoaMAFiaQTgmACMVAA/IAClcvGAG+fQApuaAHnjQAD/E5O0FGSgAAVNhFRuH8KEWIqRcilGqK4Ro7RejUBGLMRY6RNiHFOK4S4jg7jvH+MyeEgJ0TYkJJSekzJuSCklIqVUrKnAoD8kIEYKEPBa5cm2UMEavJLSIOgGwqoRBlBnQgMELkg9jRligOYAgNy/j3KgKaTkehs

i4GDEwc+tDL7GixH8YMBB6mh0abwwRIjBkKNGd0jguiDEmPMW04ZjjOnjMmb4wJITZnzPiUk1JHAMkhNWUUsplTqlglwEIH5Olwh7KhAiIQLDEZAtvL8MugJLinGdoUV2ltNzbl3PuQ8J4zwXivDee8YIg5W2MpyG+xYewx02PuY4CdHKXNTuVK0+ZPL8p8tCRlyxAoZnrt3CKrdoqwlJB3eK9rkrMlZAPLKvIR5qjHmKW189p7GtnnXeetUl7jx

Xs1OMG9wVmk6lWPeVID4DU9KfEFdDvzX2fECB+i0n6oC/OhOYFUNqKjftwY4D1c4gjOP/Cs3AgSXJOuA26gIeD50hucLV34UEfTQcwjB05ZyA3VmCQh4N36UWorReiCCqEsQnfQriKIMboOxjzXBrpCYrjACTEoZMlxizAPzAWpr93HtFozehzNWbsxkFmbmeMCZLjzCA4mt7J7axVo4ZY3AS0YDHdkEFlQah1CaK0ToPR+jDHGJMIW3IuJCBdM4

C4tFYE8DbDsU4F0gTwzuEh5QuA4CAj7UNQgmBn3bvVhWxWNI/1qzwba7WZt9YhCNi/SAps9YUAts+PjnIgh2yHQJEoQllzfktiMY4+gQIIHoGMY88RYLEGcMeECuZ6jEE0DJYg99lVrQkAsJYkJ1XVtOJc2Opw4hXETsXFOso3jdRLua9hhzcxWvM2gWuIUG70gdZFTkLq4oLXdcHPuXqMo+pyovEU0ag0lWcxVJLNV4sakS7CHUa8i1GlKFvJNV

pXN2lTf1I+GaRpny4/gpBubDJVALevNAQGg4pmmJJgQVa0CF2/kkPaf9QHXTOkAxtN1IF3SAW0U4Rd4ZvVQRu4dzq/ogZ3fzEtpbMIh2eUuUSFQNw6VggMZojR+RcDwqLSdYNiFWlnQgvD9wgR+R5culjq7GG8W+iK4Sk1LYHaOyds7+ljPBwaeHXy3ZtC5w+HRB6SRevWYjrRBIDkk7ORS+nWG2hdpPVOMcEEuYo5mu8h5y51duB+btY3ILUUQu

xU7glQLHrUrRY5J6X1kaEuBqKsGjHYb/Oc8y9z40OXmtWjaomneyaeplcPnzY+w1RorpzdNG+4INxNaLdm1+EMQQDC7fmHg+XIBts4ICeHY2OAQKgbwbY5wrNtnm4OxbWNjSTkwdg8db3jRTpu5DKi93cdPaXTQ7XkAGHrqYa7x8DSJA7FiYAeesAkyMSbIqlnTUCADc9CpRSakUBhewiA8fUBJ5T2noJGfs/lNz56bZuz9nyiOScs5+ALnKuubc

+5YhshME5K895+BPl3IqOzYgxBVhgn+VEIFpAwOyfk4p5Tqn1Oae07p/ThnN6kEhRwaFsf0DF9L9I1P6fuFZ5z4UzkTKWVsob2gTl3LSjMQQHykngqo7fak6ucVAEgKgXApBNBHBAhEhChMDmWrfEJuDlaG0MbhALHB/PqmjrCEaoCDNsTgKhVMHv5NajXGlgFklM3JFG3M6vTm6tTszv3DFuznFv6lGsLlVLzmVPKPgYLvlAwZAKLnlhLh1FLsV

img6OVvLpVkrt7quPVrfH2JrnGi1itEHDwPRjrmJLmKcARscPtLRJbtWtWJbtblCPDqodsNNs9quAOggDdo7COlgqtkDGCgQtdhDHdjRLDouvQtQsrk/mumJo/pADjHjAuHukuAekLAeuTELOetcCEdhmEYEUuPEF5vulHMKieuEfurhpdIkU9gzJdnevCA+hzDRq+nzCuOkULL/O1iUMfFrJLExgBrIbtukDgmBuYBBg0C0O0F0L0IMKMBMA+Mh

tgKhpsBcLAbAl2NaPDAMNNgRsRqRjmEhjyNRsQC+rzKCnyIoTxoxjLHUasbVnXGxnxgJmIRscQOxvxpxoJtbLCCJsZN4Z/mKhUDpM4JoMeI8W0EIJgPgA2M4BuM0AMGwJII0BwLBPEOAZhKZuTtAWcHATZNaLmEgY5qUKgWgO8LXKXBap5t5jajzsqBFhAC3MFu3GFpgriUyCzulGzkPBzhluwYVJPEwTPKwdScvI1KvGLnAYVnwdaAIX1HLq6Ar

pmjVkGBIeCB0NIctLtm1usXCN1laEkF2h8PENhhRibmAmbtLkNqdHoa2I9m0GcDnE7uYd4VYZ7vOG+tMBtqtBAVcrCiEXthILeLmPoJgDAI0CMIQLhEuKmN+vYUQo4aQoTl/HRHAUjB4eHl4S7k7BJi7L9hUA6U6S6W6aCRUNthZhDvHN2DaE9rnLsO8AkdZBHJnPZgaujswb5IqdoLRHnHRDDvEDsEXJcmiewucJiXgdiaFBQUQY6nTq6uFh2b3

J6uSTtpAMPGwcyW2QgCGiweOaOVliLk1LljIeLpvJLpaFyTLoIbye6CIdVqGWUMKbgEMGKbuZmDOjcFRF/LTCqcNtWnWboR2j1g9DROeXAcgu9IaRGcaTYRVqDL6TOv6Xhi2uoSHruRHkaawrChIHEKgIAGA6KigAh/KAD2BoADdOgA016AA68oAFxyMigAX3qACEVtQIAG6KgAa3IyIaKAD3ytYoAHvxgApcahKACb8YANlyMi8iDK2WdS++EAU

FsFyiiFqFmFOF+FxFMiFFNF9FTF0iLFWy2Q9eByTe2Qpy7MregC7eocQ+XeGUveV0by7galI+JA4+TqxoU+gKeos+lsjxzxrx7xnx3xvx/xgJwJnIEK/ge+EF6AXF8FyF6FWF0ieFhFJF0iIltFjFzFrFxo1+bArKrAd+qAD+S6L+7m7+yRYAXpYAg04IcAcAgo4MgGhQ0APwmQFQtyAI+wDAhACAFAG4ZBvZTOzcXI9VDVawEAAx2EGUjQQ4+gg

o88uJ+JtOpVLVA87VGQVVPZxJfZpJVBFJpQA1bVHVQwVJdBXOtJkAM1oGHVXVyWpZvA/VIgg1619cM5DBzVu1s1GQOk85bJO1rVa1GQ6kvBq5JWK1J1N1+gQwzeilbeeVq1UAQ1r1de7KjeV1e1GQBeulEgjyO2x111P1+1+x5s5xYhUNwN+gHQNIpxhxkBlx01z1MNGQpxtSIOEA8UTVzA2AiIfIykbweY2glccIZNGIvsgCe0CQ2GNEROeVRgb

ABguVLyBAXKgIn+SNp1+g51mCYuRNC0TVlIJAMlgNeV0txAgoCAZGaAtNCtfQbA00qNuAmgwQH58t2+jOhBIkG4GIlspAygpIAAFEbiWLwMWNQPbXbRcKcAAJScisrKABhsjzCW24A20PSO30Sgi8CB3Qg03u2C3fUbUojqRvKcC2EvwnxVasohjb7bEiRZA6163cBxWwjYBEAq2xWkBcpgi76FX34l0+EmjMrP651V2C12DHgICLDMBnakYa1a2

7661gVVyLCECMC1Jc34A82PiE1hDBD91m6T6oY/L6AE0QFh4PLhlR7V34DMxVD92D3D10YSbgBCTci8jhCAZemphAA==
```
%%