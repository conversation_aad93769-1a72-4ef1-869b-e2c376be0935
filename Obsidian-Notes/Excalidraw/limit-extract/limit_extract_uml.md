---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data

## Text Elements
%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQB2bQAWGjoghH0EDihmbgBtcDBQMBKIEm4IAFkAaQAFABUARQBBJIApJIA5AGEAdVwAEQAZAE5SADN6IebUkshYRArA7CiO

ZWDZ0sxuZx4AZgAGbRG9vYBWc4BGPZGkpLOH/lKYHZ4zo5GeS7P4g4AOP7/EYneJPSAUEjqbiXe7afYANnhZz+8Xh+wOPCSfzBUgQhGU0m48Mu2gxMKS8N+fySI3h1PhOOs63EqAOOOYUFIbAA1ghumx8GxSBUAMSXBDi8WbSCaXDYbnKLlCDjEfmC4USTnWZhwXCBbLSiDjQj4fAAZVgGwkgg8ho5XN5vUhkm4fEKAk5PIQFpgVvQNvKOKVBI44

VyaEuOLYuuwaheEYObPdEEVwjgAEliOHUHkALo48bkTKZ7gcISmnGEFVYCq4A6GpUq0PMbPFObQeAsvbugC+7IQCGI0L+Z1u8VpGJxjBY7C4aB4I6nTFYnE6nDE3BGlzJv23leYA3SUEH3E5QgQOM0whVAFFgplstmCnMiu7SgsWdAsFBpaVyhJlAOChKmUGAbz2CA3x7d182TIQ4GIXBjyHCN4neQ4bgOEZASSHEiA4blS3LfA8LYeUTzQcYCDC

Qo+0KNtIH/dBAOA0DwMND8KmPTAfxxbY0F2A4zlJB5vniakznhWlKRxeNUF2PY4i3f4kjQnhEXeW4cQhYgoTQE5knHEYDiSS54ixEypJxSQ8QJH95zORk1j9JN23tL01SFUVJQlJBLzlBVG1VAUvM1cgOB1PUsl45NjVNH0/QgAMh3ZT1HWdV1Uodb1LU/ZKG2EEMw2hKMYzjaFExxVN4MzJ9YPbQtcGLFDUDLCtkyrYgawkOsADUCuVYhm2zNqS

OTMIKNQS4eGwr5KTOXDk2nFc51QP49iXGdV3XFkzLM04zh4cT90PYJkNPUhz0va9iDvDJopG4icXgxDztQ9CDkw7CTLwqtCLQUbSPIlqqPwMIcW4+z0D0Dhp2PYUg0oepvwqGG4aYQ1xk4KAzUIIwWR4VzSix7IADEmpNOTHOTSHmiIZRVogMRsgxzaoHMAg6fxRn9BIYgNhxGGoirJgSwkGoGhadouj6QZRgmKYZijUh8SrAhkZ41HOHRhHk1wI

QoDYAAlcI8ZZM8L2TfCEAACVswkIzhM5aPAeqIFwOA4AtJCWTbaAbMyCp6cJJ4GEIBAKAAIQC6qVU8jV0BFcZk5TzYmZEfUoHTY99AtbL4+83ypVD7AM+i7OMmj+VY+C9UuPCyLM7T0vSEziv9FJk1zVyip8pLsvsnbvOvSdXSXXnfvW/LnPh95BK8oFQNCnTqfB5zo3CskYaSuXlu25zgB5MrYAqomV/3jJSex8nefwKnJ4vjvsdx/HXTPvfp4y

DWoC5hmg4QcYMVSgfzXhkb2pAf6tzYBQGyuAWqA13gPLOOcbwqmaFAmBIQWruwwc3JB7d0FcgoPUTsSwbpp2YNgLkpoAAa3A/i3DhCMB4qIUR/DpJJUOlDqH4AAJrcApCMbQexAQmSOoTW4aJQ5GDYAYbgDEGAEHPNCbQlwtyKWdiUOiwD8HrxutvCQQU06KhIC/AmZ8THEAtAgOA3BqalEsZUNgXVUG4E0MEEG1FLYOJVrXUKqAFGRwFNg0gyhZ

QAAovigl4GZagMTolHDOAASkNCbZQ5Y9RLDCbgSJhw4nonyXk1k2hkmQS0Q/aKs8EBH3ZpwR67ViZFgQCbasKs1jyOTFkNxHiLpXWTNgIgti0AWxxBwJq5tLreNKMIKA1tekIFoqHOwAArBAKxmBmjGXAJxLixnuMmqDcGy85Ts0YPUWR+AOntk4tadIKxZyGlLhyAwJDFgAyelbMivJPFgymZAfAoQf73LORcoipoXZgG0UaE04R5HQR7EAA===
```
%%