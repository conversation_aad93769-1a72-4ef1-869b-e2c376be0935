---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data

## Text Elements
ToolManager ^WlPCJcdA

最基本的toolmanager应该包括什么? 

1. 提供工具,这是最基本的. 
2. 执行工具并返回结果,这是其次,也是很基本的.
3. 支持mcp能力. 这个后续开发吧.

- get_tools: 获得当前可用的工具. 本质上还是list[dict].
- exec_tool: 执行工具. 返回执行结果. ^5M0Eaf1j

%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQB2bQAWGjoghH0EDihmbgBtcDBQMBKIEm4IADMATgA1fCh9StqAZX0AcRghfAApAHkAYR4AIRFUkshYRArA7CiOZWDx0sxu

ZySAVgAObQA2Dd2k+I2ABl34gEYAZi34/lKYNZ5j7Wr4q/2N+JOzrY/7yAUEjqbi3DavHjXS6barVXYnaoAqQIQjKaTcA6JHhbJIneLY45vXa7JHWRbiVAnJHMKCkNgAawQAzY+DYpAqAGILghudzlpBNLhsPTlHShBxiMzWeyJLTrMw4LhAtl+VVCPh8C1YEsJIIPKqaXTGQB1YGSbh8QoCWkMhBamA69B68pIsVojjhXJoC5ItiK7BqR7en5I0

XCOAASWIXtQeQAukjKuRMlHuBxuvgkYQJVgKrgTqqxRKPcwY8UJtB4BSrlaAL7UhAIYjcC4XeKwq4XE48RFWhhMVicFuQpGMFjsDgAOU4YmHzwuW2qPBOGyzzAAIukoE3uJUCGEkZphBKAKLBTLZGPxpFCODEXDb5ve47PXaL/E8K53PtEDj0tMZkirLCjuaB7vgYSFPWhTlpA5QSPoJ49AAmic0b0gAshcyjEJUbAnMobbEC0bCqlMFIQL+SBIq

saDrF82hXBsPCbIuGw3F8SRIkGqDOMuSSvD81xtl8VzVBsXxIkCxAgmgYLaCuFzVFsxLVEkVxMb2FaSCiaJQNwVJ9mSjqGRWhq2lKbKcryPLUX2grCmG4qSiyVmyuQHAKkqWT6Ym6qatqFHOs21I2iaZoWqFRp2oFFTBYWwjup6Bm+v6gYtiGfZOZG0b5AmfZJrgKZPqg6YalmOa0eguA8AlzklmWVqTFWFp1g2oGoG2Fz7NUnbtqOA4TtwX5cX2

Y6DlOM4Ul2FzPFsGxJD2a6bsEj67vuCCHsexBnhkPkxmVmZ9re95rc+zHxG+sI8D1QHZv+aCHUBbAgSV4EHn2cBsNmOT5E1BQTCUpmAycTX5YDAOA/JinKap6maQCQNg1a4OlPgoRQMy+j6Goj4AArfSqj2AX2NJKlAwzZo4CwAeVfZZMQlMStmyi00dZlRKQUAAIKkHSFA6bgJVPfTEq8/zgvCxmUH3LBZQlRAxr4HjAw9NgxDc2RLWylgvl9lV

zitnE1RdV2STqa2zFXNxTwSdobYaX8XYnN11TdlJEXelsFwKbsPCrn2OmouiaAaXspILCZUUWa5MroFytl8oeQoikWLnShUcqeYqyp6xWlT+fajoQPF0fhTJ5poJaHPRUXQUsi6fZupIDUpX2fpCulwbA5A2VRleqOQIVxVsxVxC5hIuBXHVxbJcTdNmY2JUbCbJtbHiAcVuNQ3ektY2DZw04cLO3rbM78RJEpy1bh172bfZ227Rev3z+zpQnQ+H

Uia+btXCuCJ3T/KPH8L1GRvQ2kibcmB9ISAACpsBZBhaw0QmCFkoLA3WFR4GIOQcoVBiZOBQBaIQIwFJsQEOyAAMSKuqHi1s+xQJ5kQZQXAJDBEqHnUoY4oDmAINzZhrD0BQD9KqPQ2RcDZiYKmV+vpSComzAQDB0CsEIPwEgjgKD2SkiEMIgASuEEhFJaRCHvhWKiAAJXSIdOraB4DLGCfZ4LoA2BhE4J5cCVAuAAKy1tMHWyiaJrHUrsV4psTj

m07BcK2Ns6LdXBKJG4nYhK7C0qUaSslUAbGuK8dsuwLiLS+N7f2SIg56QtJlCsxkKQ9xLmFJksdrKJzshWByqdtqWTjtADyXlc6qgLhqOucUG4hVJnU00FdIqjNrrFXUwy6pJVLG3CsHcAywAyjUvuuU0DXgKsmBA0jSokwrFTCe1UNgz2IK3GRpMl7cBSS7N8RxN5cIPoIy4zzIDb0PlNFsWx15iSuEkPJ19Vq3wgQ/ZyT99rAIrB/M6nUXyXTh

IcfY1c0b3RhWjUBYKIKmNKIwiogAAc0AF/qgAac0ACFuwiWT6FwUwQAKXqAFPowAoMqABujQAA3KAAk5AA/KgAAOhwflPtUCAAXjQA2fKAFPdQA7orUEAJvxgB6MxJRS7QfKOBxFQIAcyNAAyEVKwAbnqABX4wAe2qAGW/QAOeayrlYAN0VACE1tQQA+nJysABH6ZLyXaH5VcZVgB6U0AIDG+hsBwEAL8BgBspWVTKwAVHKADgVQAt36AAB9QAi8

qAHIVV1AqXCoDwVAAA+lSiCaBADtwYAdP1ADK+oAWSVAD3yoACldyVSuVaSwAFLGACg5QAG/FyqIDSPIjg5hxiTc4VAWAEDYEzaotAWqq2oENVq012g0EUCUTA9AirKWqJpRovBpBGWss5Ty/lgrlViqlea+dyr+VquHZK/VxqzXyutXax1zqk3utQN631Abg2oDDVGuNiat0prTQOlk8hUD5uLeWytkrq31qbS2qAbbzBQE7fy7tvb+1ZqHdq0D

o6DXjpNZOihRDDHlJw9QnG+AeIfOgLrfhqJBGUQQBw1U3DeH4AoywzOIikRiKiJI0gByRbLLkf4RRmCJDzqzUuzRa72XcpVdukVErpXyoPSq49qGz2mvNVe+1TqlVus9T6v1QaQ0RpjQmpN8HU0IAzVm/9gHS0VpHbWxtzbCCtvbbBrtPbMB9t/fgFDI6x2aonaqXAOi2D6NYKQ7gxi8WQAsVY2dPt/b2JKHLJxEB6TczIAALUoZoAskDtZCMwYE

qun5kgAvNt7H4Wwewkj7DxZwVxsSMXeIk52rsPYTLkiU2LFpSNVKWaUcyjIOmNJsqqVpTkJTDfcvKHOPk+mFxmU6OZZcEDjIyWi600yHT131K6RKLc56UlSp3NZ3dQxihygPRMezuNHNKCcqqEBcC7AuVcw5C8Bu3NDuJFSLsJIDXHEOUOAOJpHxPgi/Y8R4iLjEiC8zHVItbUheeaF1zYV3k/iVb+SK3wpL3mYjFaOsWvXWriyBgn0BTpnRUPph

DiHharjUvCVCaHEYxOT6BTGqPsM4Z8pgPD3Bc5Y3AURhCJEei41LD7kA2TyI0fganEhAvBdC3htAiOfySMscHOLtiNhQXAODJ7cA4Bak/twcs0AdKZAqMw9E9wGCEAQBQYYKcJvpzcvHSo3uffLAgNgEQucIzbn0FqaKU3442SToUf3gefLB4yK7xyacI9dJm95FUDuA983jyHyhC3ttDN2zH7PQeQ9h9tGtyuvAs9x+yAn0PdTBmzOL6UUvueMi

6P2294Gsec/15D30NKp3OoVMgO3gfGRKGEMI7Q9nJe69QAb9P7I9OyG94n0vkPM6hdsJo5wvvZeMhm65uLNgAsQhS7fofjviExZ83P5LPMD+qC1/71vjIZ/p35YgGnP3zBsA6QNQAANO5N2bQA4T8c4ZSRcGHB3AAoA/AZCYaP4WxJIcSfYTAhra4Z5CAIwBBfQC3MaAgExFsRLcfRfBvLveqQ7X/baP3UUEgNffDGPRg4ibcEXNAXAtgjCNgceE

8DRTQYIcBMnVguRD3OOOWYYFkBWUgZQQUAACkhDuF4DbGoFUJUJOAgIAEpVR9FlBuglQZh5DcAlC/51DPwqReBzDKQdCIByCb9V86kh8eFOADo7sqg9l9Ecw5EaY0A5YshcAhCEdSATE2MiBODUANcKwNEbd1dQiosIBhAoAqIIsEiHC7AvE+0cgWgNE4BeD+DBDhDScPpSghQeFGBsF8AiCKxyI4p0g5gho2MhAaQDBYF8seNicwESjEj0YaRuZ

GjKjVE2YDcwBoIh51RwgLdawQBawgA==
```
%%