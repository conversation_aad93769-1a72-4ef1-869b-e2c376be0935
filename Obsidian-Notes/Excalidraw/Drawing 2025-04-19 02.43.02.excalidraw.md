---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data

## Text Elements
%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQB2bR4aOiCEfQQOKGZuAG1wMFAwYogSbghSAEkANgBGAHUAaWZmbAAVAGsADgoAMQBOZQA1AHF4owAhFOLIWERywOwojmVg

6ZLMbmcAFm2AZgTdveqAVmr+nlq9rvj+EpgtnhOu7Vrtnni6nj2ABm3qm4nO6QCgkdTcHj9bbabY/H5dbZda5darxWr9YFSBCEZTSbjvA61WpnOrVS4/fp7eJAgqQayrcSoH6Y5hQUhsDoIADCbHwbFI5QAxLUECKRetIJpcNgOsp2UIOMQeXyBRI2dZmHBcIEshKIAAzQj4fAAZVgawkgg8etZ7M59TBkghLLZHIQZpgFvQVrKmPluI44RyaFqm

LYWuwageIbhmLlwjglWIwdQuQAupj9eQMknuBwhMbMYRFVhyrgfnr5YrAy1uEUZtB4Iy9rSAL4shAIYjcalUonVPaYxgsdhcND9LpDpisTgAOU4Ym46MpPz2+yeReYABE0lAu9x9QQwpjNMJFQBRYIZLIp/OF2kQIRwYi4PfdkPxbYnOG1eL9E7xF0GIPkQHAdHmBb4JifIyvuaCHvgYQFO2BT1pAZQSPqQgjO0mgAFJwP0AAyAASPCzgAqr0qx4

fE55wHqcyMhUCBLPSayYpsaB7AccJPMc8TxMcXS1NU2yYtGqDODwYkJHUFJ7Fc/RiQi4kPqCxDgmgkLQrC8KIsiqLopikjYriUD4t8rzEtUpLkpS1KYuxjLMg+tpusq/JCmKopICe0qylWSq8l5arkBwmrapkFmZkaprmsxPrdi6doIA6mlOtpKVuh6XoQEllbCAGQZLmGEZRkusYPvGT5Jim6aZtmCC5mgd5QQ+xbEKWEi4LUhUKsQNYpmhsxNt

wLYzChDZhHBqDEnsUJogOtwPsOM5jrwXSuQ2a2jvOHCLmgYlru86IrQ2hDbrus0IceD6ngNl7pNF9UZg+T4vm+S6ft+Py1CJaKAdBxbga1kHQWwsHvqgt0IMhdxoaU0MQMQACa/QAFZcgA+ggACOIxDKcPwjBMyhEYQZqEIxY0SIsywMnqXFSeiLwDl0Jy/EJtQ8H9v4SY8FLaL82yiTcfNol0k7qY6ELQuitR/HCC0/GSNINqZOJ4kdJxJPEPwy

QJ/2OQ+zncNtJTuZynmqugwq+eK/kyjVio2+U6oRVqOoxQ+hrGrliW8r6bmuvastZSHqUB+UBV+kVkhDaVD7htKFUxhbkA1YmyZ5G9DZZrgObQ21RYlszEC4MkccDYnaAjY28zcW2HazaJS0XBzg6rdOo49hnDA93OC6Mor/RCYJlyhh1V3BF98FHggJ5nsQT3XtkeS0oUm/ocjkgAAoYyc2C1EMewAFowLUzgwCaqNn1AACyeF45oEolExZakOy

VCb1NMwjTvd2Iw969AAIp7DwlyWo2M4CzgxnAZwJwRh7AQHvAAGm/Uajd0Dam/hAX+tI84lA+q+VuP0fwXEAmuYGYEIL3gbDBTk0NYbw1Qh1ZGZ8hDY2wCafUMBJCYH0LgVG9BnAdCaPUeg2B4g02wSxNiKwOIPmZicXWAkRLnCeH8KWXcGySWklcbQYkDZAV+LZWEZJMQaS0rweW/RFZ6RVmrEyZltaoFOHrA2A4jKAicooly2VrYhVthAe2Pk9

RSmdkFN2YUNRe2inqP28VPSB2tIEtK4deDpOjpaIOyUHz+gTiVEMZVU6wEqv3LOdVc6NULs1Yu4MOplzLNsfq1ZimoHrh/Juk0W7Q1/P2YxHMpwjk4EuFEIz1r7UOqgeI3wzh7CeMBC6M8EBzxhgvJej0rwvQ3jMLe+zAESH3ofY+p8L5XxvnfR+z9X7AnfrTHBX82A/32X/YoACkZAJAeAyB0DYHwMQcg1BGD7lYOYrgl5+C3mEMxCQ9Zv4vwUI

+EidWJRQKg1QCXECkMmEHgXqwj57DygnKPifc+l9r633vk/F+siIXPKoJxLYol+gwm2J+foqsLhXBROde4LKoRJFFn+H4gk3gImuFYzJlw2UiU5mKuoksTYa1cRZNAPxtD2J4Dcb4bwvyKQ3Kbfx5t0kxLtj5R290Aou2Ciqd24VIre0SXFHJ3o8k2lDhkjKzpI45QSjHD11diq1hKcncq5T05xnlNnV6tSi50PahdZpPUThtMGh0rpjyeDNzcp2

aGizKR7E5vzbuoyNqK10SUXaQ8DqMh4LzHm/40XoVWes2GWyLw7JvImuFz5SH9PIX9KWxtqg0MxdihhuKbqbIfHANgxZ15oHyPsld+yM7FB+JvIhxQ10zFldoeVXMlXGxbcUZwmrtW6sUrsEtTxt2wpAqEKAPJ9D6DUG+PeC7dRg3oZbKIpAoATE6sWZQdZN4YG7VAFq6A0aYxxvjQmxNSbk0plAam9yDSQyECmC92hOaIihCcSEtkUTzUw8oXAD

E0Bszmb8Bt/wFIqvzoQTAXYv2Lr2fs2oSRVYAi2v+akHL0RnrAJq74iqHL/ELaiU4D7ekPkyMQYDipQPgf2ZB56WQYMQE4dw3h/DBHCNEeIxokjpFvyw9gHD5sYTFt2PrUjKJqj6Qo1Rns+HDVnoNKx9j371Wpm3nh3mTmKQAROMJ5SY7t7iaRJcWyZIeCwi/Fyk48nijvPygBqAABBRlplcANL/ZAJTeXv4FeRpCplin8CngoDOxCcMMsI2JWqY

BYCIFQJgXAhBSCUHoPpZ/PBzK0D7Ds6rS4gFBLXA5WpPRLK6hav2M8ICvNi3fn5SCGVhiAI6uNkJBaAE5slE1uZU1xrGYarNcE7yDs/LWqicvc10BHXxN1LFf2AbclpL9WHH1EdpperdflINBT461zmqUyMka5pVQbFUnOy6d0GiajBydJROrdRwdUdNEOs3YJzQp6a+buBQkRAav6kze4hjElT2tMyTg8wWrsexm4dyzwa3dBsD0u1aaXVixpDZ

4VkKRX9KEYt+4Yt7TiqG+LGuYnnYu+qm892brBVu/ZO6wCq7AAYg4u2RI3AO4J47MwNczDTI+hhz7X3vpkH5zjv6k3/u1EBkDKx1MNjSGvHTemeF8IEUIkRYiJFSJkZh/U2HcOaueLCESSWObx+2FCNz1G5o8YciJq43NVwRYj754gHGf0C+NLmr3ioVOOA93XCD3voo6bg1jXGBMibfhQxTKmlnI/WZTJqr8PM3gnH6MpdEoseKp/xMLH4xGHKq

0REpKtJRDRscL/5xNZeXeAbKy8ir0vy/EG3xQXfPVGV6iCHVznTWwB/0RhhdAQh9SqH1g/Hg9Q8KgNAfgUBMASIRckJIUFB8bpdAemM2EbKSX4A4FWf4DmMeUWS4AWNAZwKkOIDlCcZ4BaSVZjLbf7XgIVPSBEJEKWIyZZE7NVSyQkGyOyUeKkFtCuE1K7X7bkG7CQMJXyCJG1aJFg9AD2J1BJD7ZJPKWOJg9KGxPgJg4HYQhsQpCHKeBsFOaHSS

RWSpGNapJHeNepPfDHFNHBPYXHTNCDYAiaZrPNWaXmYfdAqbOnCtA2aw6ZEeVcMVISZbNna6aGNkIQRee6ZeVeXZdQ96ftBFIdf6GbACcdLQyARhS/ZCcAPOCuOAOAM0UhT3aAUyDIcoIgM7O4BgQgBACgCYTgp7bg0JfUUoso9YCAazL+evPcfQM0VKZ7Ngq1EoKo72SoWogox7AaZ7Xgt7H2FokQNo2o3oV1L7d1H7AY6o7TWo+ot0UQzKLJAo

SowYmo9IWYzkSQ0HSYoY9IAAJXBw6TkMgFaNWP0AAHkI0lC4djiVjpj0hehOAoBehC4jRJI0VlipjoNhjHiTRCAjB60LYPidj9A2gsBctMiNoIBgh9R+ibjPj2i1jstD9j8S9ncgTTjzxFRkSQhKtT9siTi7j9BD82hHkKhl4KjWh2RjQMEjp7Ekg3hiQUtFI+NsjKTeR8BUZxoRIEgJwtp9ZEV9hiRsijA2ADBPdIB6ACBPDbMx4R1ahCU4TgT9

ia4OkySBoKi5QSBfj/iIRATNTiAzQEA093j9SH42AupMTcBNBghmFZ0Sh9TzVEYJheRkZSBlApQAAKSbagXgfmX024JkfDAASj1F2IQGUALG1AWHdNwC9N+B9IkwTPjMDJOBDIVPRKyHWIQAuPQ04FvEF0gALgyDDJLFIDUxr3LytJtO4A8K8IbGwCIDT1rMxA4DqRrNIE8LDCECgFAkZFrPTLsAxlYmyBNFbLgDNItNbOtMv2yOlHQ0YDaFFPwH

FIbkSjSCWF7kxB7ygAMBJOwXR0iOnVtPlyfVZByw3IXKXPXwy3ACmh82CGGlbBAFbCAA
```
%%