
# 本周完成事项

1. png问不出答案
2. 日志优化


## png问题

这两个问题都是PNG格式的，显示Embedding完成了， 但是问答都找不到语料

![[image/Pasted image 20240920210711.png]]

本地尝试复现：

![[image/Pasted image 20240920211020.png]]

找到了语料。尝试传入完整参数：

![[image/Pasted image 20240920211205.png]]
发现搜不到了。

可能是目录问题，数据库查找文件目录：

![[image/Pasted image 20240920211450.png]]


发现目录为AF_Q&A_其他，查找传递的目录列表，同样含有AF_Q&A_其他。

难道嵌入的语料中没有目录字段？查找ai搜索数据库：

![[image/Pasted image 20240920211605.png]]

还真是。很多png图片都没有目录字段。但是有的有目录。

怀疑java没有把目录传递过来，查找日志，发现已经截断了。找到的其他png的日志，找到的日志传递了对应的目录，ai搜索中的语料也没有问题。

其他jpg的文件也没有问题。

总结：由于某种原因，导致嵌入时导致少数的png类型的文件嵌入的目录设置成了null. 在ai搜索中把目录改回来就好了。

## 日志优化

日志的输出还是不太清晰，又进行了进一步的优化。加入了日志轮转和多日志输出。