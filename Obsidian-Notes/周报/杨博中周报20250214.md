
### 本周完成事项
---

1. 回顾openai sdk, 为项目中使用做准备
2. 回顾之前程序设计优缺点，设计完善的workflow
3. 学习微软 agent ai system ppt

### 回顾openai sdk
---

复习了非流式调用，流式调用，以及在两种情况下的工具调用方法。新版sdk也做了一些调整。[[1. chat]] [[2. tools]]

### workflow设计
---
搭建了workflow框架，预期可以：

1. 灵活的节点搭建，通过nodes的增加/删除
2. 灵活的输出，使用observer动态获取流程状态
3. 多线程支持

but还需要完善

### ppt相关
---
- multidomain agent比较inspiring
- 没有提到microsoft的框架之类的，更像是一个领域报告+最后提到了一点公司的产品

调研nv的agent产品
调研一下agent领域的东西