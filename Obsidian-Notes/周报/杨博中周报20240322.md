# 本周完成事项

1. 调研虚拟机相关内容，包括各种概念学习，kvm安装，虚拟机安装等

# 虚拟机相关内容

## 概念学习

### KVM, kernal-based virtual machine

融合到linux中的一种虚拟化技术，开源，大多数发行版都支持。

### hyprvisor
A hypervisor is software that creates and runs [virtual machines (VMs)](https://www.redhat.com/en/topics/virtualization/what-is-a-virtual-machine) A hypervisor, sometimes called a virtual machine monitor (VMM), isolates the hypervisor operating system and resources from the virtual machines and enables the creation and management of those VMs.

就是掌管虚拟机的的东西

### 虚拟机的类型

#### Type 1

A type 1 hypervisor, also referred to as a native or bare metal hypervisor(裸金属hypervisor), runs directly on the host’s hardware to manage guest operating systems. It takes the place of a host operating system and VM resources are scheduled directly to the hardware by the hypervisor. 

**也就是说type1这种类型直接就替代操作系统了**

This type of hypervisor is most common in an enterprise **data center** or other **server-based environments**.

**KVM**, Microsoft **Hyper-V**, and VMware **vSphere** are examples of a type 1 hypervisor. KVM was merged into the Linux kernel in 2007, so if you’re using a modern version of [Linux](https://www.redhat.com/en/topics/linux), you already have access to KVM. 



#### Type 2

A type 2 hypervisor is also known as a hosted hypervisor, and is run on a conventional operating system as a software layer or application.

It works by abstracting guest operating systems from the host operating system. VM resources are scheduled against a host operating system, which is then executed against the hardware. 

A type 2 hypervisor is better for individual users who want to run multiple operating systems on a personal computer. 

**VMware Workstation** and Oracle **VirtualBox** are examples of a type 2 hypervisor.

type2就是建立在操作系统上的。其特点有：

1. 底层操作系统的存在会引入不可避免的延迟，因为所有该管理程序的活动和每个VM的工作都必须通过主机操作系统
2. 主机操作系统中的任何安全问题或漏洞都可能会危及在其上运行的所有虚拟机。

因此，Type 2管理程序通常不用于数据中心计算，并且仅用于客户端或最终用户系统(有时称为客户端管理程序)，其中性能和安全性较少受到关注。

性能损失，那type2还是算了。我们所用过的，不论是VMware workstation还是virtualbox，都是type2类型的。不过wsl2使用了hypr-v的一部分功能（官方称之为，使用了一个子集：**The newest version of WSL uses a subset of Hyper-V architecture to enable its virtualization**. This subset is provided as an optional component named "Virtual Machine Platform," available on all Desktop SKUs.）

##### 关于wsl：
The biggest difference between them is that **WSL 1 uses the Windows kernel to implement Linux system calls**, while WSL 2 uses Hyper-V technology, a virtual machine technology.

**Hyper-V 是一个type 1 hypervisor**，当在 Windows 中启用 Hyper-V 时，Windows 系统在硬件底层与 Windows 应用层之间插入了一层 Hyper-V，而原来的 Windows 应用层则变成了一个运行在 Hyper-V 上的虚拟机。

### 麒麟系统

麒麟家族包括中标麒麟（NeoKylin）、银河麒麟（Kylin）、优麒麟（Ubuntu Kylin）和麒麟信安（Kylinsec）。其中中标麒麟和银河麒麟占主要地位。

	值得一提的是，麒麟家族操作系统近期出现新的进展，2019年12月6日，中标软件有限公司(中标麒麟)与天津麒麟信息技术有限公司(银河麒麟)正式整合。 2020年8月13日，银河麒麟V10系统发布，立即引起广泛关注。

### 关于容器和虚拟化

在redhat的官网上，找到很多关于虚拟化等技术的内容。其中，关于容器和虚拟化技术的讨论，他对两种技术的应用做出了如下阐述:

Compared to VMs, containers are best used to: 

- Build cloud-native apps
- Package microservices
- Incorporate applications into DevOps or CI/CD practices
- Move scalable IT projects across a diverse IT footprint 

Compared to containers, VMs are best used to:

- House traditional, legacy, and monolithic workloads
- Isolate risky development cycles
- Provision [infrastructural](https://www.redhat.com/en/topics/cloud-computing/what-is-it-infrastructure) resources (such as networks, servers, and [data](https://www.redhat.com/en/topics/big-data))
- Run a different OS inside another OS (such as running Unix on [Linux](https://www.redhat.com/en/topics/linux))

与虚拟机（VMs）相比，容器最适合用于：

1. 构建云原生应用程序
2. 打包微服务
3. 将应用程序整合到 DevOps 或 CI/CD 实践中
4. 在各种不同的 IT 环境中迁移可扩展的 IT 项目

与容器相比，虚拟机最适合用于：

1. 承载传统、遗留和单块工作负载
2. 隔离风险开发周期
3. 提供基础架构资源（如网络、服务器和数据）
4. 在另一个操作系统内运行不同的操作系统（例如在 Linux 上运行 Unix）


[redhat-container vs virtualization](https://www.redhat.com/en/topics/containers/containers-vs-vms)

### 关于bare metal server

就是裸机的意思，独占一台服务器，而不是虚拟化之类的。之前看到华为云的裸金属服务器不确定是什么东西，应该就是这个了。
## kvm安装

[kvm 安装教程 install-kvm-on-linux](https://sysguides.com/install-kvm-on-linux)

在笔记本上安装了kvm，并尝试安装了~~中标~~麒麟。

- 大约在2007年以后发行的linux都支持kvm(如果硬件支持)
- 中标麒麟(neokylin)已经没了,银河kylin v10取而代之。装新版/旧版？
- 桌面端基于ubuntu，但是不知道哪个版本。服务端基于centos。
- [安装手册等](https://product.kylinos.cn/productCase/159/36)
- 内核版本5.10.0


![[934e8af4bb9e0e0007985dadce899ea.jpg]]

![[7f824b4de4e4281a787aa150799e95f.jpg]]

### 定价查询

由于具体的需求不太清楚，根据之前提到的一台机器上跑7 8台客户机的计划，我们暂定每台客户机配置为4c 8g 50g->最终的host machine为 32c 64g 400g. 其他需求：图形，网络

#### aliyun

术语：

- RDMA: 远程直接内存访问
- RoCE：基于无损以太网的RDMA
- eRDMA：不同于RoCE，是阿里云自己的rdma技术

方案选择：

1. ecs 的大部分实例：本身基于kvm虚拟化，可以多买几个实现多台主机的部署，但是不是所有都支持rdma,并且都不支持roce。关于支持rdma的实例：[# 在企业级实例上配置eRDMA](https://help.aliyun.com/zh/ecs/user-guide/configure-erdma-on-a-cpu-instance#task-2128004) 
2. ecs中的超级计算集群：可以虚拟化，支持RoCE
	-  超级计算集群（Super Computing Cluster，简称SCC）是在弹性裸金属服务器基础上，加入高 速 RDMA 互联支持，大幅提升网络性能，提高大规模集群加速比。因此超级计算集群在提供高带 宽、低延迟的优质网络的同时，还具备所有弹性裸金属服务器的优点
3. 裸金属服务器：可以虚拟化，不支持RoCE，部分支持eRDMA

其他信息：

eRDMA：从ECS第八代实例开始，在全地域和可用区，ECS VM、弹性裸金属、ECI、异构计算等所有算力，都提供了支持。[来源](https://developer.aliyun.com/article/1308339) 

无法使用中标麒麟/麒麟操作系统。

不过暂时没找到符合配置的合适的。

xen, vxworks, vmware, kylin on hypr-v

