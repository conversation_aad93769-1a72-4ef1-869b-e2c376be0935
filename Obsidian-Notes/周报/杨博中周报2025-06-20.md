

本周完成事项

1. 继续完善毕设内容，在graphrag的基础上加入了时空信息的提取, 尝试实现了搜索和加入时空多跳推理
2. 限制提取工作推进

### 时空



- [x] 使用llm提取了时空信息。 把时空信息加入到提取实体中
	- 属性图模型 - [属性图 \| NebulaGraph Academy](https://academic.nebula-graph.io/graph-basics/basic-knowledge/basic-knowledge-graph-property/) 边和点都可以带有属性的模型.目前尝试了实体带有属性,但是并不理想.
	- 关于为什么使用parquet: [[为什么使用parquet]]
![[Pasted image 20250623082658.png]]
![[Pasted image 20250623082257.png]]

由于graphrag没有开放的修改提取图结构的接口, 这部分需要深入到代码中改动. 包括更改内置prompt, schema, 合并逻辑,列定义等.

**后记**- 实际上, 使用属性图感觉并不理想, 因为很多提取到的实体,并不应该附加时间或者空间信息,它本身就是强烈的时空信息的.下一步试试把时空抽离成特殊的节点,然后让其他节点与这个节点关联.

- [x] 测试postgres储存地理时间信息，将提取的parquet存储到本地和postgres中
- [ ] 尝试在输出的图谱数据上进行时空筛选 


### 限制提取

- 完成基本限制提取流程

```bash
➜ barry@archlinux ~/debug/limit-extract
>cd /home/<USER>/debug/limit-extract && python src/limit_extract_workflow.py            
mking log dir
sk-xhhwByc5wb3hqHEyS1Mm3EW6E7nTIJmjGUxUqX1iMqVZlngH https://api.chatanywhere.com.cn/v1 
Running step dispatch_query
Step dispatch_query produced no event
Running step extract_nutrition_table
Step extract_nutrition_table produced event MergeEvent
Running step parse_doc
2025-06-23 10:04:45,383 - proc_file - INFO - File is duplicate, skipping
2025-06-23 10:04:45,383 - proc_file - INFO - Executing function: _on_duplicate
Step parse_doc produced event DocSearchEvent
Running step collect
Step collect produced no event
Running step map_reduce_search
Step map_reduce_search produced event MergeEvent
Running step collect
Step collect produced event ComposeResultEvent
Running step compose_table
Step compose_table produced event StopEvent
Workflow执行完成:
{
  "document_analysis": {
    "results": {
      "铅的限量标准是多少？": "The limit requirement for lead has been adjusted to be stricter than the provision of ≤0.2mg/kg as per GB2762-2022.",
      "微生物指标的要求是什么？": "微生物指标的要求是应符合商业无菌的要求。",
      "产品的保质期是多长时间？": "The product's shelf life is not to exceed 12 months under the specified storage and transportation conditions.",
      "贮存条件有什么要求？": "Products should be stored away from heat sources, at room temperature, protected from light, and not exposed to the outdoors. After opening, they should be stored in the refrigerator or consumed promptly. The storage warehouse should be well-ventilated, clean, and dry (with a relative humidity of ≤ 75%). Products should be stored at least 15cm above the ground and 50cm away from walls. Additionally, products should not be stored together with toxic, harmful, odorous, or corrosive items.",
      "感官要求包括哪些项目？": "容器、色泽、组织状态、气味和滋味、杂质"
    }
  },
  "nutrition_table": {
    "result": {
      "serving_size": 100,
      "serving_unit": "g",
      "items": [
        {
          "name": "能量",
          "amount": 36,
          "unit": "千焦",
          "nrv_percent": 0
        },
        {
          "name": "蛋白质",
          "amount": 0,
          "unit": "g",
          "nrv_percent": 0
        },
        {
          "name": "脂肪",
          "amount": 0,
          "unit": "g",
          "nrv_percent": 0
        },
        {
          "name": "碳水化合物",
          "amount": 2.1,
          "unit": "g",
          "nrv_percent": 1
        },
        {
          "name": "糖",
          "amount": 0,
          "unit": "g",
          "nrv_percent": 0
        }
      ]
    }
  },
  "summary": {
    "doc_search_success": true,
    "nutrition_extraction_success": true,
    "timestamp": 7493.760485414
  }
}
```


### todo

是否已经有成熟的时间/空间索引结构?

寻找真正的创新点 非集成创新