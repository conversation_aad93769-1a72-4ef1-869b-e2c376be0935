# 清单

算法端
系统：Linux (ubuntu 22.04)
硬件：CPU: 32核心64线程 256G内存 卡选型见下面 ssd 1-2T
软件：FastAPI es langchain, llamaindex, paddle ocr（llm框架主要用于处理语料部分。同时也可以尝试其他部署方案?）

后端
系统：Linux (ubuntu 22.04)
硬件：(8 vcpu，64 GiB 内存（redis1G、mysql600MB）、硬盘（mysql20G）)
软件：java8、mysql5.7、redis、blob

问题：
1. 卡如何选择
2. 
# 分析

## 硬件
### python后端需求

- webserver
- 嵌入模型，llm，存储服务的部署

##### 相关资料

[# 为了实现大模型的本地部署，应该怎么配置电脑硬件？](https://www.zhihu.com/question/628771017)
[ollama本地部署llm指南](https://oct.cool/blog/ollama%E6%9C%AC%E5%9C%B0%E5%A4%A7%E6%A8%A1%E5%9E%8B%E8%BF%90%E8%A1%8C%E6%8C%87%E5%8D%97/)

### Webserver

还是fastapi，8vcpu 16GBram 小策同款配置

CPU 4核8线程
内存 16G

### 嵌入模型：

[嵌入模型榜单](https://huggingface.co/spaces/mteb/leaderboard)
#### 榜单是否权威？

应该是比较权威的。因为：

```
1. google 搜索embedding benchmark后，多数结果指向mteb
2. huggingface上4.5k like
3. 部分博客称之为 公认为是目前业界最全面、最权威的中文语义向量评测基准`C-MTEB`
```

拿榜单上前两个模型考虑

```
模型参数量：326M（百万参数）
内存使用：1.21GB（FP32精度）
嵌入维度：1792
最大token：512
```

CPU 4核8线程
内存 4G
GPU 2G显存

### LLM 本地部署

[llm相关领域排行榜，包括llm以及刚才提到的mteb等榜单](https://huggingface.co/collections/open-llm-leaderboard/the-big-benchmarks-collection-64faca6335a7fc7d4ffe974a)

- 根据openllm 榜单，qwen的效果比较好。一眼望去全都是qwen或者其微调版本

根据[chatbot arena](https://huggingface.co/spaces/lmarena-ai/chatbot-arena-leaderboard)榜单， 表现较好的模型包括：

| model          | parameter size |
| -------------- | -------------- |
| kdeepseek v2.5 | 236B           |
| llama3.1-405B  | 405B           |
| llama3.3-70B   | 70B            |
| Qwen2.5 72B    | 72B            |
| deepseek v3    | 671B           |
#### 部署成本

暂时只考虑部署一次的情况，不考虑部署到多个机器上。

显存占用约为参数量\*量化位数/8 比如一个72B模型，int8量化占用 72\*88=72G ，实际上约为70-80G。我们按照需要90算。

待选卡有

| 型号                | 价格                 | 备注                              |
| ----------------- | ------------------ | ------------------------------- |
| rtx a6000 48GB    | 没包装3 有包装3.8        | 速度不如a6000 ada快，cuda核心少          |
| rtx a6000ada 48GB | 简装5.3 盒装5.6        | 没有nvlink，部署可能稍微难一点              |
| rtx 4090 24GB*4   | 4090 1.8 4090D 1.5 | 功率大(a6000 ada两倍) 消费级 性能和a6000接近 |
| L40/L40s          | 6左右                | 类似a6000ada                      |
| a100              | 15                 |                                 |
| h100              | 22                 |                                 |

```
在价格方面，RTX A6000Ada的价格比RTX A6000贵了40%多，性能提升了2倍左右，如果想要大容量显存进行训练模型的工作者可以购买两块RTX A6000搭配NVLink可以获得性能比单块RTX A6000Ada更强。
```

```
如果我企业有100人，我会配20个RTX A6000或者10个A100 80G吧。自己用就上RTX 6000 Ada或H100。自己用和公司用还是不一样的，越便宜越有利于规模化。
```

```
另外RTX 6000 Ada没有NVLink，但是通信可以走P2P。在服务器端，L40s可取代[RTX 6000 Ada](https://zhida.zhihu.com/search?content_id=628696896&content_type=Answer&match_order=5&q=RTX+6000+Ada&zhida_source=entity)。L40s是被动散热，而RTX 6000 Ada相对安静，因此后者用在工作站中比较合适。
```

最终gpu部分方案

| 方案         | 备注        |
| ---------- | --------- |
| 2*a6000ada | 快 但贵      |
| 2*l40      | 快 但贵      |
| 4*4090     | 不贵 快 但功率大 |
| 4*a6000    | 不贵 功率小 但慢 |


cpu 16核 32线程 (可降级)
内存 128-256GB
显卡 2*a6000ada 2\*a6000  或者4\*4090
ssd  256G

### 存储

es做文档和向量数据库 elastic search
**postgresql**

CPU: 6核 12线程
内存: 16GB
存储: 1T SSD

### 最终选型

CPU 4+4+16+6 = 30 核心
内存 256G 
GPU 96G显存，型号不定
ssd 1-2T





