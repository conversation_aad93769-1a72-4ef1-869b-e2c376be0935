**本周完成事项**

1. graphrag源码学习
2. 学习langgraph, llamaindex, fastapi等

### graphrag源码

这周主要工作是阅读了graphrag的源码

![[graphrag程序设计架构 | 700]]

有几个很关键的地方。

1. graphrag源码组织方式是一个package，这是一种很常见，也很容易犯错的组织方式。程序的入口在`graphrag/__main__.py` 。包之间按理说可以使用相对引用，but它使用了绝对引用,问题也不大。
2. graphrag代码经历了多版重构。在现在的代码中，用自己实现的功能替代了原来的datashaper框架，用typer代替了argparse。typer是fastapi团队的命令行库，用了类似fastapi的方式实现了一个命令行交互框架。
3. graphrag在现在的版本里引入了论文中没有的global local drift 还有最普通的naive rag方法。


### 限值提取  langgraph llamaindex 等

考虑到要做sgs的限制提取的项目，我觉得最好用业界比较成熟的workflow框架。目前暂时学习了langgraph框架的使用，但是这个框架说实话，写起来很奇怪，不太好写。此外,又学习了llamaindex的写法, 暂时感觉llamaindex比较好用. 
