# 本周完成事项

1. 修改建议书，完善了技术细节，去掉了文献部分
2. 学习基于http的spaql查询，修改数据。调研推理规则修改的方法
3. 在2基础上做了一个简单的客户端小程序，模拟实时上传数据，执行任务等

# 数据库增删改查

## 查询 python实现

上周已经总结过使用select查询，这里贴一个编程实现查询的效果:

```bash
PS C:\Users\<USER>\debug\spaql> & C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/python3.11.exe c:/Users/<USER>/debug/spaql/main.py
{'head': {'vars': ['com', 'movie']}, 'results': {'bindings': [{'com': {'type': 'uri', 'value': 'http://www.semanticweb.org/vv/ontologies/2024/3/unmanned_system#打击林肯号'}, 'movie': {'type': 'uri', 'value': 'http://www.semanticweb.org/vv/ontologies/2024/3/unmanned_system#打击任务'}}, {'com': {'type': 'uri', 'value': 'http://www.semanticweb.org/vv/ontologies/2024/3/unmanned_system#打击林肯号'}, 'movie': {'type': 'uri', 'value': 'http://www.w3.org/2002/07/owl#NamedIndividual'}}, {'com': {'type': 
......(omitted content)
```


## 修改&添加：Update语句

SPARQL 1.1 Update提供了一种强大的方式来执行数据的添加和修改。可以使用如下命令：

- **添加数据：**
    
    `PREFIX ex: <http://example.org/>  INSERT DATA {     ex:John ex:hasFriend ex:Jane . }`
    
    添加一条表示John和Jane是朋友的关系。
    
- **修改数据：**
    
    `PREFIX ex: <http://example.org/>  DELETE { ex:John ex:hasFriend ex:Jane . } INSERT { ex:John ex:hasFriend ex:Mike . } WHERE { ex:John ex:hasFriend ex:Jane . }`
    修改John的朋友从Jane变为Mike。
    
#### HTTP API

通过HTTP API上传数据，支持多种数据格式，如Turtle和RDF/XML。使用如下HTTP POST命令可以向指定数据集添加数据：


`curl -X POST --data-binary "@data.ttl" -H "Content-Type: text/turtle" http://localhost:3030/dataset/data`

#### Jena API

在Java程序中使用Jena API操作数据，可以构建和修改RDF模型，并通过编程方式将变更发送到服务器。但是由于我java没接触过，就没有再仔细研究。

## 删除 DELETE语句

如果你知道确切的三元组（主题、谓词、宾语）并想将其从数据中删除，你可以直接使用`DELETE DATA`语句。例如，要删除John和Jane之间的朋友关系，你可以写：



`PREFIX ex: <http://example.org/>  
DELETE DATA {     ex:John ex:hasFriend ex:Jane . }`

# 实时修改规则


```
在Apache Jena Fuseki中，推理规则通常在数据集的配置阶段被设置，并且在数据集运行时应用。这意味着一旦数据集启动，其使用的推理规则通常不能动态地更改。这是因为推理引擎需要在启动时加载并准备规则，以便在处理查询和数据操作时提供高效的性能。
```

不过可以通过以下几个达到更新类似的效果：**重新启动数据集** **周期性更新** **手动编程推理** 