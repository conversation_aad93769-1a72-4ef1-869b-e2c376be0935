我们这里使用ollama部署，并直接使用ollama提供的接口

## ollama 部署

1. 去[ollama官网](https://ollama.com/)下载ollama。v100服务器上已经下载过，就不再重复

![[Pasted image 20250207111457.png]]

2. 官网寻找r1以及qwen模型。这里以r1为例：

![[Pasted image 20250207111916.png]]

![[Pasted image 20250207112117.png]]

终端执行，等待下载完成

![[Pasted image 20250207112230.png]]

下载完成后会进入命令行对话模式，可以对话测试一下。

![[Pasted image 20250207115502.png]]

部署到此就结束了。qwen2.5同理。

## 本地调用

可以参考这个[调用教程](https://www.53ai.com/news/qianyanjishu/737.html)

这里使用openai的python sdk，测试代码如下：

非流式输出：

```python
from openai import OpenAI

client = OpenAI(
        base_url = 'http://localhost:11434/v1',
        api_key = 'ollama',
        )

messages =[
        {"role":"user","content":"hi"}
        ]

response = client.chat.completions.create(
        messages = messages,
        model = "deepseek-r1:32b"
        )

print(response.choices[0].message.content)
```

```
python ollama_request.py    
<think>  
  
</think>  
  
Hello! How can I assist you today? 😊
```

流式输出：

```python
from openai import OpenAI  
  
client = OpenAI(  
       base_url = 'http://localhost:11434/v1',  
       api_key = 'ollama'  
       )  
  
messages = [  
       {"role":"user","content":"hi"}  
       ]  
  
stream = client.chat.completions.create(  
       stream = True,  
       model = "deepseek-r1:32b",  
       messages = messages  
       )  
  
for chunk in stream:  
   if chunk.choices[0].delta.content != None:  
       print(chunk.choices[0].delta.content,end='')
```

```
<think>

</think>

Hello! How can I assist you today? 😊
```

