
本周工作主要是写了限值提取的代码,目前进度如下

- [x] llamaindex框架 限值提取工作流
	- [ ] 搜索map/reduce
	- [ ] 成分表提取
	- [ ] 生成表格
- [x] rag流程
	- [x] 框架设计, 重构rag关键组件
		- [x] proc_file
			- [x] ocr- mineru
			- [x] uploadfile
			- [x] hash related
			- [x] 文件重复性检验
			- [x] 向量存储
		- [x] search
		- [x] 新增文件/删除文件 测试
- [x] 日志
	- [x] logging模块学习
	- [x] 设计完善的追踪方案
- [ ] 后端

### 日志部分

这周重点学习了一下日志部分用法.[[logging]] 目前设计了几个日志模块, 包括追踪函数调用, 以及侵入式输出. 不过没有写完这部分.

