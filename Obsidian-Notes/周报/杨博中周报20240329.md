

xen, kvm, vmware exsi, hyper-v，openvz


在xen上装系统
在kvm上装系统
在esxi上装系统
调查esxi价格
调查硬件价格

# 本周完成事项

1.本周还是尝试了几个虚拟机平台，学习了创建，配置等操作
2.调研了一下服务器配置，无损以太网相关配件的价格，但是暂时还没有明确的价格

在xen上装系统
在kvm上装系统
在esxi上装系统
调查esxi套件价格
调查硬件价格

## 虚拟机安装

### 目标平台

vmware esxi, xen, kvm, 
### 测试系统

由于虚拟机安装的时候也需要指定目标操作系统，因此需要调查kylin系列是什么内核，基于什么做的。

- kylin v10 desktop (2020) (基于ubuntu 20.04 lts)
- kylin v10 server(2023.1 [source](https://news.tom.com/202301/4010242318.html) )(基于 centos)
- Neokylin v7(基于RHEL 7.3, 2016)
- Neokylin v7 server (官网已不再提供下载)
- VxWorks

安装结果：

### kvm:

- kylin v10 desktop      ✅
- kylin v10 server         ✅
- Neokylin v7               ✅
- vxworks                     x (要先安裝msdos，但是kvm上安裝dos一直失敗，需要额外研究)

kylin v10和 neokylin v7的风格很相似，几乎就是迭代版

### xen:
装xen之后系统出了问题，重装了一次系统。之後系統如果伴隨xen啓動也經常崩潰...可能kvm和xen共存問題, 後續還需要再調研測試。

### vmware esxi

~~esxi还需要额外再分区，电脑内存不太充裕，没有再试验。回头重新规划一下分区再试试~~

#### 术语

- esxi：vmware的type1 虚拟化技术
- vSphere 套件：包括esxi，除此之外还有vCenter Server、vSphere Client 等
- vSAN: 常常和vSphere一起用的虚拟存储技术

- 使用嵌套虚拟化，把esxi装在了vmware workstation上：
- 创建虚拟机esxi空间不够，需要添加硬盘，操作教程：[链接](https://zhuanlan.zhihu.com/p/484961316) 

- kylin v10 desktop      ✅
- kylin v10 server         ✅
- Neokylin v7               ✅
- vxworks                    --- 理論可行，因为李凯在workstation上成功了，但是esxi上还没尝试。

![[Pasted image 20240401000912.png]]


 ### 关于esxi：
VMware ESXi 的价格取决于许可证类型和 CPU 数量。

### 确定所需的订阅容量

**最终价格还是没确定, vmware的定价不透明，不在官网直接告诉你，并且它也有很多其他合作商，也会提供vmware的产品。**

vmware被博通收购后，于去年年底不再提供永久授权，改为订阅制度。订阅可订阅一年或三年。esxi提供在
需要和vSAN結合使用

vSphere+ 订阅容量基于与计划订阅 vSphere+ 的 vCenter Server 实例关联的所有 ESXi 主机上每个 CPU 的物理 CPU 内核总数。同样，vSAN+ 订阅容量基于与 vCenter Server 管理的 vSAN 集群关联的所有 ESXi 主机上每个 CPU 的物理 CPU 内核总数。

购买的最小容量必须为每个 CPU 16 个内核。

订阅容量 = 每个 CPU 所需的内核数 × 每个 ESXi 主机的 CPU 数 × ESXi 主机数
4c 8g 100g
32c 64g

32c x 1 x  2 =64
16c x 2 x  2 =64

# 物理机配置

**暂时还是没有最终报价，要看的东西也挺多的**

考虑以下几个因素：

1. cpu配置：4c8g x8 x2 -> 32c 64g x2
2. 支持无损以太网
	1. 只要有无损以太网网卡就可以
	2. 以 [哪些英特尔® 以太网网络适配器支持iWARP 和RoCE v2？](https://www.intel.cn/content/www/cn/zh/support/articles/000031905/ethernet-products/700-series-controllers-up-to-40gbe.html) 中提到的网卡为例，一块无损以太网网卡价格从400\$ ~ 600\$ ，nvidia的价格从\$200（旧设备）到\$1000不等。各种博客中提到nvidia Mellanox 的设备比较多，intel很少
3. 无损以太网交换机
	1. 不一定需要。首先是两台设备的连接不一定使用交换机，其次[也可以使用普通的以太网交换机](https://www.sdnlab.com/25923.html) 

问题：

旧设备？
服务器？

仅供参考，来自google gemini给出的建议：

**无损以太网交换机**：无损以太网交换机是无损以太网协议的核心部件，负责转发无损以太网数据包。无损以太网交换机的价格取决于端口数量、性能和品牌等因素。一般来说，一个 10GbE 端口的无损以太网交换机价格约为 1000 美元，一个 40GbE 端口的无损以太网交换机价格约为 4000 美元。。
  
**服务器**：服务器用于运行虚拟机。服务器的价格取决于 CPU、内存、存储和品牌等因素。一般来说，一台能够运行 8 个虚拟机的服务器价格约为 5000 美元。

**虚拟机**：虚拟机是运行在服务器上的软件环境。虚拟机软件的成本通常按年订阅计算。例如，VMware vSphere 的年订阅费用约为 1000 美元。
	  

东西特别多, 罗列一下方向

HPE ProLiant DL380 Gen10 Plus
浪潮 NF5280M5
戴尔 PowerEdge 系列

# 附录

博客[业界虚拟化技术分析](https://www.cnblogs.com/xiaoyuxixi/p/11277063.html) 中提到：

|         |         |     |         |             |            |                                                      |
| ------- | ------- | --- | ------- | ----------- | ---------- | ---------------------------------------------------- |
| 技术      | 运行平台    | 开源  | 需要cpu支持 | 虚拟化性能损耗     | 支持系统情况     | 风险点与优劣                                               |
| Kvm     | Linux   | 是   | 是       | 略有损耗        | 都支持        | 老牌的虚拟化技术，开源稳定，虚拟windows有定制镜像，且单机最多虚拟出4台机器。多了会崩溃      |
| Xen     | Linux   | 是   | 是       | 略有损耗，但低于kvm | 都支持        | 操作复杂，难以上手，但性能比较稳定，局域伸缩性强                             |
| Hyper-V | Windows | 否   | 是       | 略有损耗        | 对linux支持较差 | Microsoft的产品，肯定对windows支持较好，但对linux很不友好，对windows支持较好 |
| OpenVZ  | Linux   | 是   | 是       | 损耗极小，且低于xen | 都支持        | 虚拟化性能较好，但内核故障可能会导致虚拟机全部故障，不如xen稳定，且可以资源超卖            |
| Vmware  | Esxi    | 否   | 是       | 略有损耗        | 都支持        | 相对较易操作，但对硬件有限制，cpu只支持到12核                            |
