## 本周完成事项

- chatbot代码修改，部署到小策
- 分析测试限值提取流程关键步骤
- 测试限值提取相关方法
- 写文档

## 部署到小策

linux迁移到windows，更改对应代码。可优化

- 考虑是否需要统一化代码，现在的代码和sgs还是有点耦合。写个配置文件之类的。之前写过一次，效果不太好

## 测试限值提取

#### 项目框架搭建

- 想试试还是用之前的workflow的设计方式，组织了项目结构
- 试着用了比较先进的Dependency Management Tool ---- Poetry，预防chatbot上的一些问题

#### 测试分类能力

使用python脚本，批量自动化测试表格转换，图片识别能力。

- 将样本结构化处理成程序可以批量操作的数据

尝试写脚本的时候，出现了以下几个问题，整理如下：[[预处理-表格待对齐内容]] ，需要开会讨论一下

### 写文档

和泓宇石恒一起写了文档。