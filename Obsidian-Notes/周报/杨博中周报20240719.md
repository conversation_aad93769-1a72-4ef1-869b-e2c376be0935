# 本周完成事项

1. sgs项目功能添加
2. ocr相关内容
3. python技能学习

## 1. sgs

本周sgs工作内容如下：

![[Pasted image 20240719214134.png]]


## 2. ocr相关

调研了一些ocr相关技术，这里就把日报贴出来。主要的收获就是发现qwen的ocr效果很好

-----


# DocTR

![[Pasted image 20240717221431.png]]

![[Pasted image 20240717221625.png]]

优点
1.大于号小于号的区域的检测还可以
2.有很多模型的选择

选择
1.不支持中文，
2.小于等于识别成小于

-----

ocr项目进度：

1. 继续调研DocTR识别效果
2. 调研qwen ocr效果

# DocTR

doctr今天保持检测模型不变（resnet50），尝试了所有的识别模型。结论：

优点：
有的可以识别孤立的小于号

缺点：
都不支持中文
上下标支持也不好

crnn_mobilenet v3 small
![[Pasted image 20240718210519.png]]
crnn mobilenet_v3 large
![[Pasted image 20240718210536.png]]
master
![[Pasted image 20240718210549.png]]
sar resnet31
![[Pasted image 20240718210601.png]]
vitstr_small
![[Pasted image 20240718210609.png]]
vitstr base
![[Pasted image 20240718211508.png]]
parseq
![[Pasted image 20240718210620.png]]

# Qwen-VL-Max

qwen模型的效果特别好，是目前见过最好的ocr效果。但是他不是直接的ocr模型，把图片送给他之后还需要问他然后输出识别的内容

优点

1. 效果好，上下标不仅识别准确，还自动加上了^
2. 小于等于号识别的也可以

后续还需要继续调研。和Qwen的聊天记录如下：

![](https://qwen-qwen-vl-max.hf.space/--replicas/kob4z/file=/tmp/gradio/0eff5369a70fc93c8fc8137f8b64732e790f2546/%E5%B1%8F%E5%B9%95%E6%88%AA%E5%9B%BE%202024-07-17%20220428.png)

识别图中所有文字

图片中的文字内容如下：

---

表格 OCR POC

1.单元格合并的情况：

表3 微生物限量与试验方法

项 目 采样方案a及限量 试验方法 n c m M  
菌落总数b/（CFU/g） 5 2 10^4 10^5 GB 4789.2  
大肠菌群b/（CFU/g） 5 2 10 10^2 GB 4789.3 平板计数法  
霉菌c/（CFU/g） ≤ 150 GB 4789.15  
沙门氏菌 5 0 0/25g —— GB 4789.4  
金黄色葡萄球菌/（CFU/g） 5 1 100 1000 GB 4789.10第二法  
a样品的采样及处理按GB 4789.1执行。  
b不适用于含未成熟的发酵配料或新鲜水果蔬菜的产品。  
c不适用于添加霉菌成熟干酪的产品。

2.表格被切割在不同页上

表 2 理化指标与试验方法

项 目 蛋类制品 谷物制品 肉制 品 水产 制品 果蔬 蔬菜 食用菌 复合调味料 制品 试验方法  
铅(以Pb)计，mg/kg ≤ 0.1 0.1 0.4 0.9 0.9 0.9 0.9 GB 5009.12  
镉(以Cd)计，mg/kg ≤ 0.05 0.2* 0.1* 0.2* - 0.5d - GB 5009.15  
甲基汞(以Hg)计)，mg/kg ≤ - - - 0.5* - - - GB 5009.17  
总汞(以Hg)计)，mg/kg ≤ - 0.02* - - 0.1 - - GB 5009.17  
无机砷(以As)计)，mg/kg ≤ - 0.2* - 0.5d - - - GB 5009.11  
总砷(以As)计)，mg/kg ≤ - - 0.5 - - - 0.5 0.5* GB 5009.11  
铬(以Cr)计)，mg/kg ≤ - 1.0* 1.0 2.0 - - - GB 5009.123  
展青霉素，μ/g/kg ≤ - - - - 50^ - - - GB 5009.185  
苯并[a]芘，μ/g/kg ≤ - 5.0* 5.0* 5.0* - - - GB 5009.27  
N-二甲基亚硝胺，μ/g/kg ≤ - - 3.0* 4.0* - - - GB 5009.26  
多氯联苯，mg/kg ≤ - - - - 0.5 - - - GB 5009.190  
*指标配调料中除水以外最大添加原料种类的对应要求执行  
^以糯米为主要原料。  
^仅限肉制品（肝脏制品、肾脏制品除外）。  
^仅限鱼类罐头（凤尾鱼、旗鱼罐头除外）。  
^仅限食用菌制品（姬松茸制品罐头除外）。

以markdown形式输出，最好能还原表格的内容

由于OCR的结果并不完全准确，部分文字可能有误。根据提供的信息，我将尝试还原两个表格的内容。

**表3 微生物限量与试验方法**

| 项目              | 采样方案a及限量 | 试验方法 |
| --------------- | -------- | ---- |
| n               | c        | m    |
| m               | M        |      |
| 菌落总数b/(CFU/g)   | 5        | 2    |
| 大肠菌群b/(CFU/g)   | 5        | 2    |
| 霉菌c/(CFU/g)     | ≤        | 150  |
| 沙门氏菌            | 5        | 0    |
| 金黄色葡萄球菌/(CFU/g) | 5        | 1    |

**表 2 理化指标与试验方法**

| 项目                | 蛋类制品 | 谷物制品 | 肉制 品  | 水产 制品 | 果蔬 蔬菜 | 食用菌 | 复合调味料 | 制品         | 试验方法        |
| ----------------- | ---- | ---- | ----- | ----- | ----- | --- | ----- | ---------- | ----------- |
| 铅(以Pb)计, mg/kg    | ≤    | 0.1  | 0.1   | 0.4   | 0.9   | 0.9 | 0.9   | GB 5009.12 |             |
| 镉(以Cd)计, mg/kg    | ≤    | 0.05 | 0.2*  | 0.1*  | 0.2*  | -   | 0.5d  | -          | GB 5009.15  |
| 甲基汞(以Hg)计), mg/kg | ≤    | -    | -     | -     | 0.5*  | -   | -     | -          | GB 5009.17  |
| 总汞(以Hg)计), mg/kg  | ≤    | -    | 0.02* | -     | -     | 0.1 | -     | -          | GB 5009.17  |
| 无机砷(以As)计), mg/kg | ≤    | -    | 0.2*  | -     | 0.5d  | -   | -     | -          | GB 5009.11  |
| 总砷(以As)计), mg/kg  | ≤    | -    | -     | -     | 0.1   | -   | -     | -          | GB 5009.11  |
| 铬(以Cr)计), mg/kg   | ≤    | -    | 1.0*  | 1.0   | 2.0   | -   | -     | -          | GB 5009.123 |
| 展青霉素，μ/g/kg       | ≤    | -    | -     | -     | -     | 50^ | -     | -          | GB 5009.185 |
| 苯并[a]芘，μ/g/kg     | ≤    | -    | -     | -     | -     | -   | -     | -          | GB 5009.27  |
| N-二甲基亚硝胺，μ/g/kg   | ≤    | -    | -     | -     | -     | -   | -     | -          | GB 5009.26  |
| 多氯联苯，mg/kg        | ≤    | -    | -     | -     | -     | -   | -     | -          | GB 5009.190 |

注意：表格中的数据可能存在一些错误和不完整的地方，请谨慎使用这些信息。

-----

## python知识学习

学习了python相关的知识，包括pipx，先进的poetry包管理器等。