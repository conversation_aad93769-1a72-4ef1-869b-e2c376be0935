1. 搭建限值提取框架，测试效果。
2. 调研llm方面技术，为开题作准备

## 限值提取

在workflow框架基础上，完成了：

- rapid ocr 模块的编写，包括paddle和rapid
- 搭建之前的rag workflow.这个部分稍微复杂一点,不过还差一点。


这周尝试调节一下结构,测试一下

## llm方面

调研了一下rag,agent方面感兴趣的问题，包括场话题保持，能力评估等.但是方向暂时不明确

[How well can your RAG agent carry out a conversation? - IBM Research](https://research.ibm.com/blog/conversational-RAG-benchmark)****
[\[2501.03468\] MTRAG: A Multi-Turn Conversational Benchmark for Evaluating Retrieval-Augmented Generation Systems](https://arxiv.org/abs/2501.03468)
[Paper page - CORAL: Benchmarking Multi-turn Conversational Retrieval-Augmentation Generation](https://huggingface.co/papers/2410.23090)
