
### 为什么fastapi这边的并发不理想？

事后总结

1. 个别请求总是超时
	1. 最终原因：就是会有超时的
2. 当并发数量大的时候，同时处理的请求数并不高
	1. 相对于ab对服务端请求，请求fastapi的性能变差

### 1. 测试fastapi本身能力

一是找错，二是摸底

测试方式：

- 开16个workers(不论是线程池还是异步 都能最大化利用cpu)
- 使用apache bench测试，逐步增加并发数
- 先用sleep代替请求

~~fastapi为什么并发数好像和网上说的40对不上？~~直接改成200，网上说和tomcat一样

试试命令行启动   x   提升不大
试试wsgi           x      提升不大
使用异步睡眠2s模拟io操作 -> 并发很健康 1000个同时处理（2.0-2.2s 2.1-2.3 2.1-2.3）
使用同步睡眠2s模拟io操作->也很健康，1000个同时处理（2.3-2.5s 2.1-2.2 2.1-2.2）
由此可以判断，**fastapi框架本身应该是问题不大的** 

#### 测试国内其他家的

我去，也有延迟...但是确实好一些。并且...fastapi这边还是慢

#### 直接测试azure的端点

直接测试azure的端点：

- 非常简单的问答
- 200个请求

数据记录方式：总时间(s)/50%(ms)/90%(ms)/100%(ms)

200个请求

##### 第一天

|         | fastapi接口                                                          | azure接口                                                           |
| ------- | ------------------------------------------------------------------ | ----------------------------------------------------------------- |
| 4o-mini | 17/2332/7333/15629<br>8.5/1990/7014/7209<br>8.5/1722/4013/7271<br> | 2.910/1181/1575/1818                                              |
| 4o      |                                                                    | 6.1/1716/2357/5031<br>23.0/1647/2141/21927<br>7.5/1348//2381/6357 |

###### 第一天的记录

4o-mini:

```
Server Software:        
Server Hostname:        af-koreacentral-aoai.openai.azure.com
Server Port:            443
SSL/TLS Protocol:       TLSv1.3,TLS_AES_256_GCM_SHA384,2048,256
Server Temp Key:        ECDH secp384r1 384 bits
TLS Server Name:        af-koreacentral-aoai.openai.azure.com

Document Path:          /openai/deployments/4o-mini/chat/completions?api-version=2024-10-21
Document Length:        1010 bytes

Concurrency Level:      200
Time taken for tests:   2.910 seconds
Complete requests:      200
Failed requests:        0
Keep-Alive requests:    200
Total transferred:      334000 bytes
Total body sent:        107000
HTML transferred:       202000 bytes
Requests per second:    68.74 [#/sec] (mean)
Time per request:       2909.615 [ms] (mean)
Time per request:       14.548 [ms] (mean, across all concurrent requests)
Transfer rate:          112.10 [Kbytes/sec] received
                        35.91 kb/s sent
                        148.01 kb/s total

Connection Times (ms)
              min  mean[+/-sd] median   max
Connect:        0  505  84.6    503     643
Processing:   588  720 152.6    662    1278
Waiting:      588  719 152.7    662    1278
Total:        876 1225 191.3   1181    1818

Percentage of the requests served within a certain time (ms)
  50%   1181
  66%   1238
  75%   1265
  80%   1288
  90%   1575
  95%   1704
  98%   1754
  99%   1802
 100%   1818 (longest request)
```

4o:

```
Server Software:        
Server Hostname:        af-koreacentral-aoai.openai.azure.com
Server Port:            443
SSL/TLS Protocol:       TLSv1.3,TLS_AES_256_GCM_SHA384,2048,256
Server Temp Key:        ECDH secp384r1 384 bits
TLS Server Name:        af-koreacentral-aoai.openai.azure.com

Document Path:          /openai/deployments/AF-gpt4o/chat/completions?api-version=2024-10-21
Document Length:        1016 bytes

Concurrency Level:      200
Time taken for tests:   6.104 seconds
Complete requests:      200
Failed requests:        7
   (Connect: 0, Receive: 0, Length: 7, Exceptions: 0)
Keep-Alive requests:    200
Total transferred:      334837 bytes
Total body sent:        107200
HTML transferred:       203222 bytes
Requests per second:    32.77 [#/sec] (mean)
Time per request:       6103.676 [ms] (mean)
Time per request:       30.518 [ms] (mean, across all concurrent requests)
Transfer rate:          53.57 [Kbytes/sec] received
                        17.15 kb/s sent
                        70.72 kb/s total

Connection Times (ms)
              min  mean[+/-sd] median   max
Connect:        0  536 102.4    541     692
Processing:   635 1282 536.8   1133    4470
Waiting:      635 1281 537.0   1133    4470
Total:        681 1818 567.9   1716    5031

Percentage of the requests served within a certain time (ms)
  50%   1716
  66%   1802
  75%   1883
  80%   2049
  90%   2357
  95%   2716
  98%   3410
  99%   5003
 100%   5031 (longest request)





第二次
Server Software:        
Server Hostname:        af-koreacentral-aoai.openai.azure.com
Server Port:            443
SSL/TLS Protocol:       TLSv1.3,TLS_AES_256_GCM_SHA384,2048,256
Server Temp Key:        ECDH secp384r1 384 bits
TLS Server Name:        af-koreacentral-aoai.openai.azure.com

Document Path:          /openai/deployments/AF-gpt4o/chat/completions?api-version=2024-10-21
Document Length:        1016 bytes

Concurrency Level:      200
Time taken for tests:   22.994 seconds
Complete requests:      200
Failed requests:        2
   (Connect: 0, Receive: 0, Length: 2, Exceptions: 0)
Keep-Alive requests:    200
Total transferred:      334818 bytes
Total body sent:        107200
HTML transferred:       203208 bytes
Requests per second:    8.70 [#/sec] (mean)
Time per request:       22993.922 [ms] (mean)
Time per request:       114.970 [ms] (mean, across all concurrent requests)
Transfer rate:          14.22 [Kbytes/sec] received
                        4.55 kb/s sent
                        18.77 kb/s total

Connection Times (ms)
              min  mean[+/-sd] median   max
Connect:        0  520  91.1    519     680
Processing:   623 1393 2122.2   1114   21301
Waiting:      623 1393 2122.2   1114   21301
Total:        718 1913 2130.2   1647   21927

Percentage of the requests served within a certain time (ms)
  50%   1647
  66%   1753
  75%   1818
  80%   1881
  90%   2141
  95%   2320
  98%   6715
  99%  16689
 100%  21927 (longest request)

第三次
Server Software:        
Server Hostname:        af-koreacentral-aoai.openai.azure.com
Server Port:            443
SSL/TLS Protocol:       TLSv1.3,TLS_AES_256_GCM_SHA384,2048,256
Server Temp Key:        ECDH secp384r1 384 bits
TLS Server Name:        af-koreacentral-aoai.openai.azure.com

Document Path:          /openai/deployments/AF-gpt4o/chat/completions?api-version=2024-10-21
Document Length:        1016 bytes

Concurrency Level:      200
Time taken for tests:   7.511 seconds
Complete requests:      200
Failed requests:        6
   (Connect: 0, Receive: 0, Length: 6, Exceptions: 0)
Keep-Alive requests:    200
Total transferred:      334839 bytes
Total body sent:        107200
HTML transferred:       203218 bytes
Requests per second:    26.63 [#/sec] (mean)
Time per request:       7511.289 [ms] (mean)
Time per request:       37.556 [ms] (mean, across all concurrent requests)
Transfer rate:          43.53 [Kbytes/sec] received
                        13.94 kb/s sent
                        57.47 kb/s total

Connection Times (ms)
              min  mean[+/-sd] median   max
Connect:        0  527  91.5    530     703
Processing:   622 1062 706.6    807    5822
Waiting:      622 1061 706.6    807    5822
Total:       1015 1589 716.1   1348    6357

Percentage of the requests served within a certain time (ms)
  50%   1348
  66%   1573
  75%   1729
  80%   1788
  90%   2381
  95%   2737
  98%   4548
  99%   5443
 100%   6357 (longest request)
```


fastapi 异步
```
Server Software:        uvicorn
Server Hostname:        localhost
Server Port:            8000

Document Path:          /chatHTTP
Document Length:        1069 bytes

Concurrency Level:      200
Time taken for tests:   17.071 seconds
Complete requests:      200
Failed requests:        196
   (Connect: 0, Receive: 0, Length: 196, Exceptions: 0)
Total transferred:      254886 bytes
Total body sent:        59200
HTML transferred:       225686 bytes
Requests per second:    11.72 [#/sec] (mean)
Time per request:       17071.300 [ms] (mean)
Time per request:       85.356 [ms] (mean, across all concurrent requests)
Transfer rate:          14.58 [Kbytes/sec] received
                        3.39 kb/s sent
                        17.97 kb/s total

Connection Times (ms)
              min  mean[+/-sd] median   max
Connect:        0    9   3.3      8      16
Processing:  1017 4356 3220.6   2322   15622
Waiting:     1016 4355 3220.6   2320   15622
Total:       1032 4365 3220.0   2332   15629

Percentage of the requests served within a certain time (ms)
  50%   2332
  66%   6868
  75%   6997
  80%   7106
  90%   7333
  95%  11347
  98%  14226
  99%  14747
 100%  15629 (longest request)

Server Software:        uvicorn
Server Hostname:        localhost
Server Port:            8000

Document Path:          /chatHTTP
Document Length:        1135 bytes

Concurrency Level:      200
Time taken for tests:   8.519 seconds
Complete requests:      200
Failed requests:        192
   (Connect: 0, Receive: 0, Length: 192, Exceptions: 0)
Total transferred:      255849 bytes
Total body sent:        59200
HTML transferred:       226649 bytes
Requests per second:    23.48 [#/sec] (mean)
Time per request:       8518.884 [ms] (mean)
Time per request:       42.594 [ms] (mean, across all concurrent requests)
Transfer rate:          29.33 [Kbytes/sec] received
                        6.79 kb/s sent
                        36.12 kb/s total

Connection Times (ms)
              min  mean[+/-sd] median   max
Connect:        0   10   5.2      9      21
Processing:  1180 2517 1640.6   1970    7204
Waiting:     1179 2516 1640.8   1968    7203
Total:       1197 2526 1640.5   1990    7209

Percentage of the requests served within a certain time (ms)
  50%   1990
  66%   2156
  75%   2319
  80%   2736
  90%   6776
  95%   7014
  98%   7147
  99%   7196
 100%   7209 (longest request)



Server Software:        uvicorn
Server Hostname:        localhost
Server Port:            8000

Document Path:          /chatHTTP
Document Length:        1104 bytes

Concurrency Level:      200
Time taken for tests:   8.579 seconds
Complete requests:      200
Failed requests:        197
   (Connect: 0, Receive: 0, Length: 197, Exceptions: 0)
Total transferred:      255499 bytes
Total body sent:        59200
HTML transferred:       226299 bytes
Requests per second:    23.31 [#/sec] (mean)
Time per request:       8579.234 [ms] (mean)
Time per request:       42.896 [ms] (mean, across all concurrent requests)
Transfer rate:          29.08 [Kbytes/sec] received
                        6.74 kb/s sent
                        35.82 kb/s total

Connection Times (ms)
              min  mean[+/-sd] median   max
Connect:        0   24   6.7     21      37
Processing:  1108 2291 1526.0   1691    7249
Waiting:     1107 2290 1526.1   1691    7248
Total:       1138 2315 1525.7   1722    7271

Percentage of the requests served within a certain time (ms)
  50%   1722
  66%   1921
  75%   2096
  80%   2414
  90%   4013
  95%   6955
  98%   7199
  99%   7251
 100%   7271 (longest request)
```

##### 第二天

依然是200并发

4o 韩国端点
```
Server Software:        
Server Hostname:        af-koreacentral-aoai.openai.azure.com
Server Port:            443
SSL/TLS Protocol:       TLSv1.3,TLS_AES_256_GCM_SHA384,2048,256
Server Temp Key:        ECDH secp384r1 384 bits
TLS Server Name:        af-koreacentral-aoai.openai.azure.com

Document Path:          /openai/deployments/AF-gpt4o/chat/completions?api-version=2024-10-21
Document Length:        1016 bytes

Concurrency Level:      200
Time taken for tests:   10.391 seconds
Complete requests:      200
Failed requests:        9
   (Connect: 0, Receive: 0, Length: 9, Exceptions: 0)
Keep-Alive requests:    200
Total transferred:      334856 bytes
Total body sent:        107200
HTML transferred:       203236 bytes
Requests per second:    19.25 [#/sec] (mean)
Time per request:       10390.616 [ms] (mean)
Time per request:       51.953 [ms] (mean, across all concurrent requests)
Transfer rate:          31.47 [Kbytes/sec] received
                        10.08 kb/s sent
                        41.55 kb/s total

Connection Times (ms)
              min  mean[+/-sd] median   max
Connect:        0  543 269.0    519    1417
Processing:   510 1412 833.8   1203    7859
Waiting:      510 1412 833.8   1203    7859
Total:        510 1956 897.2   1795    8466

Percentage of the requests served within a certain time (ms)
  50%   1795
  66%   1993
  75%   2125
  80%   2209
  90%   2722
  95%   3247
  98%   4885
  99%   7144
 100%   8466 (longest request)

```


突然意识到了一些事情...

原来azure的服务就是有延迟，并且有的延迟就是很大...
#### 并发&长尾

##### 确认ab的测试方式

ab的测试过程，1条测试消息+然后是c个线程轮流发送n个消息。没问题

##### 继续测试

是否符合ab的测试的规律？

- 1000并发 韩国节点 

```
Server Hostname:        af-koreacentral-aoai.openai.azure.com
Server Port:            443
SSL/TLS Protocol:       TLSv1.3,TLS_AES_256_GCM_SHA384,2048,256
Server Temp Key:        ECDH secp384r1 384 bits
TLS Server Name:        af-koreacentral-aoai.openai.azure.com

Document Path:          /openai/deployments/AF-gpt4o/chat/completions?api-version=2024-10-21
Document Length:        1016 bytes

Concurrency Level:      1000
Time taken for tests:   7.859 seconds
Complete requests:      1000
Failed requests:        307
   (Connect: 0, Receive: 0, Length: 307, Exceptions: 0)
Non-2xx responses:      281
Keep-Alive requests:    1000
Total transferred:      1430903 bytes
Total body sent:        536000
HTML transferred:       823649 bytes
Requests per second:    127.24 [#/sec] (mean)
Time per request:       7859.014 [ms] (mean)
Time per request:       7.859 [ms] (mean, across all concurrent requests)
Transfer rate:          177.80 [Kbytes/sec] received
                        66.60 kb/s sent
                        244.41 kb/s total

Connection Times (ms)
              min  mean[+/-sd] median   max
Connect:        0  413 435.8      0    1317
Processing:   126  817 620.0    937    6196
Waiting:      126  817 620.0    937    6196
Total:        126 1230 967.8   1202    6767

Percentage of the requests served within a certain time (ms)
  50%   1202
  66%   1885
  75%   2024
  80%   2087
  90%   2278
  95%   2423
  98%   2728
  99%   3548
 100%   6767 (longest request)
 ```

1000并发 本地fastapi 同步

```
Server Software:        uvicorn
Server Hostname:        localhost
Server Port:            8000

Document Path:          /chatHTTP
Document Length:        1099 bytes

Concurrency Level:      1000
Time taken for tests:   32.839 seconds
Complete requests:      1000
Failed requests:        298
   (Connect: 0, Receive: 0, Length: 298, Exceptions: 0)
Non-2xx responses:      294
Total transferred:      935471 bytes
Total body sent:        296000
HTML transferred:       781828 bytes
Requests per second:    30.45 [#/sec] (mean)
Time per request:       32839.400 [ms] (mean)
Time per request:       32.839 [ms] (mean, across all concurrent requests)
Transfer rate:          27.82 [Kbytes/sec] received
                        8.80 kb/s sent
                        36.62 kb/s total

Connection Times (ms)
              min  mean[+/-sd] median   max
Connect:        0   49   9.8     48      74
Processing:   591 15207 8711.4  18711   28511
Waiting:      591 15205 8710.1  18697   28502
Total:        665 15256 8708.3  18752   28553

Percentage of the requests served within a certain time (ms)
  50%  18752
  66%  20874
  75%  21996
  80%  22988
  90%  23519
  95%  23843
  98%  27695
  99%  28019
 100%  28553 (longest request)
```

1000并发 本地fastapi 异步

```
Server Software:        uvicorn
Server Hostname:        localhost
Server Port:            8000

Document Path:          /chatHTTP
Document Length:        1099 bytes

Concurrency Level:      1000
Time taken for tests:   22.470 seconds
Complete requests:      1000
Failed requests:        530
   (Connect: 0, Receive: 0, Length: 530, Exceptions: 0)
Non-2xx responses:      526
Total transferred:      691640 bytes
Total body sent:        296000
HTML transferred:       531964 bytes
Requests per second:    44.50 [#/sec] (mean)
Time per request:       22470.037 [ms] (mean)
Time per request:       22.470 [ms] (mean, across all concurrent requests)
Transfer rate:          30.06 [Kbytes/sec] received
                        12.86 kb/s sent
                        42.92 kb/s total

Connection Times (ms)
              min  mean[+/-sd] median   max
Connect:        0   36  12.1     35      58
Processing:  1264 12510 7808.1  18159   20681
Waiting:     1264 12363 7671.5  18104   20639
Total:       1322 12546 7805.0  18183   20717

Percentage of the requests served within a certain time (ms)
  50%  18183
  66%  19154
  75%  19969
  80%  20372
  90%  20640
  95%  20660
  98%  20681
  99%  20689
 100%  20717 (longest request)
```

还是有问题。基本可以确定azure的服务会有一些会卡住，但是经过fastapi转发，还是相比直接请求慢

问了一下 朋友 朋友说可以从这几个方面排查：

1. azure服务自带的监控面板
2. 是否并发占用了所有cpu

#### azure监控面板

包含以下指标

```
### 1. **Azure OpenAI - HTTP Requests**

- **Azure OpenAI Availability Rate**：表示 Azure OpenAI 服务的可用性，通常是服务在一段时间内没有中断的比例。
- **Azure OpenAI Requests**：指的是发出的所有请求的总数。包括向 OpenAI 模型（如对话、嵌入等）发送的请求数量。

### 2. **Azure OpenAI - Latency**

- **Normalized Time to First Byte (TTFB)**：请求到收到第一个字节的时间，衡量的是从请求发出到开始接收响应的延迟。
- **Time Between Tokens**：指的是生成一个 token（如文本生成中的单个词或符号）之间的时间间隔，通常反映了模型生成内容的速率。
- **Time to Last Byte**：从请求开始到最终接收到所有响应字节的时间，衡量整个响应过程的延迟。
- **Time to Response** 关注的是整个请求的响应时间（包括所有延迟）。barry注：如果是流式，那应该比timetolastbyte小很多，非流式的话感觉差不多。
- **Tokens Per Second** 关注的是模型在每秒钟生成多少个 token，反映的是生成内容的速率。

### 3. **Azure OpenAI - Usage**

- **Active Tokens**：指的是当前正在使用的 tokens 数量。tokens 是指模型处理的文本单元（包括输入和输出文本的内容）。
- **Generated Completion Tokens**：生成的响应 tokens 数量。指模型生成的输出文本的 tokens 数量。
- **Processed Fine-Tuned Training Hours**：已经处理的微调训练时间，表示你是否使用了微调模型，并计算了其消耗的计算资源时间。
- **Processed Inference Tokens**：推理请求中处理的 tokens 数量。即发送给模型用于生成回答的 tokens 数量。
- **Processed Prompt Tokens**：提示请求中的 tokens 数量，即用户输入给模型的文本中包含的 tokens 数量。
- **Prompt Token Cache Match Rate**：表示缓存中存储的提示 tokens 与实际请求中的匹配比例。较高的匹配率表明缓存机制的有效性较高。
- **Provisioned-managed Utilization**：表示已配置的资源（如计算、内存）和其利用率。这通常涉及你为服务预配的计算资源（例如虚拟机、GPU）的使用情况。
- **Provisioned-managed Utilization V2**：类似上面指标，但可能是更新版本或更详细的资源利用情况指标。

### 4. **Cognitive Services - HTTP Requests**

- **Blocked Calls**：被阻止的 API 调用。可能是由于权限问题或访问控制。
- **Client Errors**：客户端错误（如 400 错误），通常是由请求格式不正确或缺少必要参数导致。
- **Data In**：传入的数据量，通常指你向服务发送的数据量。
- **Data Out**：传出的数据量，通常指服务返回给你的数据量。
- **Latency**：API 响应的延迟时间，通常用于衡量服务的性能。
- **Rate Limit**：表示API请求频率的限制，可能由于超过了预设的请求频次限制。
- **Server Errors**：服务器错误（如 500 错误），通常是由于服务端问题导致的。
- **Successful Calls**：成功的 API 调用次数。
- **Total Calls**：所有 API 调用的总次数。
- **Total Errors**：所有错误的总次数，包括客户端和服务器端错误。

### 5. **Cognitive Services SLI**

- **Availability Rate**：指认知服务的可用性率，类似于 **Azure OpenAI Availability Rate**，衡量服务的稳定性。

### 6. **Content Safety - Risks & Safety**

- **Blocked Volume**：被安全系统阻止的内容量，通常指潜在有害的文本或信息。
- **Harmful Volume Detected**：检测到的有害内容量，表示安全系统识别出可能有害的文本（如暴力、仇恨言论等）。
- **Potentially Abusive User Count**：可能存在滥用行为的用户数量，通常用于检测是否有恶意用户或违反行为规范的用户。
- **Safety System Event**：安全系统事件，通常指发生的与安全相关的事件，如检测到潜在风险或异常行为。
- **Total Volume Sent for Safety Check**：为安全检查而发送的总文本量，表示你发送给安全审查系统的所有数据量。
```

看来 azureopenai的请求确实会有延迟：

![[Pasted image 20241208212000.png]]

**这里探索了一会grafana+Prometheus，最终发现好像没啥用**

依然是200并发，小问题

按理说，前面200的并发明显被拖后腿了，如果是cpu的问题，应该会显示的比较明显

cpu

![[Pasted image 20241208223231.png]]

200个请求的过程中，没有出现cpu占用满的情况。

##### 看网络请求

瞎测了半天。使用单线程请求的时候，出现一些端倪？

```
tcpdump

barry@BarryLaptop ..ug/openai-chat-backend-fastapi/src/api (git)-[main] % sudo tcpdump -n tcp and \( host *************  \) and port 443 -v -XX  -w fastapi.pcap
tcpdump: listening on wlp3s0, link-type EN10MB (Ethernet), snapshot length 262144 bytes
^C130 packets captured


barry@BarryLaptop ..ug/openai-chat-backend-fastapi/src/api (git)-[main] % sudo tcpdump -n tcp and \( host *************  \) and port 443 -v -XX  -w ab-direct.pcap 
tcpdump: listening on wlp3s0, link-type EN10MB (Ethernet), snapshot length 262144 bytes
^C69 packets captured
69 packets received by filter
```

使用ab测试的时候，每个线程的连接不会中断。因此只需要握手一次
fastapi请求的时候，每次请求都需要重复握手?

后来发现不是，只是每个线程都握手一次。重复利用的线程没有重复握手

#### 部署到其他机器上

我的电脑上 情况竟然突然好转了
16核心，开32个进程，效果竟然和直接请求差不多

v100上这一堆核心岂不是更好
竟然并不好，还是相比ab差一截 晕了

