## 本周完成事项

1. 和子豪一起配置环境，成功运行了论文对应的代码
2. 学习了论文内容，了解了典型的jscc系统
3. 继续学习了linux的一些知识
## 论文学习

简单学习了 *Deep Joint Source Channel Coding for Wireless Image Transmission with OFDM*

对于一个基于深度学习的通信模型，其构成主要是一个自编码器实现编解码，中间加入信道的模型。如何模拟不同的信道，设计面向不同场景的模型都是一个通信系统设计的关键。

本文中针对无限场景，模拟了agn和多径衰落。并且设计了两个不同模型。最终作者称在模拟的环境下可以达到超过传统ldpc的效果。

## 配置环境

配置了代码对应的环境，中间解决了包括conda源，代理，版本等问题。花的时间比较多，不过最终代码跑起来了，也成功训练了一个代码中的implicit类型的模型。

## linux学习

本周在linux上又学到了一些实用的命令：

type，这个可以看某个命令的类型，比如脚本，二进制文件等

### 重定向

本周又复习了一下重定向的机制。重定向分为标准输出，标准错误重定向和标准输入重定向，即把标准输出重定向到文件和把文件送到标准输入（标准错误暂时不讨论）。除此之外，还有一个管道，可以把标准输出送到另一个函数的标准输入。

```
./myprogram > output

./myprogram_needinput < input

./myprogram | ./myprogram_needinput
```

尤其是，**当你的文件需要测试数据，使用`.testprogram < testdata`就可以方便的测试输入据**，不用每次都输入

## time
统计命令执行开始到结束的用时
```
barry@barry-laptop:~$ time sudo apt update
错误:1 http://packages.microsoft.com/repos/code stable InRelease
  403  Forbidden [IP: 2620:1ec:bdf::74 80]
命中:2 https://dl.google.com/linux/chrome/deb stable InRelease    
命中:3 https://deb.termius.com squeeze InRelease 
命中:4 http://security.ubuntu.com/ubuntu mantic-security InRelease  
命中:5 http://us.archive.ubuntu.com/ubuntu mantic InRelease     
命中:6 http://us.archive.ubuntu.com/ubuntu mantic-updates InRelease             
命中:7 http://us.archive.ubuntu.com/ubuntu mantic-backports InRelease           
命中:8 https://ppa.launchpadcontent.net/oibaf/graphics-drivers/ubuntu mantic InRelease
正在读取软件包列表... 完成
E: 无法下载 http://packages.microsoft.com/repos/code/dists/stable/InRelease  403  Forbidden [IP: 2620:1ec:bdf::74 80]
E: 仓库 “http://packages.microsoft.com/repos/code stable InRelease” 的签名不再生效。
N: 无法安全地用该源进行更新，所以默认禁用该源。
N: 参见 apt-secure(8) 手册以了解仓库创建和用户配置方面的细节。

real    0m3.297s
user    0m0.002s
sys     0m0.008s
```
可以用来统计应用性能，方便看到执行时间

## cron
定期执行脚本。可以计划性地每天执行。还有一个类似的anacron命令
# 找东西

## man -k

根据关键字找命令。**很有用**。比如上学期我们找一个dhcp server的程序，只需要
```
barry@barry-laptop:~$ man -k dhcp
dhclient-script (8)  - DHCP client network configuration script
dhclient.conf (5)    - DHCP client configuration file
dhclient.leases (5)  - DHCP client lease database
dhcp-eval (5)        - ISC DHCP conditional evaluation
dhcp-options (5)     - Dynamic Host Configuration Protocol options
dhcpcd (8)           - a DHCP client
dhcpcd-run-hooks (8) - DHCP client configuration script
dhcpcd.conf (5)      - dhcpcd configuration file
dnsmasq (8)          - A lightweight DHCP and caching DNS server.
netplan-ip (8)       - retrieve IP information (like DHCP leases) from the system
```
可以看到所有和dhcp相关的程序。结合gpt的推荐可以做出更优的选择。
## whereis

简单易用，比find 和 locate记起来简单点，不用加什么参数

## tldr

个简单的可以查看命令常用用法的命令。比如tar命令：

```
barry@barry-laptop:~$ tldr tar
tar

归档实用程序。
通常与压缩方法结合使用，例如 gzip 或 bzip2.
更多信息：https://www.gnu.org/software/tar.

 - 创建存档并将其写入文件：
   tar cf 目标.tar 文件1 文件2 ...

 - 创建一个 gzip 压缩文件并将其写入文件：
   tar czf target.tar.gz file1 file2 ...

 - 使用相对路径从目录创建一个 gzip 压缩文件：
   tar czf target.tar.gz --directory=path/to/directory .

 - 详细地将（压缩的）存档文件提取到当前目录中：
   tar xvf source.tar[.gz|.bz2|.xz]

 - 将（压缩的）存档文件解压缩到目标目录中：
   tar xf source.tar[.gz|.bz2|.xz] --directory=directory

 - 创建压缩存档并将其写入文件，使用存档后缀确定压缩程序：
   tar caf target.tar.xz file1 file2 ...

 - 详细列出 tar 文件的内容：
   tar tvf source.tar

 - 从存档文件中提取与模式匹配的文件：
   tar xf source.tar --wildcards "*.html"
```