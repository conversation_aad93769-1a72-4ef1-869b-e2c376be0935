# 本周完成事项

本周暂时没有什么工作，于是根据自己的兴趣以及以后的需要，进一步学习了linux的使用技巧。这里介绍几个提升使用效率和系统理解的工具和概念。

## backgroud & forwadground （前后台）

使用C-z将命令放到后台，使用jobs查看当前运行的命令，使用fg n进入。
## history

使用history可以查看以前执行过的命令，使用!\<history number\>执行对应的命令

使用! \<command\> 执行最近执行过的命令

## strace

`strace` 是一款强大的Linux命令行工具，用于监视和跟踪**系统调用**以及**信号**。。相关知识点：

`strace <command>` 跟踪命令的调用。

> 上学期，当时写过一个计算网速的软件，当时的思路就是学习nload源码，看他如何实时地跟踪网络速度变化。最终阅读源码，很艰难地找到了调用的api，发现是直接读取系统的某个文件。
>
>如果是使用strace,当nload调用api的时候我们就能看到。问题就从阅读源码->寻找关键的调用。

```
Barry@japan-linux:~$ strace nload
execve("/usr/bin/nload", ["nload"], 0x7ffc934ab940 /* 28 vars */) = 0
brk(NULL)                               = 0x55bfab94b000
arch_prctl(0x3001 /* ARCH_??? */, 0x7ffca20e0d00) = -1 EINVAL (Invalid argument)
access("/etc/ld.so.preload", R_OK)      = -1 ENOENT (No such file or directory)
openat(AT_FDCWD, "/etc/ld.so.cache", O_RDONLY|O_CLOEXEC) = 3
fstat(3, {st_mode=S_IFREG|0644, st_size=38905, ...}) = 0
mmap(NULL, 38905, PROT_READ, MAP_PRIVATE, 3, 0) = 0x7f0e30096000
close(3)                                = 0
openat(AT_FDCWD, "/lib/x86_64-linux-gnu/libform.so.6", O_RDONLY|O_CLOEXEC) = 3
read(3, "\177ELF\2\1\1\0\0\0\0\0\0\0\0\0\3\0>\0\1\0\0\0\340E\0\0\0\0\0\0"..., 832) = 832
fstat(3, {st_mode=S_IFREG|0644, st_size=73224, ...}) = 0
mmap(NULL, 8192, PROT_READ|PROT_WRITE, MAP_PRIVATE|MAP_ANONYMOUS, -1, 0) = 0x7f0e30094000
mmap(NULL, 75112, PROT_READ, MAP_PRIVATE|MAP_DENYWRITE, 3, 0) = 0x7f0e30081000
mprotect(0x7f0e30085000, 53248, PROT_NONE) = 0
mmap(0x7f0e30085000, 36864, PROT_READ|PROT_EXEC, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0x4000) = 0x7f0e30085000
mmap(0x7f0e3008e000, 12288, PROT_READ, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0xd000) = 0x7f0e3008e000
mmap(0x7f0e30092000, 8192, PROT_READ|PROT_WRITE, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0x10000) = 0x7f0e30092000
close(3)                                = 0
openat(AT_FDCWD, "/lib/x86_64-linux-gnu/libncurses.so.6", O_RDONLY|O_CLOEXEC) = 3
read(3, "\177ELF\2\1\1\0\0\0\0\0\0\0\0\0\3\0>\0\1\0\0\0000~\0\0\0\0\0\0"..., 832) = 832
fstat(3, {st_mode=S_IFREG|0644, st_size=161872, ...}) = 0
mmap(NULL, 164232, PROT_READ, MAP_PRIVATE|MAP_DENYWRITE, 3, 0) = 0x7f0e30058000
mprotect(0x7f0e3005f000, 131072, PROT_NONE) = 0
mmap(0x7f0e3005f000, 102400, PROT_READ|PROT_EXEC, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0x7000) = 0x7f0e3005f000
mmap(0x7f0e30078000, 24576, PROT_READ, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0x20000) = 0x7f0e30078000
mmap(0x7f0e3007f000, 8192, PROT_READ|PROT_WRITE, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0x26000) = 0x7f0e3007f000
close(3)                                = 0
openat(AT_FDCWD, "/lib/x86_64-linux-gnu/libtinfo.so.6", O_RDONLY|O_CLOEXEC) = 3
read(3, "\177ELF\2\1\1\0\0\0\0\0\0\0\0\0\3\0>\0\1\0\0\0 \347\0\0\0\0\0\0"..., 832) = 832
fstat(3, {st_mode=S_IFREG|0644, st_size=192032, ...}) = 0
mmap(NULL, 194944, PROT_READ, MAP_PRIVATE|MAP_DENYWRITE, 3, 0) = 0x7f0e30028000
mmap(0x7f0e30036000, 61440, PROT_READ|PROT_EXEC, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0xe000) = 0x7f0e30036000
mmap(0x7f0e30045000, 57344, PROT_READ, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0x1d000) = 0x7f0e30045000
mmap(0x7f0e30053000, 20480, PROT_READ|PROT_WRITE, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0x2a000) = 0x7f0e30053000
close(3)                                = 0
openat(AT_FDCWD, "/lib/x86_64-linux-gnu/libstdc++.so.6", O_RDONLY|O_CLOEXEC) = 3
read(3, "\177ELF\2\1\1\3\0\0\0\0\0\0\0\0\3\0>\0\1\0\0\0 \341\t\0\0\0\0\0"..., 832) = 832
fstat(3, {st_mode=S_IFREG|0644, st_size=1956992, ...}) = 0
mmap(NULL, 1972224, PROT_READ, MAP_PRIVATE|MAP_DENYWRITE, 3, 0) = 0x7f0e2fe46000
mprotect(0x7f0e2fedc000, 1290240, PROT_NONE) = 0
mmap(0x7f0e2fedc000, 987136, PROT_READ|PROT_EXEC, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0x96000) = 0x7f0e2fedc000
mmap(0x7f0e2ffcd000, 299008, PROT_READ, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0x187000) = 0x7f0e2ffcd000
mmap(0x7f0e30017000, 57344, PROT_READ|PROT_WRITE, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0x1d0000) = 0x7f0e30017000
mmap(0x7f0e30025000, 10240, PROT_READ|PROT_WRITE, MAP_PRIVATE|MAP_FIXED|MAP_ANONYMOUS, -1, 0) = 0x7f0e30025000
close(3)                                = 0
openat(AT_FDCWD, "/lib/x86_64-linux-gnu/libgcc_s.so.1", O_RDONLY|O_CLOEXEC) = 3
read(3, "\177ELF\2\1\1\0\0\0\0\0\0\0\0\0\3\0>\0\1\0\0\0\3405\0\0\0\0\0\0"..., 832) = 832
fstat(3, {st_mode=S_IFREG|0644, st_size=104984, ...}) = 0
mmap(NULL, 107592, PROT_READ, MAP_PRIVATE|MAP_DENYWRITE, 3, 0) = 0x7f0e2fe2b000
mmap(0x7f0e2fe2e000, 73728, PROT_READ|PROT_EXEC, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0x3000) = 0x7f0e2fe2e000
mmap(0x7f0e2fe40000, 16384, PROT_READ, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0x15000) = 0x7f0e2fe40000
mmap(0x7f0e2fe44000, 8192, PROT_READ|PROT_WRITE, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0x18000) = 0x7f0e2fe44000
close(3)                                = 0
openat(AT_FDCWD, "/lib/x86_64-linux-gnu/libc.so.6", O_RDONLY|O_CLOEXEC) = 3
read(3, "\177ELF\2\1\1\3\0\0\0\0\0\0\0\0\3\0>\0\1\0\0\0\300A\2\0\0\0\0\0"..., 832) = 832
pread64(3, "\6\0\0\0\4\0\0\0@\0\0\0\0\0\0\0@\0\0\0\0\0\0\0@\0\0\0\0\0\0\0"..., 784, 64) = 784
pread64(3, "\4\0\0\0\20\0\0\0\5\0\0\0GNU\0\2\0\0\300\4\0\0\0\3\0\0\0\0\0\0\0", 32, 848) = 32
pread64(3, "\4\0\0\0\24\0\0\0\3\0\0\0GNU\0\356\276]_K`\213\212S\354Dkc\230\33\272"..., 68, 880) = 68
fstat(3, {st_mode=S_IFREG|0755, st_size=2029592, ...}) = 0
pread64(3, "\6\0\0\0\4\0\0\0@\0\0\0\0\0\0\0@\0\0\0\0\0\0\0@\0\0\0\0\0\0\0"..., 784, 64) = 784
pread64(3, "\4\0\0\0\20\0\0\0\5\0\0\0GNU\0\2\0\0\300\4\0\0\0\3\0\0\0\0\0\0\0", 32, 848) = 32
pread64(3, "\4\0\0\0\24\0\0\0\3\0\0\0GNU\0\356\276]_K`\213\212S\354Dkc\230\33\272"..., 68, 880) = 68
mmap(NULL, 2037344, PROT_READ, MAP_PRIVATE|MAP_DENYWRITE, 3, 0) = 0x7f0e2fc39000
mmap(0x7f0e2fc5b000, 1540096, PROT_READ|PROT_EXEC, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0x22000) = 0x7f0e2fc5b000
mmap(0x7f0e2fdd3000, 319488, PROT_READ, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0x19a000) = 0x7f0e2fdd3000
mmap(0x7f0e2fe21000, 24576, PROT_READ|PROT_WRITE, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0x1e7000) = 0x7f0e2fe21000
mmap(0x7f0e2fe27000, 13920, PROT_READ|PROT_WRITE, MAP_PRIVATE|MAP_FIXED|MAP_ANONYMOUS, -1, 0) = 0x7f0e2fe27000
close(3)                                = 0
openat(AT_FDCWD, "/lib/x86_64-linux-gnu/libdl.so.2", O_RDONLY|O_CLOEXEC) = 3
read(3, "\177ELF\2\1\1\0\0\0\0\0\0\0\0\0\3\0>\0\1\0\0\0 \22\0\0\0\0\0\0"..., 832) = 832
fstat(3, {st_mode=S_IFREG|0644, st_size=18848, ...}) = 0
mmap(NULL, 8192, PROT_READ|PROT_WRITE, MAP_PRIVATE|MAP_ANONYMOUS, -1, 0) = 0x7f0e2fc37000
mmap(NULL, 20752, PROT_READ, MAP_PRIVATE|MAP_DENYWRITE, 3, 0) = 0x7f0e2fc31000
mmap(0x7f0e2fc32000, 8192, PROT_READ|PROT_EXEC, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0x1000) = 0x7f0e2fc32000
mmap(0x7f0e2fc34000, 4096, PROT_READ, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0x3000) = 0x7f0e2fc34000
mmap(0x7f0e2fc35000, 8192, PROT_READ|PROT_WRITE, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0x3000) = 0x7f0e2fc35000
close(3)                                = 0
openat(AT_FDCWD, "/lib/x86_64-linux-gnu/libm.so.6", O_RDONLY|O_CLOEXEC) = 3
read(3, "\177ELF\2\1\1\3\0\0\0\0\0\0\0\0\3\0>\0\1\0\0\0\300\323\0\0\0\0\0\0"..., 832) = 832
fstat(3, {st_mode=S_IFREG|0644, st_size=1369384, ...}) = 0
mmap(NULL, 1368336, PROT_READ, MAP_PRIVATE|MAP_DENYWRITE, 3, 0) = 0x7f0e2fae2000
mmap(0x7f0e2faef000, 684032, PROT_READ|PROT_EXEC, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0xd000) = 0x7f0e2faef000
mmap(0x7f0e2fb96000, 626688, PROT_READ, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0xb4000) = 0x7f0e2fb96000
mmap(0x7f0e2fc2f000, 8192, PROT_READ|PROT_WRITE, MAP_PRIVATE|MAP_FIXED|MAP_DENYWRITE, 3, 0x14c000) = 0x7f0e2fc2f000
close(3)                                = 0
mmap(NULL, 8192, PROT_READ|PROT_WRITE, MAP_PRIVATE|MAP_ANONYMOUS, -1, 0) = 0x7f0e2fae0000
arch_prctl(ARCH_SET_FS, 0x7f0e2fae1380) = 0
mprotect(0x7f0e2fe21000, 16384, PROT_READ) = 0
mprotect(0x7f0e2fc2f000, 4096, PROT_READ) = 0
mprotect(0x7f0e2fc35000, 4096, PROT_READ) = 0
mprotect(0x7f0e2fe44000, 4096, PROT_READ) = 0
mmap(NULL, 8192, PROT_READ|PROT_WRITE, MAP_PRIVATE|MAP_ANONYMOUS, -1, 0) = 0x7f0e2fade000
mprotect(0x7f0e30017000, 45056, PROT_READ) = 0
mprotect(0x7f0e30053000, 16384, PROT_READ) = 0
mprotect(0x7f0e3007f000, 4096, PROT_READ) = 0
mprotect(0x7f0e30092000, 4096, PROT_READ) = 0
mprotect(0x55bfab0e6000, 8192, PROT_READ) = 0
mprotect(0x7f0e300cd000, 4096, PROT_READ) = 0
munmap(0x7f0e30096000, 38905)           = 0
brk(NULL)                               = 0x55bfab94b000
brk(0x55bfab96c000)                     = 0x55bfab96c000
openat(AT_FDCWD, "/etc/nload.conf", O_RDONLY) = -1 ENOENT (No such file or directory)
openat(AT_FDCWD, "/home/<USER>/.nload", O_RDONLY) = -1 ENOENT (No such file or directory)
stat("/sys/class/net", {st_mode=S_IFDIR|0755, st_size=0, ...}) = 0
openat(AT_FDCWD, "/sys/class/net", O_RDONLY|O_NONBLOCK|O_CLOEXEC|O_DIRECTORY) = 3
fstat(3, {st_mode=S_IFDIR|0755, st_size=0, ...}) = 0
getdents64(3, /* 5 entries */, 32768)   = 128
getdents64(3, /* 0 entries */, 32768)   = 0
close(3)                                = 0
stat("/sys/class/net", {st_mode=S_IFDIR|0755, st_size=0, ...}) = 0
stat("/sys/class/net", {st_mode=S_IFDIR|0755, st_size=0, ...}) = 0
stat("/sys/class/net", {st_mode=S_IFDIR|0755, st_size=0, ...}) = 0
stat("/sys/class/net/eth0", {st_mode=S_IFDIR|0755, st_size=0, ...}) = 0
openat(AT_FDCWD, "/sys/class/net/eth0/statistics/rx_bytes", O_RDONLY) = 3
read(3, "127161618485\n", 8191)         = 13
close(3)                                = 0
openat(AT_FDCWD, "/sys/class/net/eth0/statistics/tx_bytes", O_RDONLY) = 3
read(3, "102931875389\n", 8191)         = 13
close(3)                                = 0
openat(AT_FDCWD, "/sys/class/net/eth0/statistics/rx_packets", O_RDONLY) = 3
read(3, "238092227\n", 8191)            = 10
close(3)                                = 0
openat(AT_FDCWD, "/sys/class/net/eth0/statistics/tx_packets", O_RDONLY) = 3
read(3, "222395271\n", 8191)            = 10
close(3)                                = 0
openat(AT_FDCWD, "/sys/class/net/eth0/statistics/rx_errors", O_RDONLY) = 3
read(3, "0\n", 8191)                    = 2
close(3)                                = 0
openat(AT_FDCWD, "/sys/class/net/eth0/statistics/tx_errors", O_RDONLY) = 3
read(3, "0\n", 8191)                    = 2
close(3)                                = 0
openat(AT_FDCWD, "/sys/class/net/eth0/statistics/rx_dropped", O_RDONLY) = 3
read(3, "0\n", 8191)                    = 2
close(3)                                = 0
openat(AT_FDCWD, "/sys/class/net/eth0/statistics/tx_dropped", O_RDONLY) = 3
read(3, "0\n", 8191)                    = 2
close(3)                                = 0
socket(AF_INET, SOCK_STREAM, IPPROTO_IP) = 3
ioctl(3, SIOCGIFADDR, {ifr_name="eth0", ifr_addr={sa_family=AF_INET, sin_port=htons(0), sin_addr=inet_addr("********")}}) = 0
close(3)                                = 0
stat("/sys/class/net/lo", {st_mode=S_IFDIR|0755, st_size=0, ...}) = 0
openat(AT_FDCWD, "/sys/class/net/lo/statistics/rx_bytes", O_RDONLY) = 3
read(3, "51162273\n", 8191)             = 9
close(3)                                = 0
openat(AT_FDCWD, "/sys/class/net/lo/statistics/tx_bytes", O_RDONLY) = 3
read(3, "51162273\n", 8191)             = 9
close(3)                                = 0
openat(AT_FDCWD, "/sys/class/net/lo/statistics/rx_packets", O_RDONLY) = 3
read(3, "528510\n", 8191)               = 7
close(3)                                = 0
openat(AT_FDCWD, "/sys/class/net/lo/statistics/tx_packets", O_RDONLY) = 3
read(3, "528510\n", 8191)               = 7
close(3)                                = 0
openat(AT_FDCWD, "/sys/class/net/lo/statistics/rx_errors", O_RDONLY) = 3
read(3, "0\n", 8191)                    = 2
close(3)                                = 0
openat(AT_FDCWD, "/sys/class/net/lo/statistics/tx_errors", O_RDONLY) = 3
read(3, "0\n", 8191)                    = 2
close(3)                                = 0
openat(AT_FDCWD, "/sys/class/net/lo/statistics/rx_dropped", O_RDONLY) = 3
read(3, "0\n", 8191)                    = 2
close(3)                                = 0
openat(AT_FDCWD, "/sys/class/net/lo/statistics/tx_dropped", O_RDONLY) = 3
read(3, "0\n", 8191)                    = 2
close(3)                                = 0
socket(AF_INET, SOCK_STREAM, IPPROTO_IP) = 3
ioctl(3, SIOCGIFADDR, {ifr_name="lo", ifr_addr={sa_family=AF_INET, sin_port=htons(0), sin_addr=inet_addr("127.0.0.1")}}) = 0
close(3)                                = 0
stat("/sys/class/net/ztfp6jg7sn", {st_mode=S_IFDIR|0755, st_size=0, ...}) = 0
openat(AT_FDCWD, "/sys/class/net/ztfp6jg7sn/statistics/rx_bytes", O_RDONLY) = 3
read(3, "6341666\n", 8191)              = 8
close(3)                                = 0
openat(AT_FDCWD, "/sys/class/net/ztfp6jg7sn/statistics/tx_bytes", O_RDONLY) = 3
read(3, "419243117\n", 8191)            = 10
close(3)                                = 0
openat(AT_FDCWD, "/sys/class/net/ztfp6jg7sn/statistics/rx_packets", O_RDONLY) = 3
read(3, "102229\n", 8191)               = 7
close(3)                                = 0
openat(AT_FDCWD, "/sys/class/net/ztfp6jg7sn/statistics/tx_packets", O_RDONLY) = 3
read(3, "155210\n", 8191)               = 7
close(3)                                = 0
openat(AT_FDCWD, "/sys/class/net/ztfp6jg7sn/statistics/rx_errors", O_RDONLY) = 3
read(3, "0\n", 8191)                    = 2
close(3)                                = 0
openat(AT_FDCWD, "/sys/class/net/ztfp6jg7sn/statistics/tx_errors", O_RDONLY) = 3
read(3, "0\n", 8191)                    = 2
close(3)                                = 0
openat(AT_FDCWD, "/sys/class/net/ztfp6jg7sn/statistics/rx_dropped", O_RDONLY) = 3
read(3, "0\n", 8191)                    = 2
close(3)                                = 0
openat(AT_FDCWD, "/sys/class/net/ztfp6jg7sn/statistics/tx_dropped", O_RDONLY) = 3
read(3, "0\n", 8191)                    = 2
close(3)                                = 0

```

## tmux
命令行分屏命令，可以通过tmux将命令行分成多个终端，而不需要同时开多个，很方便。

![[Pasted image 20240225153100.png]]



## alias (别名)

通过设置别名，可以简化某些命令的调用。比如在ubuntu的~/.basrc中，就有这些别名：
```
Barry@japan-linux:~$ cat ~/.bashrc | grep alias
# enable color support of ls and also add handy aliases
    alias ls='ls --color=auto'
    #alias dir='dir --color=auto'
    #alias vdir='vdir --color=auto'
    alias grep='grep --color=auto'
    alias fgrep='fgrep --color=auto'
    alias egrep='egrep --color=auto'
# some more ls aliases
alias ll='ls -alF'
alias la='ls -A'
alias l='ls -CF'
```
这也是为什么，在ubuntu上使用ll la 等可以代替ls -l等。并且很多linux发行版没有这些设置，于是不能通过ll使用ls -l等。

## vim

几个常用快捷键：
**g: go to top**
**G: go to bottom**
d \<number\> d : 删除几行
hjkl: 上下左右
0开头，$行尾
**u： undo， C-r： redo**
**put: 粘贴已删除的内容**

···

## POSIX standard(Portable Operating System Interface)

在UNIX最初分发的时候，各个组织制定了很多不同的自己的标准，包括系统调用，软件库等等。于是有了POSIX标准。POSIX 标准最初由 IEEE（Institute of Electrical and Electronics Engineers）制定。随着时间的推移，POSIX 成为了不仅仅是 UNIX 系统，还有其他类 UNIX 操作系统（如 Linux、BSD 等）的通用标准。

1. **文件系统接口**：`open()`、`read()`、`write()`、`close()`等函数就是POSIX文件系统接口的一部分。
    
2. **进程控制接口**：POSIX定义了一套用于进程控制和管理的接口，包括进程创建、销毁、等待进程状态变化等函数。例如，`fork()`、`exec()`、`wait()`、`exit()`
    
3. **信号处理接口**：POSIX定义了一套信号处理接口，用于处理进程间的通信和事件处理。这些接口包括注册信号处理函数、发送信号、捕获信号等。例如，`signal()`、`kill()`、`sigaction()`
    
4. **线程接口**：POSIX定义了一套用于创建和管理线程的接口，包括线程创建、销毁、同步等函数。例如，`pthread_create()`、`pthread_join()`、`pthread_mutex_lock()`、`pthread_cond_wait()`
    
5. **tcp/ip协议** `socket()、 bind()、listen()、recv()`等等。 