# 本周完成事项

1. 完善了上周修改的建议书，进一步细化技术细节，并移除了不必要的文献部分。
2. 学习并掌握了基于HTTP的SPARQL查询，修改了相关数据，调研了推理规则修改的方法。
3. 基于上周学习的内容，开发了一个简单的客户端小程序，模拟了实时数据上传和任务执行的功能。

# 数据库增删改查

## 查询 Python 实现

上周总结了使用SELECT查询，这里展示一个实际编程实现查询的效果:

```bash
PS C:\Users\<USER>\debug\spaql> & C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/python3.11.exe c:/Users/<USER>/debug/spaql/main.py
{'head': {'vars': ['com', 'movie']}, 'results': {'bindings': [{'com': {'type': 'uri', 'value': 'http://www.semanticweb.org/vv/ontologies/2024/3/unmanned_system#打击林肯号'}, 'movie': {'type': 'uri', 'value': 'http://www.semanticweb.org/vv/ontologies/2024/3/unmanned_system#打击任务'}}, {'com': {'type': 'uri', 'value': 'http://www.semanticweb.org/vv/ontologies/2024/3/unmanned_system#打击林肯号'}, 'movie': {'type': 'uri', 'value': 'http://www.w3.org/2002/07/owl#NamedIndividual'}}, {'com': {'type': 
......(省略内容)
```

## 修改&添加：Update语句

SPARQL 1.1 Update 提供了一种强大的方式来执行数据的添加和修改。可以使用如下命令：

- **添加数据：**

    ```sparql
    PREFIX ex: <http://example.org/>  
    INSERT DATA {     
        ex:John ex:hasFriend ex:Jane . 
    }
    ```
    添加一条表示John和Jane是朋友的关系。

- **修改数据：**

    ```sparql
    PREFIX ex: <http://example.org/>  
    DELETE { ex:John ex:hasFriend ex:Jane . } 
    INSERT { ex:John ex:hasFriend ex:Mike . } 
    WHERE { ex:John ex:hasFriend ex:Jane . }
    ```
    修改John的朋友从Jane变为Mike。

#### HTTP API

通过HTTP API上传数据，支持多种数据格式，如Turtle和RDF/XML。使用如下HTTP POST命令可以向指定数据集添加数据：

```bash
curl -X POST --data-binary "@data.ttl" -H "Content-Type: text/turtle" http://localhost:3030/dataset/data
```

#### Jena API

在Java程序中使用Jena API操作数据，可以构建和修改RDF模型，并通过编程方式将变更发送到服务器。由于我目前对Java不熟悉，所以没有进一步研究。

## 删除 DELETE语句

如果知道确切的三元组（主题、谓词、宾语）并想将其从数据中删除，可以直接使用`DELETE DATA`语句。例如，要删除John和Jane之间的朋友关系，可以写：

```sparql
PREFIX ex: <http://example.org/>  
DELETE DATA {     
    ex:John ex:hasFriend ex:Jane . 
}
```

# 实时修改规则

在Apache Jena Fuseki中，推理规则通常在数据集的配置阶段被设置，并在数据集运行时应用。这意味着一旦数据集启动，其使用的推理规则通常不能动态地更改。这是因为推理引擎需要在启动时加载并准备规则，以便在处理查询和数据操作时提供高效的性能。

不过可以通过以下几种方法实现类似更新的效果：

1. **重新启动数据集**
2. **周期性更新**
3. **手动编程推理**

# 下周计划

1. 继续优化客户端小程序，增加更多功能和完善用户界面。
2. 深入学习推理规则的编写和应用，尝试在实际项目中使用。
3. 调研其他SPARQL查询优化方法，提高查询效率。