# 本周完成事项

1. 航天一院项目相关，包括学习pretege， apache jena，fusiki等用法
2. 服务器相关，包括磁盘数据读取，远程方案等

# protege，apache jena

protege用来创建本体，可以导出到rdf/owl文件。可以导入到apache jena中然后做推理。这周主要学习了这些软件的用法，下一步可以学习进一步打通.

## 使用过程：

使用protege对本体进行建模，最终可以生成一个本体文件。将文件导入到jena中，就可以进行查询，jena自己会进行推理，搜索。

## SPARQL

SPARQL即SPARQL Protocol and RDF Query Language的递归缩写，专门用于访问和操作RDF数据，是语义网的核心技术之一。W3C的RDF数据存取小组（RDF Data Access Working Group, RDAWG）对其进行了标准化。在2008年，SPARQL 1.0成为W3C官方所推荐的标准。2013年发布了SPARQL 1.1。相对第一个版本，其支持RDF图的更新，提供更强大的查询，比如：子查询、聚合操作（像我们常用的count）等等。


**一个SPARQL查询本质上是一个带有变量的RDF图，查询结果不仅会返回定义好的内容，还会返回推理出的符合要求的内容**

实际上就是用rdf格式表示的带有变量的关系。比如，*无人机a  的雷达型号是 xxx*  ，这是一个普通的spo格式的关系，相应的，我们可以写成sparql格式： *无人机a 的雷达型号是 ?x* 。可以看到，把一部分代替成问号 ？ + x变量，即可进行查询操作。

举例子，一个查询可以是这样的：


```text
SELECT * WHERE {
  ?s ?p ?o
}
```

SPARQL的部分关键词：

- SELECT， 指定我们要查询的变量。在这里我们查询所有的变量，用\*代替。
- WHERE，~~指定我们要查询的图模式。含义上和SQL的WHERE没有区别。~~ 没看懂，不过写的都是where

```text
SELECT ?n WHERE {
  ?s rdf:type : device.
  ?s :deviceName '大哥大'.
  ?a :isaVendorof ?s.
  ?a :VendorName ?n
}
```
大哥大这个设备有哪些供应商

还有限定数量等方面的查询方式，就不写了。


# 服务器相关

和李凯配置了服务器，配置了相关软件。并且打算尝试更方便一些的frp，将端口映射到公网上。
