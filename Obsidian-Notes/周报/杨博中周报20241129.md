
# 本周完成事项

1. 表格：新增删除方法，增删改查四大件重写/新写了两个了：查&删
2. 测试新的端点
4. 针对欧盟语料优化

# 表格

给`AzureAISearchRetrivalModule` 加上了删除方法：

#### 关于`serach`方法的记录

search方法参数实在是太多了，这里按照重要性记录几个参数

- search_text: text you want to search 
- filter
- select
- query_tyep:simple, full or semantic

- skip
- top
- 
- query_type:simple or full or semantic

算了还是太多了

写了一个删除脚本，感觉写的不太优雅。算了，急着用，先这样。

感觉对表格的提问还行？前端这个渲染有点问啊
![[5b1062116786e8d3f4610d87f213472.png]]![[f8e39ae8705b7ed6eeda33cf0c4ecb4.png]]

![[Pasted image 20241125214005.png]]
靠，要今天搞完？感觉有点多呀。算了，测试先放在后面吧。现在要：

1. 控制输出，减少偶尔的错误
2. 测试新的负载均衡位置，速度
3. 甚至可以把第一轮选择的模型换成mini，加快速度。但是这总体还是得10秒，真难崩啊
4. 最紧急的，赶紧把现在的表格整上去。需要把垃圾语料删一下。部署倒是不用部署，直接本地操作。就是怕删错了 😒


终于删完了。index的删除有32000条的限制，保存到本地然后分开删除。这么多语料，删除的心惊胆战的。能不能整个备份啊，测试环境也是难用得一。


重新嵌入中。明天再看看吧。

第二天

嵌入的速度怎么这么慢？怎么办

改多线程把。但是嵌入部分的代码。。。都是老代码，且比较乱...改起来真的要命。要不也设计成module方便组合到pipeline中吧？淦，有点难度，这种专用的程序，感觉不好控制自由度。

改到一半发现，慢的原因竟然是。。他按照字符计算token。对于maxtoken=512，它会逐个字符计算，直到，直到达到512. 改成换行了。

30秒一个，ok。放到本地嵌入吧。远程暂停一下，本地连接不了数据库，所以还要嵌入完了再手动改一下数据库。

ok。不过时间已经来到了下午。感觉有点来不及。

# 新端点

|          | 10组简单问答 | 10组rag |
| -------- | ------- | ------ |
| 本地测试     | 19''    | 108''  |
| 本地测试 未均衡 | 15''    | 104’‘  |

#### 详细报告

##### 简单问答总结

```
均衡前：
Server Software:        uvicorn
Server Hostname:        localhost
Server Port:            80

Document Path:          /chatHTTP
Document Length:        167 bytes

Concurrency Level:      1
Time taken for tests:   15.040 seconds
Complete requests:      10
Failed requests:        9
   (Connect: 0, Receive: 0, Length: 9, Exceptions: 0)
Total transferred:      3093 bytes
Total body sent:        2910
HTML transferred:       1833 bytes
Requests per second:    0.66 [#/sec] (mean)
Time per request:       1503.952 [ms] (mean)
Time per request:       1503.952 [ms] (mean, across all concurrent requests)
Transfer rate:          0.20 [Kbytes/sec] received
                        0.19 kb/s sent
                        0.39 kb/s total

Connection Times (ms)
              min  mean[+/-sd] median   max
Connect:        0    0   0.1      0       0
Processing:   721 1504 419.2   1434    2244
Waiting:      721 1503 419.2   1433    2244
Total:        721 1504 419.1   1434    2244

Percentage of the requests served within a certain time (ms)
  50%   1434
  66%   1718
  75%   1735
  80%   1889
  90%   2244
  95%   2244
  98%   2244
  99%   2244
 100%   2244 (longest request)


均衡后：
Server Software:        uvicorn
Server Hostname:        localhost
Server Port:            80

Document Path:          /chatHTTP
Document Length:        152 bytes

Concurrency Level:      1
Time taken for tests:   19.339 seconds
Complete requests:      10
Failed requests:        9
   (Connect: 0, Receive: 0, Length: 9, Exceptions: 0)
Total transferred:      2982 bytes
Total body sent:        2910
HTML transferred:       1722 bytes
Requests per second:    0.52 [#/sec] (mean)
Time per request:       1933.902 [ms] (mean)
Time per request:       1933.902 [ms] (mean, across all concurrent requests)
Transfer rate:          0.15 [Kbytes/sec] received
                        0.15 kb/s sent
                        0.30 kb/s total

Connection Times (ms)
              min  mean[+/-sd] median   max
Connect:        0    0   0.1      0       0
Processing:   802 1934 2670.1   1136    9498
Waiting:      802 1933 2670.2   1136    9498
Total:        802 1934 2670.1   1137    9498

Percentage of the requests served within a certain time (ms)
  50%   1137
  66%   1211
  75%   1267
  80%   1684
  90%   9498
  95%   9498
  98%   9498
  99%   9498
 100%   9498 (longest request)
```
##### rag问答总结
```text
均衡前：
Server Software:        uvicorn
Server Hostname:        localhost
Server Port:            80

Document Path:          /chatHTTP
Document Length:        252 bytes

Concurrency Level:      1
Time taken for tests:   104.464 seconds
Complete requests:      10
Failed requests:        9
   (Connect: 0, Receive: 0, Length: 9, Exceptions: 0)
Non-2xx responses:      1
Total transferred:      4821 bytes
Total body sent:        3120
HTML transferred:       3534 bytes
Requests per second:    0.10 [#/sec] (mean)
Time per request:       10446.427 [ms] (mean)
Time per request:       10446.427 [ms] (mean, across all concurrent requests)
Transfer rate:          0.05 [Kbytes/sec] received
                        0.03 kb/s sent
                        0.07 kb/s total

Connection Times (ms)
              min  mean[+/-sd] median   max
Connect:        0    0   0.1      0       0
Processing:  5989 10446 2951.2  10173   15009
Waiting:     5989 10446 2951.2  10173   15009
Total:       5990 10446 2951.2  10173   15009

Percentage of the requests served within a certain time (ms)
  50%  10173
  66%  12099
  75%  13267
  80%  13839
  90%  15009
  95%  15009
  98%  15009
  99%  15009
 100%  15009 (longest request)


均衡后：
Server Software:        uvicorn
Server Hostname:        localhost
Server Port:            80

Document Path:          /chatHTTP
Document Length:        379 bytes

Concurrency Level:      1
Time taken for tests:   108.923 seconds
Complete requests:      10
Failed requests:        9
   (Connect: 0, Receive: 0, Length: 9, Exceptions: 0)
Non-2xx responses:      1
Total transferred:      4440 bytes
Total body sent:        3120
HTML transferred:       3153 bytes
Requests per second:    0.09 [#/sec] (mean)
Time per request:       10892.345 [ms] (mean)
Time per request:       10892.345 [ms] (mean, across all concurrent requests)
Transfer rate:          0.04 [Kbytes/sec] received
                        0.03 kb/s sent
                        0.07 kb/s total

Connection Times (ms)
              min  mean[+/-sd] median   max
Connect:        0    0   0.0      0       0
Processing:  7931 10892 4264.2  10327   22036
Waiting:     7931 10892 4264.2  10327   22036
Total:       7931 10892 4264.2  10328   22037

Percentage of the requests served within a certain time (ms)
  50%  10328
  66%  10360
  75%  11321
  80%  13185
  90%  22037
  95%  22037
  98%  22037
  99%  22037
 100%  22037 (longest request)
```



