## 本周完成事项

1. 复习了一些c的知识，包括c的发展，标准，编译过程等
2. 搬运了服务器，研究了反向代理方案frp

## C的小知识

1. C最初并没有所谓的标准。后来两个人合著了一本书：the c programming language成为了公认的c的标准。后来美国国家标准协会(ANSI)开发了一个标准，在1989年发布，被称为ANSI C标准(C89)，后来被国际标准化组织(ISO)于90年采用(于是也叫ISO C标准或C90.  ISO C,  ANSI C, C89, C90 is totally the same)。
2. 后来ANSI/ISO委员会在1999修改了一版，被称为C99标准。像我们熟悉的- C++ 风格注释（`//`）就是这次引入的。
3. 后来，11年又修改了一版，被称为C11. 再后来，C17（也被称为为 C18）是于2018年6月发布的 ISO/IEC 9899:2018 的非正式名称，也是目前（截止到2020年6月）为止最新的 C语言编程标准，被用来替代 C11 标准。C17 没有引入新的语言特性，只对 C11 进行了补充和修正。

### C的编译

编译->链接

- 编译就能把源代码转换成中间代码，中间代码有多种，我们就以机器语言代码作为中间代码来举例子。生成机器语言代码。但是这时候还缺少两个东西，启动代码和库代码。启动代码用于标识用于不同操作系统。库代码是链接器要加入的东西，它把程序中调用的函数(printf)的机器语言代码加入到中间代码中。最终，在加入这两个东西后，生成了一份完整的可运行的代码。
### how to inspect bin file?
time to show, **xxd**(小兄弟)!
how to view a bin file in 0&1 format?

```
xxd -d a.out

00000000: 01111111 01000101 01001100 01000110 00000010 00000001  .ELF..
00000006: 00000001 00000000 00000000 00000000 00000000 00000000  ......
0000000c: 00000000 00000000 00000000 00000000 00000011 00000000  ......
```

## 实验室服务器相关事项

### frp方案研究

frp fast reverse proxy 快速反向代理 是github上一个受欢迎的开源项目，原理也很简单：

对于被代理主机A，公网主机B，客户端C。B建立一个与A的连接，然后确定代理的端口。比如，将所有发送到B的7000端口的数据都发送到A的88端口，这样对于C来说，其发送给B的7000端口的数据就等于发送到了A的80端口。

优点：
1. 对客户端透明，只需要把以前的访问的端口和地址一换就可以访问
2. 宽带取决于链路中最低的宽带，速度可以很快
缺点：
1. 需要自备vps
2. 到国外的话不一定稳定


在实验室实验的没问题，但是到了发改委就不行了。和技术人员交流，有以下收获：

1. 最近两会期间，可能会限制特殊地区发送到国外的流量，导致连接失败。
2. 公网服务器在日本，新加坡。发改委走联通的宽带，到日本不算快，新加坡的vps本身延迟也有点大。

## git命令学习

### checkout

checkout命令可以用来切换到某个分支或者复原到某个状态，但是为什么两个看上去联系不大的单词要用同一个命令呢？关于git checkout命令，做了一些研究：
