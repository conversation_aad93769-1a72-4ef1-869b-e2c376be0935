本周主要完成毕设相关内容，包括：

1. 阅读graphrag源码，分析管道，日志等实现
2. 调研时空相关问答数据集
3. 修改源码，实现索引，提问能力
4. 导出neo4j文件，实现图可视化


# 2. 时空问答相关数据集

[\[2402.11542\] Question Answering Over Spatio-Temporal Knowledge Graph](https://arxiv.org/abs/2402.11542)

这篇强相关,可以借鉴,但是没开源也没发表

# 3. 索引&提问

索引方面考虑了模糊性,设计了一种不丢弃时间模糊性的索引方法:

关键的方法为:

时间表述->确定性时间段表达

一个示例时间节点表示为:

```
{
    "raw_text": "那个动荡的年代",
    "type": "TIME",
    "fuzziness_type": "contextual_relative",
    "interpretations": [...],
    "confidence": 0.6
}
```

```
# 示例节点结构
    raw_text, 
    fuzziness_type, 
    interpretations,
    context_clues,
    overall_confidence,
    needs_human_review

	'那个动荡的年代',
    '相对模糊',
    '{
        "candidates": [
            {
                "interpretation": "第一次世界大战期间",
                "time_range": "1914-1918", 
                "confidence": 0.4,
                "evidence": ["文档提到1905年", "科学主题"]
            },
            {
                "interpretation": "科学革命时期",
                "time_range": "1900-1920",
                "confidence": 0.6, 
                "evidence": ["爱因斯坦", "相对论", "科学变革"]
            }
        ],
        "best_guess": "科学革命时期",
        "uncertainty_level": "中等"
    }',
    ARRAY['1905年', '爱因斯坦', '相对论', '科学变革'],
    0.6,
    true
```


# 4. Neo4j/gephi 可视化

![[Pasted image 20250714083606.png]]