# 本周完成事项

1. 调研ragflow等rag项目
2. 调试bug
# rag相关项目
### doc处理

ragflow使用了deepdoc处理方案，其宣传是：

![[Pasted image 20240624080758.png]]

	识别后的rawdata是：
```

(\'1.单元格合并的情况：@@1\t70.0\t184.3\t122.7\t137.7##\n2.表格被切割在不同页上
@@1\t71.0\t203.0\t386.0\t398.3##\n\n6.图片倾斜
@@3\t70.0\t132.0\t199.0\t215.0##\', [(<PIL.Image.Image image mode=RGB
size=725x222 at 0x7F761F7796C0>, [\'项目\n具有产品应有的色泽。\n色泽\n具有产品应有的
滋味和气味，无酸败味和其它异味\n滋味、气味\n组织紧密、无粘液、无焦斑、无莓变\n组织形态\n无
正常视力可见外来杂质\n杂质\']), (<PIL.Image.Image image mode=RGB size=1365x555 at
0x7F761F77AC50>, [\'项目：菌落总数/（CFU/g); n：5; C：2; m：104; M：10s; 试验方法：
GB 4789. 2\t——来自“表3微生物限量与试验方法采样方案"及限量”\', \'项目：大肠菌群／
（CFU/g）; n：5; C：2; m：10; M：102; 试验方法：GB4789.3平板计数法\t——来自“表3微生物
限量与试验方法采样方案"及限量”\', \'霉菌/（CFU/g)的沙门氏菌：金黄色葡萄球菌／（CFU/g）;
M 150的5：5; M 150的0：1; M 150的0/25g：100; M：1000; GB 4789. 15的GB 4789.4：
GB4789.10第二法\t——来自“表3微生物限量与试验方法采样方案"及限量”\']),
(<PIL.Image.Image image mode=RGB size=1300x672 at 0x7F761F77AB30>, [\'项目品：铅
（以Pb计），mg/kg; 蛋类制品的指标*：0. 1; 谷物制品的指标*：0.1; 指标*的肉制品：0.4; 水
产制菜制品的指标*：0.9; 水果蔬制品的指标*：0.9; 指标*的食用菌：0.9; 复合调味料制品的指标
*：0.9; 试验方法：GB 5009.12\t——来自“表2理化指标与试验方法”\', \'项目品：镉（以cd
计），mg/kg; 蛋类制品的指标*：0.05; 谷物制品的指标*：0.2; 指标*的肉制品：0.1; 水产制菜
制品的指标*：0.2; 指标*的食用菌：0.5; 复合调味料制品的指标*：一; 试验方法：GB 5009.15\t
——来自“表2理化指标与试验方法”\', \'项目品：甲基汞（以Hg计），mg/kgM; 水产制菜制品的指标
*：0.5; 试验方法：GB 5009.17\t——来自“表2理化指标与试验方法”\', \'项目品：总汞（以Hg
计），mg/kg; 谷物制品的指标*：0.02a; 水产制菜制品的指标*：0.5; 指标*的食用菌：0.1; 复合
调味料制品的指标*：二; 试验方法：GB 5009.17\t——来自“表2理化指标与试验方法”\', \'项目
品：无机砷（以As计），mg/kg; 谷物制品的指标*：0.2; 水产制菜制品的指标*：0. 1; 指标*的食
用菌：一; 试验方法：GB 5009.11\t——来自“表2理化指标与试验方法”\', \'项目品：总砷（以As
计），mg/kg; 指标*的肉制品：0.5; 指标*的食用菌：0.5; 复合调味料制品的指标*：0.5h; 试验
方法：GB 5009.11\t——来自“表2理化指标与试验方法”\', \'项目品：铬（以Cr计），mg/kg; 谷物
制品的指标*：1. 0°; 指标*的肉制品：1.0; 水产制菜制品的指标*：2.0; 复合调味料制品的指标
*：一; 试验方法：GB 5009.123\t——来自“表2理化指标与试验方法”\', \'项目品：展青霉素，
μg/kg; 水果蔬制品的指标*：50; 试验方法：GB 5009.185\t——来自“表2理化指标与试验方法”\',
\'项目品：苯并[a]芘，μg/kgM; 谷物制品的指标*：5.0; 指标*的肉制品：5.0; 水产制菜制品的指
标*：5.0k; 复合调味料制品的指标*：一; 试验方法：GB 5009.27\t——来自“表2理化指标与试验方
法”\', \'项目品：N-二甲基亚硝胺，ug/kg≤; 指标*的肉制品：3.0; 水产制菜制品的指标*：4.
0²; 水果蔬制品的指标*：一; 复合调味料制品的指标*：一; 试验方法：GB 5009.26\t——来自“表2
理化指标与试验方法”\', \'项目品：多氯联苯"，mg/kgM; 水产制菜制品的指标*：0.5; 试验方法：
GB 5009.190\t——来自“表2理化指标与试验方法”\']), (<PIL.Image.Image image mode=RGB
size=1289x254 at 0x7F761F77A8F0>, [\'*指标按配料中除水以外最大添加量原料类别的对应要
求执行：“以糙米为主要原料。\', \'*指标按配料中除水以外最大添加量原料类别的对应要求执行：仅
限肉制品（肝脏制品、肾脏制品除外）。\', \'*指标按配料中除水以外最大添加量原料类别的对应要求
执行：仅限鱼类罐头（凤尾鱼、旗鱼罐头除外）。\', \'*指标按配料中除水以外最大添加量原料类别的
对应要求执行：仅限食用菌制品（姬松茸制品除外）。\', \'*指标按配料中除水以外最大添加量原料类
别的对应要求执行：“仅限水产动物及其制品（肉食性鱼类及其制品除外），\']),
(<PIL.Image.Image image mode=RGB size=1045x777 at 0x7F761F77ACE0>, [\'项目：pH
值; 指标要求：（检测用水pH5.5-6.5)5.8±0.5; 检验方法：pH计\', \'项目：茶多酚（g/100g）;
指标要求：（茶粉样品用25±5C纯水进行溶解，制成0.1%溶液后进行测定33±3; 检验方法：QB/T4067-
2010\', \'项目：灰分(%）; 指标要求：≤30; 检验方法：GB/T18798.2\', \'项目：咖啡碱
（g/100g）; 指标要求：4.5±0.5; 检验方法：GB/T8312-2013第法\', \'项目：水分（%）; 指标
要求：≤5; 检验方法：GB5009.3\', \'项目：容重（g/100mL）; 指标要求：40±10; 检验方法：W-
5010-105\']), (<PIL.Image.Image image mode=RGB size=1347x373 at
0x7F761F77A9E0>, [\'表2理化指标\', \'项目; 指标; 检验方法\', \'水分7（g/100g）; 30
（细口径类），38（粗口径类及无肠衣类）; GB5009.3\', \'脂肪/（g/100g）; 45（优级），55
（普通级）; GB5009.6\', \'18（优级），14（普通级）; GB5009.5\', \'总糖（以葡萄糖计）/
（g/100g）蛋白质/（g/100g）; 22; GB/T9695.31\', \'过氧化值（以脂肪计）/（g/100g）;
0.5; GB5009.227\']), (<PIL.Image.Image image mode=RGB size=1301x385 at
0x7F761F7795D0>, [\'项目; 指标; 检验方法\t——来自“表3污染物限量”\', \'铅（以Pb计）/
（mg/kg）; 0.5; GB5009.12\t——来自“表3污染物限量”\', \'镉（以Cd计）/（mg/kg）; 八;
0.1; GB5009.15\t——来自“表3污染物限量”\', \'总砷（以As计）/（mg/kg); 0.5;

```


## 格式化后效果如下：

![[Pasted image 20240624080907.png]]

## deepdoctection

调研过程中发现了一个叫deepdoctection的项目，感觉效果不错，但是也有不好的地方，效果：


# bug调试

和chad沟通之后，调试了bug
问题还是出在系统上，

一 ValueError是paddle在不同平台上识别的结果返回的数据不一样，导致解析代码出现问题。

二 FileNotFoundError是windows系统文件路径格式和windows不一样，导致识别到的文件名中含
有windows特有的，本应该作为目录被剔除的反斜杠backslash ''被当做了文件名，最后写入的时
候文件名中由于有\，被认为是目录，然后返回目录不存在。

三后续还有问题，后面再继续跑，发现还有报错。报错内容是说encoding的问题
不知道为什么，windows默认当成gbk，而linux是utf-8