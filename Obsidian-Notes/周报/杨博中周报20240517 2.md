# 本周完成事项

1. 继续完善客户端程序
2. 学习了规则设计

# 客户端程序

加入了一个tui界面，可以通过图形化的方式查看各个节点的状态

# 推理规则设计

## 推理规则的基本概念

#### 基本语法

Jena规则使用一种简单的规则语言，语法格式如下：

```txt
[规则名称: (条件) -> (结论)]
```

例如，以下规则表示如果`?person`是`?parent`的父母，并且`?parent`是`?child`的父母，那么`?person`也是`?child`的祖父母：

```txt
[grandparent_rule: 
  (?person ex:hasChild ?parent) 
  (?parent ex:hasChild ?child) 
  -> 
  (?person ex:hasGrandchild ?child)
]
```

## 推理规则设计步骤

1. **确定推理目标：**
   - 明确推理规则的目的，如数据一致性检查、复杂关系推导等。
2. **定义前提条件：**
   - 列出需要满足的条件，可以包含多个三元组模式。
3. **定义推导结论：**
   - 根据前提条件，定义需要推导出的结论，同样可以包含多个三元组模式。

## 实例推理规则设计

### 场景：无人系统任务分配

假设有一个无人系统任务分配的场景，需要根据任务类型和无人机类型自动分配任务。

#### 数据示例

```ttl
@prefix ex: <http://example.org/> .

ex:Drone1 a ex:ReconDrone .
ex:Task1 a ex:ReconTask .

ex:Drone2 a ex:AttackDrone .
ex:Task2 a ex:AttackTask .
```

#### 推理规则

1. 识别侦察任务和侦察无人机匹配：

```txt
[assign_recon_task: 
  (?drone a ex:ReconDrone) 
  (?task a ex:ReconTask) 
  -> 
  (?drone ex:assignedTask ?task)
]
```

2. 识别攻击任务和攻击无人机匹配：

```txt
[assign_attack_task: 
  (?drone a ex:AttackDrone) 
  (?task a ex:AttackTask) 
  -> 
  (?drone ex:assignedTask ?task)
]
```

