### 本周完成事项
---

1. 调研nv agnent相关内容
2. 写程序
3. 修bug

## nv agent相关
---

### 结论

- nvidia没有通用的类似llamaindex的框架，但是有特定领域的sdk(ACE，他们自称sdk,我感觉说是一种框架也可以) 感觉这个不是很重要
- nvidia在llm方面有自己的基础设施(NIM，感觉就是基于容器技术的）以及开发者平台(api, 以及应用程序原型)

下面主要说说ACE和NIM

### ACE

nv在agent方面，有一个ace（Avatar Cloud Engine） agent dev sdk. 但是并不是通用的agent框架.官网自己的介绍如下：

> NVIDIA ACE Agent is an SDK for building conversational AI applications such as chatbots, multi-modal virtual assistants, and [interactive avatars](https://developer.nvidia.com/nvidia-omniverse-platform/ace/tokkio-showcase).

#### 核心组件

- **聊天控制器**：协调语音输入输出，管理ASR、聊天引擎、TTS等模块。自动维护护上下文和对话历史。
- 通过TensorRT优化和Triton部署实现。支持Colang和LangChain等框架。

#### 部署方式

三种方式：

- 本地部署：支持LLM和Agent模型本地部署，用NVIDIA NIM加速
- Kubernetes部署
- 利用NVIDIA NIM微服务 支持云环境部署

- [GitHub - NVIDIA/ACE: NVIDIA ACE samples, workflows, and resources](https://github.com/NVIDIA/ACE)
- NVIDIA ACE Agent Documentation [NVIDIA ACE — ACE Overview](https://docs.nvidia.com/ace/overview/latest/)
- [Getting Started — ACE Agent](https://docs.nvidia.com/ace/ace-agent/latest/getting-started.html)


不过考虑到...主要是做数字人相关的，并且流程稍微有点复杂，就没部署测试

### NIM

> *Built on robust foundations, including inference engines like TensorRT, TensorRT-LLM, and PyTorch, NIM is engineered to facilitate seamless AI inferencing for the latest AI foundation models on NVIDIA GPUs from cloud or datacenter to PC.*  
> 
> *NVIDIA NIM通过为爱好者，开发人员和AI构建者提供预先优化的模型和行业标准的API来简化从实验到部署AI应用程序的旅程，以构建强大的AI代理商，副驾驶员，聊天机器人和助手。建立在强大的基础上，包括Tensorrt，Tensorrt-等推理引擎LLM以及Pytorch，NIM的设计是为了促进NVIDIA GPU的最新AI基础模型的无缝AI推断，从云或数据中心到PC。*

我理解就是nv做的一个可以云上也可以本地的部署各种模型（不只是大模型，还有语音转文字等各种模型）的工具。得益于n家的各种生态，优化也好（应该会好吧）。

![[Pasted image 20250223193440.png]]

- [NIM for Developers \| NVIDIA Developer](https://developer.nvidia.com/nim#ie6lz9)
- n家自己host的nim的平台 [Try NVIDIA NIM APIs](https://build.nvidia.com/explore/discover
- [Getting Started — NVIDIA NIM for Large Language Models (LLMs)](https://docs.nvidia.com/nim/large-language-models/latest/getting-started.html)

nim的nim平台上有一个blueprints类别,是应用程序原型
[Title Unavailable \| Site Unreachable](https://build.nvidia.com/blueprints)

尝试部署rag,pdf extraction,都有一些限制（hardware, early access）

好吧，那不尝试应用程序原型了，试试拉一个模型

在经历一些网络问题后：
![[Pasted image 20250223210714.png]]

需要支付方式？可能我使用个人账号，也没绑定这个
## 程序部分
---

继续写关于限值提取的代码

## bug
---

sgs那边又提了一些bug, 解决了一下