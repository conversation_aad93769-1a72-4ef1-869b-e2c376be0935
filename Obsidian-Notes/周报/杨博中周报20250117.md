
##  本周主要工作

1. 调研postgresql和es, 学习二者基本原理，调研二者在限制项目上的适用性。
2. 设计了限值提取的基本程序框架
## es vs postgres 关键点

es相关评价

```
- **优点：**作为使用广泛的全文搜索数据库，学习上手的难度不大，如果技术团队已经使用了Elasticsearch，则不需要再引入新的技术，就可以满足向量搜索的业务需求。Elasticsearch可以同时满足全文搜索和向量搜索，灵活性比较高。横向扩展的能力比较出色，集群技术成熟。
    
- **缺点：**Elasticsearch是为全文搜索目的而设计的，虽然支持向量搜索，但对于涉及百万级向量搜索及以上的数据，性能会受到影响。Elasticsearch为满足海量数据的检索速度和可靠性，数据存储设计副本等技术，占用空间大。而且内核使用Java开发，运行内存要求非常高。

https://www.53ai.com/news/knowledgegraph/2024082793164.html
```


```
**Pros:  优点：**

- **Full-text search:** Elasticsearch excels at natural language processing (NLP) queries, making it ideal for document retrieval.  
    **全文搜索：** Elasticsearch 擅长自然语言处理 (NLP) 查询，使其成为文档检索的理想选择。
- **Scalability:** Built for distributed environments, Elasticsearch can scale horizontally across multiple servers.  
    **可扩展性：** Elasticsearch 专为分布式环境而构建，可以跨多个服务器水平扩展。
- **Fast performance:** With near real-time indexing and retrieval, it’s optimized for fast data searches, a crucial aspect of RAG.  
    **快速性能：**凭借近乎实时的索引和检索，它针对快速数据搜索进行了优化，这是 RAG 的一个重要方面。

**Cons:  缺点：**

- **Complex setup:** Elasticsearch can be challenging to configure and manage at scale.  
    **复杂的设置：** Elasticsearch 的大规模配置和管理可能具有挑战性。
- **Resource-heavy:** It can consume significant memory and CPU, especially when dealing with large datasets.  
    **资源密集型：**它会消耗大量内存和 CPU，尤其是在处理大型数据集时。

**Best for:** Enterprises and applications needing fast, large-scale document retrieval.  
**最适合：**需要快速、大规模文档检索的企业和应用程序。
```

```
全文搜索数据库（例如 ElasticSearch 和 OpenSearch）在促进全面文本搜索和实现高级分析方面表现出色。然而，在进行矢量相似性搜索和管理高维数据方面，它们与专用矢量数据库相比表现不佳。这些数据库通常需要使用其他工具进行语义搜索的增强，因为它们不使用向量索引，而仅依赖于倒排索引。 [**Qdrant 基准测试**](https://qdrant.tech/benchmarks/)的结果证明，Elasticsearch 的性能落后于 Weaviate、Milvus 和 Qdrant **。**值得注意的是，Elasticsearch 在用于基准测试的所有三个数据集上都表现出显着的延迟和有限的吞吐量。

https://medium.com/@mutahar789/optimizing-rag-a-guide-to-choosing-the-right-vector-database-480f71a33139
```



postgresql相关评价：

```
**Pros:  优点：**

- **Advanced search capabilities:** PostgreSQL supports full-text search, which is essential for retrieving relevant documents.  
    **高级搜索功能：** PostgreSQL 支持全文搜索，这对于检索相关文档至关重要。
- **Complex queries:** Its powerful querying language allows for complex data filtering and retrieval.  
    **复杂查询：**其强大的查询语言允许复杂的数据过滤和检索。
- **Extensible:** PostgreSQL's support for JSON, hstore, and other data types enables it to handle semi-structured data, which is common in RAG setups.  
    **可扩展：** PostgreSQL 对 JSON、hstore 和其他数据类型的支持使其能够处理半结构化数据，这在 RAG 设置中很常见。

**Cons:  缺点：**

- **Not specialized for search:** While PostgreSQL supports full-text search, it’s not as optimized for this purpose as dedicated search engines like Elasticsearch.  
    **不专门用于搜索：**虽然 PostgreSQL 支持全文搜索，但它并没有像 Elasticsearch 等专用搜索引擎那样针对此目的进行优化。
- **Scalability:** PostgreSQL can be more challenging to scale for extremely large datasets.  
    **可扩展性：** PostgreSQL 对于超大数据集的扩展可能更具挑战性。
```

```
postgres**最大支持创建16000维度的向量，最大支持对2000维度的向量建立索引**。
https://help.aliyun.com/zh/rds/apsaradb-rds-for-postgresql/pgvector-for-high-dimensional-vector-similarity-searches#:~:text=%E6%9C%80%E5%A4%A7%E6%94%AF%E6%8C%81%E5%88%9B%E5%BB%BA16000%E7%BB%B4%E5%BA%A6,%E7%BB%B4%E5%BA%A6%E7%9A%84%E5%90%91%E9%87%8F%E5%BB%BA%E7%AB%8B%E7%B4%A2%E5%BC%95%E3%80%82
```

其他相关数据库

Weaviate、Milvus、Qdrant 和 Vespa

### 常见rag开源项目使用的数据库


| 项目名称          | 向量数据库名称                                                                                                                            | 来源                                                                                                                                                                                                                                                           |
| ------------- | ---------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| ragflow       | elasticsearch                                                                                                                      | [  ### 来自工业界的知识库RAG 服务(二)，RagFlow 源码全流程深度 ](https://blog.csdn.net/hustyichi/article/details/139162109#:~:text=%E5%90%91%E9%87%8F%E6%95%B0%E6%8D%AE%E5%BA%93%E4%BD%BF%E7%94%A8%E7%9A%84%E6%98%AF,%E5%AD%98%E5%82%A8%E4%BD%BF%E7%94%A8%E7%9A%84%E6%98%AFMinIO) |
| dify          | 支持：Qdrant（推荐），Weaviate，Zilliz/Milvus，Pgvector，Pgvector-rs，Chroma，OpenSearch，TiDB，Tencent Vector，Oracle，Relyt，Analyticdb, Couchbase | [特性与技术支持](https://docs.dify.ai/zh-hans/getting-started/readme/features-and-specifications)                                                                                                                                                                   |
| qanything（有道） | milvus+es                                                                                                                          | [大模型RAG实战｜开源RAG引擎QAnything技术栈解析](https://53ai.com/news/RAG/2024052934857.html#:~:text=%E7%B3%BB%E7%BB%9F%E4%BD%BF%E7%94%A8%E7%9A%84%E5%90%91%E9%87%8F%E6%95%B0%E6%8D%AE%E5%BA%93%E4%B8%BAMilvus%E3%80%82)                                                    |
| quivr         | faiss 或 pgvector(即postgres的扩展)                                                                                                     | [quivr doc](https://core.quivr.com/en/latest/vectorstores/pgvector/)                                                                                                                                                                                         |

总结：

1. 选择很多，大家的选择也各不相同。
2. 在大多数博客中，大家会推荐很多没见过的数据库，比如milvus,chroma，等等。postgres似乎不是一个流行的选择

### 程序设计

设计限值提取的程序框架

```
├── app
│   ├── api
│   ├── core
│   ├── db
│   ├── models
│   ├── schemas
│   ├── services
│   └── utils
├── main.py
├── README.md
├── requirements.txt
├── tests
└── venv
    ├── bin
    ├── include
    ├── lib
    ├── lib64 -> lib
    └── pyvenv.cfg
```

主要的关键点在于


- 采用模块化设计，封装底层逻辑，比如数据库，ocr，搜索等

- 使用环境变量和配置文件，支持开发和生产环境配置

- 状态管理：使用观察者模式跟踪任务状态



- /api/v1/process-image：处理单个请求

- /api/v1/task-status/{task_id}：查询任务状态
