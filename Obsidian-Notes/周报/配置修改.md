
## 修改内容

在项目文件中找到`chatcode/askdoc/config.json` 文件，修改以下内容：

![[屏幕截图 2025-04-02 165844.png]]

#### 字段解释：

`main_rag_pipeline_config`:

	`azure_openai_api_base`: 负载均衡的容器地址
	`azure_openai_api_engine`: 创建4o资源的时候，给资源起的deployment name。在我们的方案中，会在多个地区创建openai资源，而所有地区的4o的deployment name应该都是相同的。以前名字叫AF-gpt4o
	`search_api_key`: index的key
	`search_api_index_name`:创建的index的名字
	`search_api_endpoint`：创建的index的接口/地址

`other_config.embedding_config`:

	`azure_openai_api_key`:embedding服务的key。请保证embedding创建的资源是使用了 `text-embedding-3-large`,部署名字是large
	`azure_openai_api_base`:某个embedding服务的地址。

*embedding服务使用某一地区的embedding服务即可*

