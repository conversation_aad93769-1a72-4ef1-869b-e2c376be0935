# 本周完成事项

1. 测试算法端性能，包括压力测试，tpm扩容测试等
2. 优化算法端响应时间
3. 和石恒修改问答能力

## 算法端性能

### 接口能力测试

和王禹一起测试了接口能力，扩展tpm容量

现在的tpm容量

450\*7 = 3150 k tpm -> 3150\*6  RPM

bad:

1. 每次问答 token都在2-5K左右，换算成标准提问就是每分钟600条-1575条左右
2. 不知道为什么容量不太对应，实际测下来几乎是减半，不知道哪里出错了...

### 回答耗时

回答耗时还是太慢，排查一下

#### 线上系统测试

ticbot回答耗时，ticbot系统测试，10次

1. 26''
2. 11.9"
3. 14.4"
4. 16.6"
5. 17.6"
6. 8"
7. 14.9"
8. 14.6"
9. 28"
10. 11"


```
逐个分析：
分析之前，我们需要知道，对于一次普通的rag流程，主要耗时过程有

过程                             代号
1. 第一次请求4o，判断调用必要性     4o-1
2. 嵌入                          embedding
3. 搜索                          search-1
4. 4o mini过滤                   4o-mini-1
5. 深度搜索                       search-2
6. 4o 问答                       4o-2

在这里逐个分析
1. 26''

	1. 4o-1           10''
	2. embedding      3''
	3. search-1       2''
	4. 4o-mini-1      2''
	5. search-2       1''
	6. 4o-2              8''

2. 11.9"
	1. 4o-1           2''
	2. embedding      1''
	3. search-1       3''
	4. 4o-mini-1      3''
	5. search-2       <1''
	6. 4o-2              1''

1. 14.4"
	1. 4o-1           2''
	2. embedding      <1''
	3. search-1       2''
	4. 4o-mini-1      3''
	5. search-2       2''
	6. 4o-2              4''

1. 16.6"
	1. 4o-1           2''
	2. embedding      1''
	3. search-1       2''
	4. 4o-mini-1      4''
	5. search-2       1''
	6. 4o-2              6''

1. 17.6"
	1. 4o-1           2''
	2. embedding      1''
	3. search-1       1''
	4. 4o-mini-1      3''
	5. search-2       1''
	6. 4o-2              8''

1. 8"
	1. 4o-1           1''
	2. embedding      <1''
	3. search-1       1''
	4. 4o-mini-1      2''
	5. search-2       1''
	6. 4o-2             2''

1. 14.9"
	1. 4o-1           1''
	2. embedding      <1''
	3. search-1       1''
	4. 4o-mini-1      3''
	5. search-2       3'' #文件多
	6. 4o-2             6''

1. 14.6"
	1. 4o-1           1''
	2. embedding      1''
	3. search-1       2''
	4. 4o-mini-1      2''
	5. search-2       2''
	6. 4o-2             7''

1. 28"
	1. 4o-1           9''
	2. embedding      <1''
	3. search-1       2''
	4. 4o-mini-1      7''
	5. search-2       2''
	6. 4o-2             7''

1. 11"
	1. 4o-1           1''
	2. embedding      <1''
	3. search-1       2''
	4. 4o-mini-1      2''
	5. search-2       1''
	6. 4o-2             4''

```

#### 分析：

good：

1. embeding影响较小
2. 搜索影响较小，尽管仍然有优化空间

embedding和search流程加起来都在5‘’以内

剩下的都是对话了

good:

1. 调用对话接口速度不稳定。可能的原因
	1. [Prompt Caching 提示缓存](https://platform.openai.com/docs/guides/prompt-caching) **后记:应该不是，需要提示词长度达到1024，我们不到**
	2. 负载均衡

#### 总结

在线上系统中，问题主要出在问答上。有以下优化思路：

1. 第一步的问答是否需要4o？实际上第一步完全可以用4omini代替。第一步负责一个流程判断的功能，不需要慢慢的4o   (推测影响不大)
2. azure上是否有更近的地区？多地区负载均衡是否降低了响应速度？  (推测影响较大)

针对以上问题做测试：

##### 本地测试__更换地区+多线程对比

由于上线需要等待，暂时先在本地做测试。但是本地和线上环境可能不一样，于是我们先在本地测试一下没有更改配置的情况：

```
10次测试

1.	
2.
```


本地测实在是太卡了，经常请求卡住，完不成请求

#### 线上测试__更换地区+多线程对比

1. 9.5‘’
2. 8‘’
3. 9.4‘’
4. 10‘’
5. 9‘’
6. 8.8‘’
7. 12.5‘’
8. 8.6‘’
9. 10.6‘’
10. 8.5‘’

请求分析

```
1. 9.5‘’
	1. 4o-1           <1
	2. embedding      1
	3. search-1       1
	4. 4o-mini-1      1
	5. search-2       1
	6. 4o-2           3
2. 8‘’
	1. 4o-1           1
	2. embedding      <1
	3. search-1       2
	4. 4o-mini-1      1
	5. search-2       1
	6. 4o-2           1
3. 9.4‘’
	1. 4o-1           1
	2. embedding      1
	3. search-1       1
	4. 4o-mini-1      1
	5. search-2       1
	6. 4o-2           2
4. 10‘’
	1. 4o-1           1
	2. embedding      1
	3. search-1       1
	4. 4o-mini-1      2
	5. search-2       1
	6. 4o-2           2
5. 9‘’
	1. 4o-1           1
	2. embedding      <1
	3. search-1       2
	4. 4o-mini-1      1
	5. search-2       1
	6. 4o-2           2
6. 8.8‘’
	1. 4o-1           2
	2. embedding      1
	3. search-1       1
	4. 4o-mini-1      1
	5. search-2       1
	6. 4o-2           2
7. 12.5‘’
	1. 4o-1           1
	2. embedding      <1
	3. search-1       1
	4. 4o-mini-1      2
	5. search-2       1
	6. 4o-2           6
8. 8.6‘’
	1. 4o-1           1
	2. embedding      <1
	3. search-1       1
	4. 4o-mini-1      2
	5. search-2       1
	6. 4o-2           2
9. 10.6‘’
	1. 4o-1           1
	2. embedding      1
	3. search-1       2
	4. 4o-mini-1      2
	5. search-2       1
	6. 4o-2           2
10. 8.5‘’
	1. 4o-1           2
	2. embedding      <1
	3. search-1       2
	4. 4o-mini-1      1
	5. search-2       <1
	6. 4o-2           2
```

## 算法端优化

调研了fastapi优化的方法：

- 增加workers数量
- 尝试使用异步编程(未推送)
- 测试fastapi高压低工作下最高请求处理速度

## 表格问答部分

表格问答部分涉及到的旧代码比较多，改起来比较困难

# 下周工作内容

有些工作需要最近处理一下：

1. 数据库同步问题
2. 性能优化问题
3. 旧程序优化问题
4. 配置繁琐问题

tmd，这个jb数据库，我真的要烦死了，查个东西，删个东西 tmd 麻烦的要命
