
1. sgs迁移
2. 开题

### sgs迁移

最终迁移过程大约也是花了两个小时。我们从后台日志查看，azure数据，garie自己评测等最终判断迁移完成。sgs要求不要停掉服务，我就把semantic search关闭了。openai应该不消耗就不花钱。容器根据历史纪录应该也是没有花费。

![[3d9a743be677cbf67654a1686bd58dd.png]]


![[b0b659f252304f79f5d0b611a2fa27b.png]]
老资源的后台请求数据


![[a2b862f53f90463cfbdfe7cf572b09a.png]]
新容器的请求数据

### 开题

调研了rag相关文献，并着手写了部分开题报告。

不知道从什么方向开题合适，考虑到要写开题报告，就暂停了对于记忆的探索，积累不太多


为什么选择加入时空信息

1. 加入时空信息，做时空上的索引感觉是事实存储的一个优化方向
2. 结合之前agent 记忆的探索，对于时空信息的理解，记忆能力也是一个很重要的方向


问题：

1. 如果使用stkg，那么
	1. 这个方向的研究并不多。[spatio-temporal knowledge graph - Google Scholar](https://scholar.google.com/scholar?hl=en&as_sdt=0%2C5&q=spatio-temporal+knowledge+graph&btnG=) 目前的，近年的（2020以后）研究主要关注灾难预测，交通预测，风险分析..（看上去也有些冷门）。
	2. stkg并不像标准kg一样，具有标准化的rdf/owl/其他格式的标准结构。当前缺乏标准
2. 如果单纯引入时空信息加入到graph中，不考虑使用stkg，那么stkg的问题可以规避一下，但是还有其他问题。
	1. 目前评测·这个方向的数据集并没有权威的数据集，有一些小论文做了数据集，但是看上去有些冷门。有一些带有很多时空信息的数据集，但是针对问答场景依然缺少权威数据集/评测。
3. 如果仅仅使用现有的学术成果，而没有创新，那么听上去更像一个工程项目。是否可行？
4. 时空信息听上去似乎有些过于宽泛。。是否能处理各种情况，比如持续性，瞬时性等各种问题？是否能处理无时间的问题？
5.  TimeR4、Spatial-RAG这样的成果是否已经解决了一部分问题？他们都是其中一个维度的，两个维度的似乎依然可以，但是是否显得有些重复？
6. 策略上具体怎么做，我依然没有很具体的方法
7. 评估怎么做，现在数据集没有很权威的，没有针对这个场景的。自己构建又如何保证有效性和难度？能做完吗
8. baseline选择的话，就选择其他普通rag系统，这个可以。消融实验，万一没效果 咋办？不过这个问题也不是现在开题就能确定的，就算换个题目，也无法保证就一定有效果。


考虑的问题：

时空如何编码，是否时空独立？、
关注index上的设计/优化，优化一下开题报告，围绕索引丰富研究内容