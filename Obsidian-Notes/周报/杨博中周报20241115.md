# 本周完成事项

讨论限值提取工作
去掉线上搜索功能
bug修改

# 分类

和尹南过了一下分类流程。关键内容如下：

系统的输入中会带有待补全的项目。限值仅需提取输入中已经给出的待测项目。标书中的思路是先判断分类再判断限值。但是分类的判断方法不明确，尹南也没说具体判断方法。这里就先搁置了。

关于表格的识别，有所发现：
1. 多模型比较
	- 4o/4o-mini 效果一般
	- llama3.1-405b 效果一般
	- grok-beta 不支持
	- claude3 sonnt 效果一般
	- Gemini-1.5-Pro 效果一般

## 测试记录

![[page_3 (2).png]]

### 4o
**开启高精度识别**

```markdown
4.2 质量标准

白菜类种子质量应符合表 1 的最低要求。

  

| 作物种类 | 种子类别 | 品种纯度不低于 | 净度 (净种子)不低于 | 发芽率不低于 | 水分不高于 |

| - | - | - | - | - | - |

| 结球白菜 | 原种 | 99.0 | 98.0 | 85 | 7.0 |

| 结球白菜 | 大田用种 | 96.0 | 98.0 | 85 | 7.0 |

| 不结球白菜 | 原种 | 99.0 | 98.0 | 85 | 7.0 |

| 不结球白菜 | 大田用种 | 96.0 | 98.0 | 85 | 7.0 |
```

裁切后
![[Pasted image 20241125082226.png]]


### claude3 sonnt:


```html
<table>
<tr>
  <th colspan="4">表1</th>
</tr>
<tr>
  <th>作物种类</th>
  <th>种子类别</th>
  <th>品种纯度<br>不低于</th>
  <th>溶液(溶种子)<br>不低于</th>
  <th>发芽率<br>不低于</th>
  <th>水分<br>不高于</th>
</tr>
<tr>
  <td rowspan="2">绿肥作物</td>
  <td>原种</td>
  <td>99.0</td>
  <td></td>
  <td></td>
  <td></td>
</tr>
<tr>
  <td>大田用种</td>
  <td>96.0</td>
  <td>98.0</td>
  <td>85</td>
  <td>7.0</td>
</tr>
<tr>
  <td rowspan="3">绿肥白菜</td>
  <td>原种</td>
  <td>99.9</td>
  <td></td>
  <td></td>
  <td></td>
</tr>
<tr>
  <td>亲本</td>
  <td>99.0</td>
  <td>98.0</td>
  <td>85</td>
  <td>7.0</td>
</tr>
<tr>
  <td>纯交种</td>
  <td>96.0</td>
  <td>98.0</td>
  <td>85</td>
  <td>7.0</td>
</tr>
</table>
```
![[Pasted image 20241112135628.png]]

```markdown
| 作物种类 | 种子类别 | 品种纯度不低于 | 溶液(溶种子)不低于 | 发芽率不低于 | 水分不高于 |
| --- | --- | --- | --- | --- | --- |
| 绿肥作物 | 原种 | 99.0 | | | |
| | 大田用种 | 96.0 | 98.0 | 85 | 7.0 |
| 绿肥白菜 | 原种 | 99.9 | | | |
| | 亲本 | 99.0 | 98.0 | 85 | 7.0 |
| | 纯交种 | 96.0 | 98.0 | 85 | 7.0 |
```

字符和结构都识别错了，不考虑了。

裁切后

![[Pasted image 20241125081622.png]]

### Llama-3.1-405B


```html
<table>

<tr>

<th>作物种类</th>

<th>种子类别</th>

<th>典型露露频率</th>

<th>露露(种子)</th>

<th>发育率</th>

<th>水分</th>

</tr>

<tr>

<td>蔓苣根</td>

<td>第一种</td>

<td>99.0</td>

<td>98.0</td>

<td>85</td>

<td>7.0</td>

</tr>

<tr>

<td></td>

<td>大田用种</td>

<td>96.0</td>

<td>98.0</td>

<td>85</td>

<td>7.0</td>

</tr>

<tr>

<td></td>

<td>残种</td>

<td>99.9</td>

<td>98.0</td>

<td>85</td>

<td>7.0</td>

</tr>

<tr>

<td></td>

<td>紫菜</td>

<td>大田用种</td>

<td>99.0</td>

<td>98.0</td>

<td>85</td>

</tr>

<tr>

<td></td>

<td></td>

<td></td>

<td></td>

<td></td>

<td>7</td>

</tr>

</table>
```

markdown的输出也是乱糟糟一片：
![[Pasted image 20241112140358.png]]

裁切后：
![[Pasted image 20241125081221.png]]
### Gemini-1.5-Pro


勉强可以，但是出现了幻觉：

html：

![[Pasted image 20241112140450.png]]


markdown：
```markdown
**4.2 质量标准**

白菜类种子质量应符合表1的最低要求。

| 作物种类 | 种子类别 | 品种纯度 不低于 | 净度(净种子) 不低于 | 发芽率 不低于 | 水分 不高于 |
|---|---|---|---|---|---|
| 结球白菜 | 原种 | 99.0 | 98.0 | 85 | 7.0 |
|  | 大田用种 | 96.0 |  |  |  |
| 不结球白菜 | 原种 | 99.9 | 98.0 | 85 | 7.0 |
|  | 大田用种 | 99.0 |  |  |  |
| 杂交种 | 大田用种 | 96.0 | 98.0 | 85 | 7.0 |
```

裁切后：
![[Pasted image 20241125081416.png]]


如果是整张的文档，其中含有表格，那么直接把整个图片弄过去，表格重建的效果很差：




