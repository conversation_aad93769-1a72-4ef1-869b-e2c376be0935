
# 本周完成事项

1. 排查并发能力问题，暂时无进展，于是先放弃了
2. 小策代码迁移，排查bug
3. 线程安全
4. 再次更改配置方法，把所有数据抽出来
5. 排查内存爆满问题

#### 并发问题

并发问题在不同的机器上测试结果如下：

有问题的：

我自己的笔记本，v100服务器

没问题的

我的另一个电脑，wsl，王禹的电脑，李凯的电脑

于是到此我也不太确定是什么问题，考虑到有可能sgs那边其实没有并发问题，这个就先放一下。



#### 配置文件设计&代码迁移

数据和逻辑分离，所有需要改动的地方都写到一个文件里。

其实也没什么，就是那个什么，写个配置文件

把代码放到github的私有仓库上了，方便以后项目部署到小策和sgs上

#### 线程安全

主rag流程改单例，变成线程安全

#### 嵌入失败&内存爆满

最近sgs把所有失败的都重新嵌入了。很有可能是因为这个原因内存爆满了。我这里本地测试，几个文件下来，内存暂时没有很大的波动。推测是由于超大文件（大几百，上千页）的文件持续处理，中间数据堆积过多。部分文件一页就是几百张图片。

## 待办

rag部署新机器上
sgs要嵌入文件大小的边界
