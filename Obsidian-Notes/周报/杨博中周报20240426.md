
# 本周完成事项

1. 服务器配置
2. 航天一院项目相关

# 服务器

服务器配置可谓一波三折。发改委周一把服务器放过去，结果管钥匙的没来。然后第二天有事，周三最后才过去。不过好在frp配置还算顺利，动动小手就配好了。开机自启也配置了，现场试了很多次重启，没有问题。有一些普通的收获和神奇的收获：

1. 学习了如何开启systemd的开机自启服务，配置frp等。同时也考虑到特殊情况，lk配置了备用方案，防止意外问题
3. 在发改委附近尝试直接笔记本连接国外的服务器，秒封。换服务器，设备都是这样。但是门口的小饭管可以连接。难道审查这么严。
4. frp效果很好，延迟很低，好的时候只有10ms左右，总体在50ms以下。但是带宽不高，只有3Mbps->375kB/s。


# 航天一院项目相关

继续完善protege的本体的建模，写了一部分建议书。

1. 可以导入规则了，但是那个规则如何建立，本体如何建立都是需要仔细思考的地方。有些东西不知道该建模成什么好。比如雷达，我是应该把某个型号的雷达建模成类呢还是实例呢目前就打算仿照oop思路建模

2. 相关知识学习 [[part. 1]] 

3. SPAQL语法 [[part. 1]] 
