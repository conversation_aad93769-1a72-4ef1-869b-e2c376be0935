
**本周完成事项**

1. 评测各个模型ocr能力
2. 限值提取 编写部分代码

### 评测ocr能力

待评测模型:

```
gpt-4o
gpt-4o-mini
gpt-4.1
gpt-4.1-mini
gpt-4.1-nano
gpt-4.5-preview
o1
o3
o3-mini
o4-mini
claude-3-5-sonnet-20240620
claude-3-7-sonnet-20250219
claude-sonnet-4-20250514-thinking
claude-sonnet-4-20250514
claude-opus-4-20250514
gemini-2.5-flash-preview-05-20
gemini-2.5-pro-preview-05-06
deepseek-v3
deepseek-reasoner
grok-3-reasoner
grok-3
```

最终结果:

评测了5张图片在21中模型上的ocr能力, 最终效果较好的是**o4-mini**,错误最少. google的由于api问题没有测试出来(timeout). 附录部分数据(1.png)测试结果于最后.

### 限值提取

这部分目前就是在llamaindex上实现新版的workflow. 需要注意的是, 这里的rag的过程我暂时用了以前的系统,没有单独做.


```
|   |   |   |   |
|---|---|---|---|
 
|col1|col2|col3|1.png|
|gpt-4o-mini|||分数: 4分  <br>  <br>原因:  <br>1. **单位错误**: 正确答案中“能量”的单位是“千焦”，而系统识别结果中是“千卡”，单位错误导致数据含义发生变化。  <br>2. **数值错误**: 正确答案中“碳水化合物”的数值是2.1克，而系统识别结果中是9.1克，数值错误较大。  <br>3. **缺失项**: 正确答案中包含“糖”这一项，系统识别结果中完全缺失。  <br>4. **单位错误**: 正确答案中“钠”的单位是“毫克”，而系统识别结果中是“克”，单位错误导致数据含义发生变化。  <br>5. **数值错误**: 正确答案中“钠”的数值是0毫克，而系统识别结果中是0.2克，数值错误较大。  <br>6. **缺失字段**: 正确答案中“糖”的“nrv_percent”字段为null，系统识别结果中没有体现。  <br>  <br>综合评价:  <br>系统在提取营养成分表时存在较多问题，包括单位错误、数值错误以及缺失项。这些问题会严重影响数据的准确性和可用性，尤其是单位和数值错误可能导致用户误解营养信息。系统需要进一步优化以提高识别的准确性和完整性。|
|gpt-4o|||分数: 6分  <br>  <br>原因:  <br>1. **缺失指标**: 正确答案中包含了“糖”这一项，而OCR系统的识别结果中没有提取到该项。这是一个重要的缺失，影响了营养成分表的完整性。  <br>2. **错误指标**: 正确答案中“钠”的数值为0，而OCR系统识别为2.0。这是一个明显的错误，可能会对用户的判断造成误导。  <br>3. **其他部分正确**: 除了上述问题，OCR系统在“能量”、“蛋白质”、“脂肪”和“碳水化合物”这几项的识别结果与正确答案完全一致，表现较好。  <br>  <br>综合评价:  <br>该OCR系统在部分指标的识别上表现准确，但存在指标缺失和错误的问题，尤其是“糖”的缺失和“钠”的错误会对营养成分表的准确性产生较大影响。系统需要进一步优化以提高识别的全面性和准确性。|
|gpt-4.1|||分数: 6分  <br>  <br>原因:  <br>1. **单位错误**: 正确答案中 `serving_unit` 是 "ml"，而系统识别结果是 "g"，单位错误会导致数据解释上的偏差。  <br>2. **指标缺失**: 正确答案中包含 "糖" 的营养成分，但系统识别结果中缺失了这一项，影响了营养成分表的完整性。  <br>3. **数据错误**: 正确答案中 "钠" 的 `amount` 是 0，而系统识别结果中 "钠" 的 `amount` 是 2.0，数值错误。  <br>4. **NRV百分比缺失**: 正确答案中 "糖" 的 `nrv_percent` 是 `null`，但系统识别结果中没有包含 "糖" 这一项，因此无法评估其百分比。  <br>  <br>综合评价:  <br>系统在识别主要营养成分（如能量、蛋白质、脂肪、碳水化合物）方面表现较好，但在单位识别、数据准确性以及指标完整性方面存在明显问题。这些问题可能会影响实际应用场景中的数据可靠性，需要进一步优化。|
|gpt-4.1-mini|||分数: 6分  <br>  <br>原因:  <br>1. **缺失指标**: 系统的识别结果中缺少了“糖”这一项，而正确答案中明确包含了“糖”指标。这是一个重要的遗漏，影响了系统的完整性。  <br>2. **错误指标**: 系统将“钠”的数值识别为2毫克，而正确答案中“钠”的数值为0毫克。这是一个识别错误，影响了数据的准确性。  <br>3. **错误的NRV百分比**: 系统将“碳水化合物”的NRV百分比识别为11，而正确答案中为1。这是一个显著的错误，影响了数据的可信度。  <br>4. **其他指标正确**: 除上述问题外，系统正确识别了“能量”、“蛋白质”和“脂肪”的数值及单位，表现尚可。  <br>  <br>综合评价:  <br>系统在识别部分指标时表现良好，但存在重要的缺失和错误，尤其是“糖”指标的遗漏和“钠”数值的错误。这些问题降低了系统的实用性和可靠性。建议进一步优化算法，提升对营养成分表的全面性和准确性识别能力。|
|gpt-4.1-nano|||分数: 6分  <br>  <br>原因:  <br>1. **指标缺失**: 系统的识别结果中缺少了“糖”这一项，且“钠”的单位和数值有误。正确答案中“钠”的数值为0，单位为“毫克”，而系统识别结果中“钠”的数值为2.0，单位为“g”。这些缺失和错误会影响营养成分表的完整性和准确性。  <br>2. **单位错误**: 系统将单位“千焦”识别为“kJ”，将“克”识别为“g”，虽然这两者在语义上是等价的，但在中文语境下，正确答案使用的是中文单位，系统未能保持一致。  <br>3. **NRV百分比错误**: 正确答案中“糖”的NRV百分比为`null`，而系统未识别出“糖”这一项，因此无法评估其NRV百分比。  <br>4. **整体结构正确**: 系统的输出结构与正确答案的结构基本一致，能够正确识别出“能量”、“蛋白质”、“脂肪”、“碳水化合物”和“钠”等主要营养成分，且数值和单位大部分是正确的。  <br>  <br>综合评价:  <br>系统在识别营养成分表时能够正确提取大部分主要指标，但存在指标缺失（如“糖”）和单位错误的问题，影响了结果的完整性和准确性。建议优化系统对单位的识别能力，并加强对营养成分表中所有指标的全面提取能力。|
|gpt-4.5-preview|||分数: 6分  <br>  <br>原因:  <br>1. **缺失指标**: 正确答案中包含了“糖”这一项，而OCR系统的识别结果中没有提取到“糖”的相关信息。这是一个重要的缺失，影响了系统的完整性。  <br>2. **错误指标**: OCR系统将“钠”的数值识别为2毫克，而正确答案中“钠”的数值是0毫克。这是一个明显的识别错误，影响了数据的准确性。  <br>3. **其他部分正确**: 除了“糖”缺失和“钠”错误外，其他指标（如“能量”、“蛋白质”、“脂肪”、“碳水化合物”）的数值和单位均正确，且“每份大小”和单位也正确。  <br>  <br>综合评价:  <br>该OCR系统在提取营养成分表时表现尚可，但存在两个主要问题：一是缺失了“糖”这一项，二是“钠”的数值识别错误。这些问题表明系统在处理复杂表格或多项数据时仍需优化。建议加强对数据完整性和准确性的校验机制，以提高系统的可靠性。|
|o1|||分数: 6分  <br>  <br>原因:  <br>1. **缺失指标**: 系统的识别结果中缺少了“糖”这一项，正确答案中明确包含“糖”指标，且其单位为“克”，NRV百分比为`null`。这是一个重要的遗漏，影响了系统的完整性。  <br>2. **错误指标**: 系统将“钠”的数值识别为2.0毫克，而正确答案中“钠”的数值为0毫克。这是一个识别错误，直接影响了数据的准确性。  <br>3. **其他指标正确**: 除了上述问题，其他指标（如“能量”、“蛋白质”、“脂肪”、“碳水化合物”）的数值、单位和NRV百分比均正确，说明系统在部分指标的识别上表现良好。  <br>  <br>综合评价:  <br>系统在识别营养成分表时，能够正确提取大部分指标，但存在遗漏和错误。缺失“糖”指标是一个较大的问题，尤其是对于营养成分表这种需要全面信息的场景。此外，“钠”数值的错误也降低了系统的可信度。建议进一步优化系统的识别算法，尤其是对指标的完整性和数值的准确性进行加强。|
|o3|||分数: 6分  <br>  <br>原因:  <br>1. **单位错误**: 系统输出中，"能量"的单位是"kJ"，而正确答案是"千焦"；"蛋白质"、"脂肪"、"碳水化合物"的单位是"g"，而正确答案是"克"；"钠"的单位是"mg"，而正确答案是"毫克"。单位的错误会影响数据的准确性和用户的理解。  <br>2. **数据缺失**: 系统输出缺少了"糖"这一项，正确答案中"糖"的值为2.0克，且其NRV百分比为null。这是一个重要的营养成分，缺失会导致信息不完整。  <br>3. **数据错误**: "钠"的数值识别错误，系统输出为2.0，而正确答案是0。这是一个关键错误，直接影响数据的准确性。  <br>4. **NRV百分比的处理**: 系统输出中"糖"的NRV百分比缺失（因为"糖"本身未被识别），而正确答案中明确标注为null。虽然这部分影响较小，但仍然是一个问题。  <br>  <br>综合评价:  <br>系统在识别营养成分表时，能够正确识别部分指标（如"能量"、"蛋白质"、"脂肪"、"碳水化合物"），但在单位转换、数据完整性和准确性方面存在明显问题。尤其是数据缺失和错误会对用户的使用体验和决策产生较大影响。建议优化单位识别逻辑、提高数据准确性，并确保所有营养成分项都能被完整提取。|
|o4-mini|||分数: 8分  <br>  <br>原因:  <br>1. **单位错误**: 系统输出中，单位部分有错误。例如：  <br>   - "能量"的单位识别为"kJ"，而正确答案是"千焦"。  <br>   - "蛋白质"、"脂肪"、"碳水化合物"、"糖"的单位识别为"g"，而正确答案是"克"。  <br>   - "钠"的单位识别为"mg"，而正确答案是"毫克"。  <br>   这些单位错误虽然不影响数值，但在实际应用中可能会导致误解。  <br>  <br>2. **数据完整性**: 系统输出的内容与正确答案相比，数据完整性较好，所有的营养成分都被正确识别，没有缺失。  <br>  <br>3. **数值准确性**: 所有数值（如"能量"的36、"碳水化合物"的2.1等）均正确，未出现数值错误。  <br>  <br>4. **NRV百分比**: "糖"的NRV百分比为`null`，与正确答案一致，说明系统在处理空值时表现良好。  <br>  <br>综合评价:  <br>系统在数值识别和数据完整性方面表现优秀，但在单位识别上存在一定问题。单位错误虽然不影响数值，但在实际应用中可能会导致用户混淆，因此扣2分。总体来说，系统的效果较好，仍有改进空间。|
|claude-3-5-sonnet-20240620|||分数: 0分 (0-10)  <br>  <br>原因:  <br>1. 系统完全未检测到营养成分表，输出结果为“未检测到营养成分表”，与正确答案相比，缺失了所有的营养成分信息。  <br>2. 正确答案中包含了详细的营养成分信息，包括每项的名称、含量、单位以及NRV百分比，而系统的输出完全没有任何相关内容。  <br>3. 该OCR系统在此案例中未能完成基本的识别任务，无法提取任何有用信息，表现较差。  <br>  <br>综合评价：系统在此场景下的表现完全失败，无法满足营养成分表提取的需求，需要显著改进识别能力和准确性。|
|claude-3-7-sonnet-20250219|||分数: 0分 (0-10)  <br>  <br>原因:  <br>1. 系统未能检测到营养成分表，输出结果为“未检测到营养成分表”，与正确答案完全不符。  <br>2. 正确答案中包含详细的营养成分信息，包括每项指标的名称、数值、单位以及NRV百分比，而系统的输出完全缺失这些内容。  <br>3. 由于系统未能识别任何信息，无法评估其对具体指标的识别准确性。  <br>  <br>综合评价:  <br>该OCR系统在此测试中表现不佳，未能完成营养成分表的提取任务。需要进一步优化算法或改进模型，以提高对营养成分表的检测和识别能力。|
|claude-sonnet-4-20250514-thinking|||分数: 6分  <br>  <br>原因:  <br>1. **指标缺失**: 系统的识别结果中缺少了“糖”这一项，而正确答案中明确包含了“糖”这一营养成分。缺失一个重要指标会影响系统的完整性。  <br>2. **数据错误**: 系统将“钠”的含量识别为2.0毫克，而正确答案中“钠”的含量为0毫克。这是一个关键数据错误，可能会导致用户对营养成分的误解。  <br>3. **格式问题**: 系统输出的格式与正确答案的格式不一致。例如，正确答案中“serving_size”和“serving_unit”是单独的字段，而系统将其合并为“计量单位”。虽然信息本质上相同，但格式不统一可能会影响后续数据处理的便利性。  <br>4. **单位处理**: 系统输出中“营养素参考值%”的字段名称与正确答案中的“nrv_percent”不一致，虽然信息含义相同，但命名不统一可能会影响数据的标准化。  <br>  <br>综合评价:  <br>系统在识别营养成分表时能够提取大部分信息，但存在指标缺失和数据错误的问题，且输出格式不够规范。这些问题降低了系统的实用性和可靠性。建议优化系统的识别算法，确保所有指标的完整性和准确性，同时统一输出格式以便于后续处理。|
|claude-sonnet-4-20250514|||分数: 6分  <br>  <br>原因:  <br>1. **缺失项**: 正确答案中包含了"糖"这一项，而OCR系统的识别结果中没有提取到该项。这是一个重要的营养成分，缺失会影响数据的完整性。  <br>2. **单位错误**: 正确答案中"钠"的单位是"毫克"，而OCR系统识别为"克"，单位错误会导致数据的误解。  <br>3. **数据结构差异**: 正确答案中使用了更规范的结构（如"serving_size"和"serving_unit"），而OCR系统的结果没有体现这些字段，导致数据的可读性和标准化程度较低。  <br>4. **数值错误**: 正确答案中"钠"的数值是0毫克，而OCR系统识别为2.0克，数值和单位均错误。  <br>5. **维生素C的NRV百分比**: OCR系统识别为"0%"，但正确答案中没有明确提到维生素C的NRV百分比，这可能是系统的默认值，但仍需确认是否准确。  <br>  <br>综合评价:  <br>该OCR系统在提取营养成分表时能够识别大部分关键指标，但存在缺失项、单位错误和数值错误的问题，影响了数据的准确性和完整性。此外，数据结构不够规范化，可能会对后续数据处理造成困难。系统需要进一步优化以提高识别的准确性和标准化程度。|
|claude-opus-4-20250514|||分数: 6分  <br>  <br>原因:  <br>1. **缺失指标**: 正确答案中包含了“糖”这一项营养成分，但OCR系统的识别结果中没有提取到“糖”的相关信息。这是一个重要的缺失，影响了系统的完整性。  <br>2. **单位错误**: 正确答案中“钠”的单位是“毫克”，而OCR系统识别结果中“钠”的单位是“克”，单位错误会导致数据的误解。  <br>3. **结构差异**: OCR系统的输出结构与正确答案的结构不一致。正确答案中每个营养成分都有明确的字段（如`name`、`amount`、`unit`、`nrv_percent`），而OCR系统的结果是以嵌套字典的形式呈现，缺乏统一的字段定义，可能会影响后续数据处理的便利性。  <br>4. **缺少“serving_size”和“serving_unit”字段**: 正确答案中明确标注了每份的大小和单位（100ml），而OCR系统的结果中没有提取到这些信息。  <br>  <br>综合评价:  <br>OCR系统能够识别大部分营养成分及其数值，但存在指标缺失、单位错误和结构不一致的问题。这些问题会影响数据的准确性和可用性，尤其是在需要进一步处理或分析时。系统需要改进对营养成分表的完整性提取，以及对单位的准确识别。|
|deepseek-v3|||分数: 6分  <br>  <br>原因:  <br>1. **缺失指标**: 正确答案中包含了“糖”这一项营养成分，而OCR系统的识别结果中没有提取到该项。这是一个重要的遗漏，因为“糖”是营养成分表中常见且关键的指标。  <br>2. **错误指标**: OCR系统将“钠”的含量识别为“2.0毫克”，而正确答案中“钠”的含量为“0毫克”。这属于数据错误，可能会对用户的理解造成误导。  <br>3. **格式问题**: OCR系统的输出格式与正确答案的结构不一致。例如，正确答案中明确标注了“serving_size”和“serving_unit”，而OCR系统的结果中没有体现这些信息。这可能会影响数据的标准化和后续处理。  <br>4. **其他问题**: OCR系统的结果中包含了额外的信息（如生产商、地址、电话等），这些信息虽然可能有用，但与营养成分表的提取任务无关，可能会增加数据处理的复杂性。  <br>  <br>综合评价:  <br>OCR系统在提取营养成分表时表现尚可，但存在关键指标缺失和数据错误的问题，影响了结果的准确性和完整性。此外，输出格式不够标准化，可能会对后续数据处理造成一定困难。建议优化系统的识别能力，特别是对营养成分表中常见指标的提取和数据校验。|
|deepseek-reasoner|||分数: 6分  <br>  <br>原因:  <br>1. **缺失指标**: 正确答案中包含了“糖”这一项营养成分，但OCR系统的识别结果中没有提取到该项。这是一个重要的遗漏，因为“糖”是营养成分表中的关键指标之一。  <br>2. **数据结构问题**: OCR系统的输出中，营养成分表的结构不够规范。正确答案中每个营养成分都有明确的字段（如`name`、`amount`、`unit`、`nrv_percent`），而OCR系统的结果将这些信息混合在一个字符串中，缺乏清晰的结构化数据。  <br>3. **单位错误**: 在OCR系统的结果中，“钠”的单位是“毫克”，但正确答案中“钠”的单位是“克”。单位错误会导致数据的误解。  <br>4. **净含量错误**: OCR系统将“净含量”识别为“每100毫升”，而正确答案中明确指出了“serving_size”为100和“serving_unit”为“ml”。OCR系统的结果在这一点上不够准确。  <br>5. **生产商信息错误**: OCR系统识别的生产商名称为“上海）有限责任公司”，显然有误，括号部分不完整或错误。  <br>  <br>综合评价:  <br>OCR系统在提取营养成分表时表现出了一定的能力，但存在明显的缺失和错误，尤其是“糖”指标的遗漏和数据结构的不规范。这些问题会影响数据的可用性和准确性。建议优化系统的结构化数据提取能力，并加强对单位和字段完整性的校验。|
|grok-3-reasoner|||### 评判结果  <br>  <br>#### 分数: 3分 (0-10)  <br>  <br>#### 原因:  <br>1. **指标缺失**:  <br>   - 正确答案中包含了“serving_size”和“serving_unit”字段，表示每份的大小和单位，但OCR系统的识别结果中完全缺失了这两个字段。  <br>   - 正确答案中有“糖”这一项营养成分，但OCR系统的识别结果中未提取该项，导致信息缺失。  <br>   - OCR结果未包含“nrv_percent”为`null`的情况（如“糖”的NRV%），这表明系统可能无法处理缺失值。  <br>  <br>2. **指标错误**:  <br>   - OCR系统识别的“能量”值为“3700 千焦”，而正确答案为“36 千焦”，数值差异巨大，显然是识别错误。  <br>   - OCR系统识别的“脂肪”值为“100 克”，而正确答案为“0 克”，同样是严重的识别错误。  <br>   - OCR系统识别的“碳水化合物”值为“0 克”，而正确答案为“2.1 克”，数值错误。  <br>   - OCR系统识别的“钠”单位为“毫克”，但未正确提取数值为“0 毫克”，单位和数值的匹配存在问题。  <br>  <br>3. **格式问题**:  <br>   - OCR系统的输出格式与正确答案的格式不一致。正确答案中使用了“name”、“amount”、“unit”和“nrv_percent”作为字段，而OCR系统的结果中使用了“营养素”、“每100克分量”和“营养参考值%”，字段命名不统一，且未明确区分数值和单位。  <br>   - OCR结果未将数值和单位分开，导致后续数据处理可能会遇到困难。  <br>  <br>4. **综合评价**:  <br>   - OCR系统在提取营养成分表时，存在严重的指标缺失和识别错误问题，尤其是数值的错误会对营养信息的准确性造成重大影响。  <br>   - 格式不统一和缺失字段进一步降低了系统的实用性。  <br>   - 系统在处理单位和数值时未能正确分离，影响了数据的结构化程度。  <br>  <br>#### 建议改进:  <br>1. 增强对数值和单位的识别能力，确保数值准确性。  <br>2. 增加对缺失字段的处理能力，例如“serving_size”、“serving_unit”和“糖”。  <br>3. 统一输出格式，与正确答案保持一致，便于后续数据处理。  <br>4. 提高对特殊情况（如NRV%为`null`）的处理能力，避免遗漏信息。|
|grok-3|||分数: 6分  <br>  <br>原因:  <br>1. **缺失项**: 正确答案中包含了 "糖" 的营养成分，而 OCR 系统的识别结果中没有提取到这一项。这是一个重要的缺失，影响了系统的完整性。  <br>2. **数据错误**: 正确答案中 "钠" 的 "amount" 是 0，而 OCR 系统识别为 2.0 毫克。这是一个明显的识别错误，影响了数据的准确性。  <br>3. **单位和格式问题**: 正确答案中包含了 "serving_size" 和 "serving_unit"（100 ml），而 OCR 系统的结果中没有提取到这些信息。这些信息对于营养成分表的完整性和上下文理解非常重要。  <br>4. **字段命名不一致**: OCR 系统的字段命名与正确答案的字段命名不一致。例如，正确答案使用 "name"、"amount"、"unit" 等字段，而 OCR 系统使用 "项目"、"每100克分量" 等字段。这可能导致后续数据处理的困难。  <br>  <br>综合评价:  <br>OCR 系统在提取主要营养成分（如能量、蛋白质、脂肪、碳水化合物、钠）方面表现尚可，但存在缺失项（如糖）、数据错误（钠的数值）、以及字段命名和格式不一致的问题。这些问题降低了系统的准确性和实用性。建议优化系统的识别算法，特别是对字段的完整性和准确性进行改进，同时统一字段命名以便后续处理。|
```