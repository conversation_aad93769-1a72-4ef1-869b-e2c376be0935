修复失败：

```
Reading package lists... Done
Building dependency tree... Done
Reading state information... Done
Correcting dependencies... failed.
The following packages have unmet dependencies:
 emqx-enterprise : Depends: libodbc2 (>= 2.3.1) but it is not installed
 libc-bin : Depends: libc6 (< 2.36) but 2.40-3 is installed
 libc6 : Breaks: base-files (< 13.3~) but 12ubuntu4.7 is installed
         Breaks: chrony (< 4.2-3~) but 4.2-2ubuntu2 is installed
         Breaks: locales (< 2.40) but 2.35-0ubuntu3.8 is installed
         Breaks: systemd (< 256~rc4-1~) but 249.11-0ubuntu3.12 is installed
 libsasl2-2 : Depends: libsasl2-modules-db (>= 2.1.28+dfsg1-8) but 2.1.27+dfsg2-3ubuntu1.2 is installed
              Recommends: libsasl2-modules (>= 2.1.28+dfsg1-8) but 2.1.27+dfsg2-3ubuntu1.2 is installed
 libssl3t64 : Depends: libzstd1 (>= 1.5.5) but 1.4.8+dfsg-3build1 is installed
              Depends: openssl-provider-legacy but it is not installable
              Breaks: libssl3 (< 3.3.2-1)
              Breaks: openssh-client (< 1:9.4p1) but 1:8.9p1-3ubuntu0.10 is installed
              Breaks: openssh-server (< 1:9.4p1) but 1:8.9p1-3ubuntu0.10 is installed
 libstdc++6 : Depends: gcc-14-base (= 14.2.0-5) but it is not installable
E: Error, pkgProblemResolver::Resolve generated breaks, this may be caused by held packages.
E: Unable to correct dependencies

```

历史记录:

```
 261  wget https://www.emqx.com/zh/downloads/enterprise/5.8.0/emqx-enterprise-5
.8.0-ubuntu24.04-amd64.deb
  262  sudo apt install ./emqx-enterprise-5.8.0-ubuntu24.04-amd64.deb
  263  sudo systemctl start emqx
  264  sudo systemctl daemon-reload
  265  sudo systemctl restart emqx
  266  sudo apt update
  267  apt list --upgradable
  268  ~
  269  sudo apt install ./emqx-enterprise-5.8.0-ubuntu24.04-amd64.deb
  270  apt install libc6
  271  apt install libsas12-2
  272  apt install libss13t64
  273  apt upgrade
  274  apt dist-upgrade
  275  aptitude install emqx-enterprise
  276  apt install aptitude
  277  ls
  278  root@linux-4c16g-gui:/home/<USER>
  279  Couldn't find any package whose name or description matched "emqx-enterpr
ise"
  280  Unable to apply some acti
  281  aptitude install emqx-enterprise-5.8.0-ubuntu24.04-amd64.deb
  282  sudo apt install ./emqx-enterprise-5.8.0-ubuntu24.04-amd64.deb
  283  vim /etc/apt/sources.list
  284  apt upgrade
  285  apt install aptitude
  286  aptitude install emqx-enterprise-5.8.0-ubuntu24.04-amd64.deb
eb~
  288  sudo aptitude install ./emqx-enterprise-5.8.0-ubuntu24.04-amd64.deb
d64.deb
  290  sudo aptitude install emqx-enterprise-5.8.0-ubuntu24.04-amd64.deb
  291  sudo dpkg -i emqx-enterprise-5.8.0-ubuntu24.04-amd64.deb
  292  sudo apt-get install -f
  293  sudo dpkg --remove --force-remove-reinstreq emqx-enterprise
  294  dpkg --get-selections
  295  apt list --installed | grep emq
  296  apt remove emqx-enterprise-5.8.0-ubuntu24.04-amd64.deb 
  297  sudo apt remove emqx-enterprise
  298  ls
  299  reboot
  300  ls
  301  cd ..
301  cd ..
  302  ls
  303  cd download/emqx-enterprise-5.8.0-ubuntu24.04-amd64.deb 
  304  ls
  305  cd download/
  306  ls
  307  apt install emqx-enterprise-5.8.0-ubuntu24.04-amd64.deb 
  308  sudo dpkg -i emqx-enterprise-5.8.0-ubuntu24.04-amd64.deb
  309  sudo apt update
  310  sudo apt upgrade
  311  sudo apt install libc6
  312  sudo apt --fix-broken install
  313  lsb_release -a
  314  sudo apt update
  315  sudo apt upgrade
  316  sudo apt --fix-broken install
  317  sudo dpkg --remove --force-remove-reinstreq emqx-enterprise
  318  sudo rm -rf /etc/emqx
  319  sudo rm -rf /var/lib/emqx
  320  sudo rm -f /home/<USER>/emqx-enterprise-5.8.0-ubuntu24.04-amd64.deb
  321  sudo dpkg --configure -a
  322  apt clean
  323  apt autoclean
  324  sudo apt update
erprise-5.8.0-ubuntu24.04-amd64.deb
  326  ls
  327  sudo apt-get install -f
  328  sudo apt-get remove emqx-enterprise
  329  sudo apt-get purge emqx-enterprise
  330  sudo apt list --installed | grep emqx
  331  sudo dpkg --remove --force-remove-reinstreq emqx-enterprise
  332  rm -rf /etc/emqx
  333  dpkg --remove --force-remove-reinstreq emqx-enterprise
  334  rm -rf /var/lib/emqx
  335  dpkg --configure -a
  336  cat /etc/apt/sources.list
  337  cat /etc/apt/sources.list |  grep emq
  338  ls /etc/apt/sources.list.d/
  339  cd /etc/apt/sources.list
  340  ls
  341  cd /etc/apt
  342  ls
  343  cd sources.list
  344  cat sources.list
  345  cat sources.list | grep emq
345  cat sources.list | grep emq
  346  rm /etc/apt/sources.list.d/
  347  emqx_emqx.list
  348  rm /etc/apt/sources.list.d/emqx_emqx.list 
  349  ls /etc/apt/sources.list.d/
  350  apt install
  351  cd /usr/bin
  352  ls
  353  ls | grep emq
  354  reboot
  355  cd /home/<USER>/
  356  ls
  357  dpkg -i emqx-enterprise-5.8.0-ubuntu24.04-amd64.deb 
  358  rm emqx-enterprise-5.8.0-ubuntu24.04-amd64.deb 
.8.0-ubuntu24.04-amd64.deb
  360  sudo apt install ./emqx-enterprise-5.8.0-ubuntu24.04-amd64.deb
  361  sudo apt install libc6
  362  sudo apt --fix-broken install
  363  sudo dpkg -i emqx-enterprise-5.8.0-ubuntu24.04-amd64.deb
  364  sudo apt install libc6 libodbc2 libsasl2-2 libssl3t64 libstdc++6
erse restricted multiverse'
ain universe restricted multiverse'
se restricted multiverse'
  368  apt update
  369  aptitude install libc6 libodbc2 libsasl2-2 libssl3t64 libstdc++6
  370  sudo apt install libc6 libodbc2 libsasl2-2 libssl3t64 libstdc++6
ain restricted universe multiverse'
  372  vim /etc/apt/sources.list.d/*.list
  373  vim /etc/apt/sources.list
  374  sudo apt update
  375  vim /etc/apt/sources.list
  376  vim /etc/apt/sources.list.d/*.list
  377  nano /etc/apt/sources.list.d/*.list
  378  cat /etc/apt/sources.list.d/*.list
  379  sudo apt update
  380  sudo apt upgrade
380  sudo apt upgrade
  381  sudo apt --fix-broken install
  382  dpkg --remove --force-remove-reinstreq emqx-enterprise
  383  rm -rf /usr/bin/emqx
  384  -rf /etc/emqx
  385  rm -rf /etc/emqx
  386  rm -rf /var/lib/emqx
  387  dpkg --remove --force-remove-reinstreq emqx-enterprise
  388  dpkg --configure -a
  389  apt update
ain restricted universe multiverse'
  391  apt upgrade
  392  ls
  393  cd testdata/
  394  ls
  395  dpkg -i libc6_2.40-3_amd64.deb 
  396  dpkg -i libstdc++6_14.2.0-5_amd64.deb 
  397  dpkg -i libc6_2.40-3_amd64.deb 
  398  sudo dpkg -i --force-depends libc6_2.40-3_amd64.deb
  399  sudo dpkg -i --force-confold libc6_2.40-3_amd64.deb
  400  sudo dpkg -i --force-all libc6_2.40-3_amd64.deb
  401  ls
  402  dpkg -i libsasl2-2_2.1.28+dfsg1-8_amd64.deb 
  403  sudo dpkg -i --force-all libsas
  404  sudo dpkg -i --force-all libsasl2-2_2.1.28+dfsg1-8_amd64.deb 
  405  sudo dpkg -i --force-all libssl3t64_3.3.2-1_amd64.deb 
  406  ls
  407  sudo dpkg -i --force-all libstdc++6_14.2.0-5_amd64.deb 
  408  sudo apt-get install -f
  409  dpkg -i libsasl2-2_2.1.28+dfsg1-8_amd64.deb 
  410  apt-get update
  411  apt-get upgrade
  412  apt --fix-broken install
  413  apt-get install libsasl2-modules-db
  414  dpkg -i --force-depends libsasl2-2_2.1.28+dfsg1-8_amd64.deb
  415  apt-get install ./libsasl2-2_2.1.28+dfsg1-8_amd64.deb
  416  apt-get install libsasl2-modules-db=2.1.28+dfsg1-8
  417  cd ..
  418  cd download/
  419  dpkg -i emqx-enterprise-5.8.0-ubuntu24.04-amd64.deb 
  420  apt-get install libodbc2
  421  sudo apt --fix-broken install

```