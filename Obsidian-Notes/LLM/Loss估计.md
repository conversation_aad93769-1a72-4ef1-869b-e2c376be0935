
在karpathy的模型中, 我们估计了初始化的模型的loss,约为-ln(1/65),怎么做到的呢?

output: 是一个TxV的矩阵.实际上就是$XW_{e}$,也就是嵌入后的词嵌入矩阵.

output:
$$
Output: \begin{pmatrix}
s_{11}\quad \dots \quad s_{1V}\\
\dots \\
s_{T1} \quad \dots \quad s_{TV}
\end{pmatrix}
$$
loss计算:loss计算其实就是

对于target:$$
y=(y_{1},\dots ,y_{T})
$$
第i行的loss:

$$
loss_{i}=-\log\left( \frac{1}{s_{iy_{i}}} \right)
$$
总的loss:
$$
Loss=\frac{1}{T}\sum ^{T}_{i=1}loss_{i}=\frac{1}{T}\sum ^{T}_{i=1}-\log\left( \frac{1}{s_{iy_{i}}} \right)
$$
假设embedding是均匀初始化, 也就是每一行的得分都一样,也就是每行计算出的logits都是1/V也就是1/65

那么单行的loss就是-log(1/65)

那么最终的loss也就是-log(1/65)

那么问题又来了, 为什么不是这个呢?

首先, embedding的初始化是一个正态分布, 这导致分数并不是严格的1/65, 而是会因为初始化的大小而改变. 尤其是这一行有很大的权重的时候, 那么我们很有可能就采样到小概率,比如1/100.

具体的计算, 就留着当今晚作业吧.

