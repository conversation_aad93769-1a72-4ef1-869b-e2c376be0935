the model consists of three components: 1x input layer, Nxdecoder block layer, 1xoutput layer.

# 1. input layer

Input is a sequence of tokens: $X=(x_{1},x_{2},\dots,x_{T})$where $T$ is the sequen size and $x_{t} \in {1,2,\dots,V}$, where V is the vocab size.

# 1.1 Embedding & Pos Embedding

Each token $x_t$ is mapped to its corresponding vector via a lookup in the embedding matrix $W_{e}$, forming a embeded matrix $E$.

$$
E=Embedding(X) \in R^{T\times d_{m}}
$$
then, mebeded matrix will plus a positional matrix $PE$ to get ouput $X^{(0)}$:
$$
X^{(0)}=E+PE
$$
where

- $PE \in R^{T\times d_{m}}$
- $X^{(0)}\in R^{T\times d_{m}}$

# 2. decoder block

for the l-th decoder block, the input is $X^{(l-1)}$, output is $X^{(l)}$.

## 2.1 attention

simplify the attention machnism to one head.

pre layernorm:

$$
X^{(l-1)}_{norm}=LayerNorm(X^{(l-1)})
$$

Q,K,V:

$$
Q=X^{(l-1)}_{norm}W^{(l)}_{Q}
$$
$$
K=X^{(l-1)}_{norm}W^{(l)}_{K}
$$
$$
V=X^{(l-1)}_{norm}W^{(l)}_{V}
$$
where:

$$
Q,K,V \in R^{T\times d_{m}}
$$
$$
W^{(l)}_{Q},W^{(l)}_{K},W^{(l)}_{V}\in R^{d_{m}\times d_{m}}
$$

attention score:

$$
Attention=Softmax\left( \frac{QK^{T}}{\sqrt{ d_{k} }} +M\right)V \in R^{T\times d_{m}}
$$
residual connection:
$$
X_{atten}=Attention+X^{(l-1)}
$$

## 2.2 FFN

pre-layer norm

$$
X_{atten,norm}=LayerNorm(X_{atten})
$$
FFN

$$
FFN(X_{atten,norm})=GELU(X_{atten,norm}W^{(l)}_{1}+b^{(l)}_{1})W^{(l)}_{2}+b^{(l)}_{2}
$$
output
$$
X^{(l)}=X_{atten}+FFN(X^{(l)}_{norm'})
$$
# 3. output

final layer norm:

$$
X^{(N)}_{norm}=LayerNorm(X^{(N)})
$$
logits:

$$
Logits=X^{(N)}_{norm}W^{T}_{e}\in R^{T\times V}
$$
output:
$$
Output=Softmax(Logits)
$$
