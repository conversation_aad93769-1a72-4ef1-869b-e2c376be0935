the model consists of three main parts: 1x input layer, a core layer composed of Nx deocder block, 1x output layer.

# 1. Input layer

Input: sequence of tokens:  $X=(x_{0},x_{1},\dots,x_{T})$, where $x_{t}\in{1,\dots,V}$. 

- $V$: vocab size
- $T$: input sequence length

## 1.1 Embedding & Pos Embedding

Each token $x_{t}$ is mapped to its corrsponding vector, via a lookup in the embedding table $W_{e}$ ,where $W_{e}\in R^{V\times d_{m}}$, forming a Embedding matrix $E$. 

$$
E=Embedding(X)\in R^{T\times d_{m}}
$$


Positional embedding matrix $PE$ is add to the embedding matrix, the sum of $E$ and $PE$ forms the output of input layer: $X^{(0)}$

$$
X^{(0)}=E+PE \in R^{T\times d_{m}}
$$

# 2. Decoder block

for the l-th block, the input is $X^{(l-1)}$, output is $X^{(l)}$

**LayerNorm**:

$$
X^{(l-1)}_{norm}=LayerNorm(X^{(l-1)})
$$

Projection to Q, K, V:

$$
Q=X^{(l-1)}_{norm}W^{(l-1)}_{Q}\in R^{T\times dm}
$$
$$
K=X^{(l-1)}_{norm}W^{(l-1)}_{K}\in R^{T\times dm}
$$
$$
V=X^{(l-1)}_{norm}W^{(l-1)}_{V}\in R^{T\times dm}
$$
Masked Attention:

$$
Attention = softmax\left( \frac{QK^T}{\sqrt{ d_{k} }}+M \right)V
$$
residual connection:

$$
X_{atten}=X^{(l-1)}+Attention \in R^{T\times d_{m}}
$$
FFN:
$$
X^{(l-1)}_{norm'}=LayerNorm(X_{atten})
$$
$$
FFN(X^{(l-1)}_{norm'})=GELU(X^{(l-1)}_{norm'}W^{(l-1)}_{1}+b^{(l-1)}_{1})W^{(l-1)}_{2}+b^{(l-1)}_{2}
$$

where $W^{(l-1)}_{1}\in R^{d_{m}\times d_{ff}}$, $b^{(l-1)}\in R^{d_{ff}}$,  $W^{(l-1)}_{2}\in R^{d_{ff}\times d_{m}}$, $b^{(l-1)}_{2}\in R^{d_{m}}$

residual connection:
$$
X^{(l)}=X_{atten}+FFN(X^{(l-1)}_{norm'})
$$
# 3. Output

LayerNorm
$$
X^{(N)}_{norm}=LayerNorm(X^{(N)})
$$
Logits

$$
Logits=X^{(N)}_{norm}W^T_{e} \in R^{T\times V}
$$
Prob
$$
Prob=Softmax(Logits)\in R^{T\times V}
$$