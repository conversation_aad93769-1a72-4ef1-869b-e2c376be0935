/* Settings panel */

.setting-item.hidden {
    display: none;
}

.setting-item.setting-item-heading .latex-suite-settings-icon {
    margin-right: var(--size-4-2);
    display: inline-flex;
}

.setting-item.setting-item-heading:has(.latex-suite-settings-icon) {
    border-bottom: 1px solid var(--background-modifier-border);
}

.setting-item.setting-item-heading:has(.latex-suite-settings-icon) + .setting-item {
    border-top: none;
}

.setting-item.setting-item-heading:has(.latex-suite-settings-icon) ~ .setting-item:not(.setting-item-heading), .latex-suite-snippet-variables-setting + .setting-item-control {
    width: calc(100% - 26px);
    margin-left: 26px;
}

.latex-suite-snippet-variables-setting .setting-item-control {
    height: 120px;
}

.latex-suite-snippet-variables-setting .setting-item-control textarea {
    width: 100%;
    height: 100%;
}

.snippets-text-area, .latex-suite-snippet-variables-setting {
    display: inline-block;
}

.snippets-text-area .setting-item-info, .latex-suite-snippet-variables-setting .setting-item-info {
    margin-bottom: 0.75rem;
}

.snippets-text-area .setting-item-control {
    flex-direction: column;
    align-items: flex-end;
}

.snippets-editor-wrapper {
    width: 100%;
    margin-bottom: 0.75rem;
}

.snippets-editor-wrapper .cm-editor {
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    font-size: var(--font-inputs);
    height: 20em;
    outline: none !important;
    text-align: left;
}

.snippets-editor-wrapper .cm-line, .snippets-editor-wrapper .cm-lineNumbers {
    font-family: var(--font-monospace);
}

.snippets-footer {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.snippets-editor-validity {
    display: flex;
    align-items: center;
}

.snippets-editor-validity-indicator {
    color: white;
    display: inline-block;
    border-radius: 1em;
    margin-right: 10px;
    cursor: default;
    visibility: hidden;
}

.snippets-editor-validity-indicator svg {
    width: 16px !important;
    height: 16px !important;
}

.snippets-editor-validity-indicator:hover {
    color: white;
}

.snippets-editor-validity-indicator.valid {
    background-color: var(--color-green);
    visibility: visible;
}

.snippets-editor-validity-indicator.invalid {
    background-color: var(--color-red);
    visibility: visible;
}

.snippets-editor-buttons {
    display: flex;
    flex-direction: row;
}

.latex-suite-confirmation-modal .setting-item {
    border: none;
}

.search-input-container input.latex-suite-location-input-el {
    width: initial;
}

/*
Snippet color classes.
*/

/* These extra selectors enforce their color on all children, because CodeMirror does weird nesting of spans when
nesting multiple decorations. */

.latex-suite-snippet-placeholder {
    border-radius: 2px;
    background-color: var(--placeholder-bg);
    outline: var(--placeholder-outline) solid 1px;
}

.latex-suite-snippet-placeholder-0, span.latex-suite-snippet-placeholder-0 span {
    --placeholder-bg: #87cefa2e;
    --placeholder-outline: #87cefa6e;
}

.theme-dark .latex-suite-snippet-placeholder-0, span.latex-suite-snippet-placeholder-0 span {
    --placeholder-outline: #87cefa43;
}

.latex-suite-snippet-placeholder-1, span.latex-suite-snippet-placeholder-1 span {
    --placeholder-bg: #ffa50033;
    --placeholder-outline: #ffa5006b;
}

.theme-dark .latex-suite-snippet-placeholder-1, span.latex-suite-snippet-placeholder-1 span {
    --placeholder-outline: #ffa5004d;
}

.latex-suite-snippet-placeholder-2, span.latex-suite-snippet-placeholder-2 span {
    --placeholder-bg: #00ff0022;
    --placeholder-outline: #00ff0060;
}

.theme-dark .latex-suite-snippet-placeholder-2, span.latex-suite-snippet-placeholder-2 span {
    --placeholder-outline: #00ff003d;
}


/* Conceal */

span.cm-math.cm-concealed-bold {
    font-weight: bold;
}

span.cm-math.cm-concealed-underline {
    text-decoration: underline;
}

span.cm-math.cm-concealed-mathrm, sub.cm-math.cm-concealed-mathrm {
    font-style: normal;
}


/* Conceal superscripts without changing line height */
sup.cm-math {
    line-height: 0;
}

sup.cm-math, sub.cm-math {
    font-style: italic;
}


/* Inline math tooltip styling */

.theme-light .cm-tooltip.cm-tooltip-cursor {
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.028), 0px 3.4px 6.7px rgba(0, 0, 0, .042), 0px 5px 20px rgba(0, 0, 0, .07);
}

.theme-dark .cm-tooltip.cm-tooltip-cursor {
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.1),
    0px 3.4px 6.7px rgba(0, 0, 0, 0.15),
    0px 0px 30px rgba(0, 0, 0, 0.27);
}


/* Highlight brackets */
.theme-light .latex-suite-highlighted-bracket, .theme-light .latex-suite-highlighted-bracket [class^="latex-suite-color-bracket-"] {
    background-color: hsl(var(--accent-h), var(--accent-s), 40%, 0.3);
}

.theme-dark .latex-suite-highlighted-bracket, .theme-dark .latex-suite-highlighted-bracket [class^="latex-suite-color-bracket-"] {
    background-color: hsl(var(--accent-h), var(--accent-s), 70%, 0.6);
}


/* Color matching brackets */

.theme-light .latex-suite-color-bracket-0, .theme-light .latex-suite-color-bracket-0 .cm-bracket {
    color: #527aff;
}

.theme-dark .latex-suite-color-bracket-0, .theme-dark .latex-suite-color-bracket-0 .cm-bracket {
    color: #47b8ff;
}

.theme-light .latex-suite-color-bracket-1, .theme-light .latex-suite-color-bracket-1 .cm-bracket {
    color: #ff50b7;
}

.theme-dark .latex-suite-color-bracket-1, .theme-dark .latex-suite-color-bracket-1 .cm-bracket {
    color: #ff55cd;
}

.theme-light .latex-suite-color-bracket-2, .theme-light .latex-suite-color-bracket-2 .cm-bracket {
    color: #69ba00;
}

.theme-dark .latex-suite-color-bracket-2, .theme-dark .latex-suite-color-bracket-2 .cm-bracket {
    color: #73ff63;
}

/* .latex-suite-color-bracket-3 {
    color: #8de15c;
} */
