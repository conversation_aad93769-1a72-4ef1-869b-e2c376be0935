{"main": {"id": "4302fcb2ff30983c", "type": "split", "children": [{"id": "56a52265263888c9", "type": "tabs", "children": [{"id": "614122638bea5f8e", "type": "leaf", "state": {"type": "markdown", "state": {"file": "今明任务.md", "mode": "source", "source": false, "backlinks": false}, "icon": "lucide-file", "title": "今明任务"}}, {"id": "9b950a5f4df0c8d3", "type": "leaf", "state": {"type": "markdown", "state": {"file": "技术栈/docker/基本用法.md", "mode": "source", "source": false, "backlinks": false}, "icon": "lucide-file", "title": "基本用法"}}, {"id": "0ef4dc42692e18e1", "type": "leaf", "state": {"type": "markdown", "state": {"file": "未命名 1.md", "mode": "source", "source": false, "backlinks": false}, "icon": "lucide-file", "title": "未命名 1"}}, {"id": "62b9563516b2240f", "type": "leaf", "state": {"type": "markdown", "state": {"file": "杨博中周报2025-07-25.md", "mode": "source", "source": false, "backlinks": false}, "icon": "lucide-file", "title": "杨博中周报2025-07-25"}}], "currentTab": 3}], "direction": "vertical"}, "left": {"id": "5c8677c5520e9559", "type": "split", "children": [{"id": "4dcffda7461c47dc", "type": "tabs", "children": [{"id": "d9e201c560876381", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "文件列表"}}, {"id": "cfd56fc7e8728160", "type": "leaf", "state": {"type": "search", "state": {"query": "tag:#review", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "搜索"}}, {"id": "cf7d9cda7e458f03", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 291.49999618530273}, "right": {"id": "ea7ede03733492df", "type": "split", "children": [{"id": "5bc881c9789ebaab", "type": "tabs", "children": [{"id": "5a6fa1668de64e99", "type": "leaf", "state": {"type": "backlink", "state": {"file": "技术栈/rag/RAG优化方法.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "RAG优化方法 的反向链接列表"}}, {"id": "8d7afe92f122d867", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "技术栈/rag/RAG优化方法.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "RAG优化方法 的出链列表"}}, {"id": "a05204cc7e362e97", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "标签"}}, {"id": "5fee90077275ef98", "type": "leaf", "state": {"type": "outline", "state": {"file": "技术栈/rag/RAG优化方法.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "RAG优化方法 的大纲"}}, {"id": "e616e20a7deea539", "type": "leaf", "state": {"type": "review-queue-list-view", "state": {}, "icon": "SpacedRepIcon", "title": "笔记复习序列"}}, {"id": "a676ae6ce6f8b2e7", "type": "leaf", "state": {"type": "copilot-chat-view", "state": {}, "icon": "message-square", "title": "Copilot"}}], "currentTab": 5}], "direction": "horizontal", "width": 425.5}, "left-ribbon": {"hiddenItems": {"switcher:打开快速切换": false, "graph:查看关系图谱": false, "canvas:新建白板": false, "daily-notes:打开/创建今天的日记": false, "templates:插入模板": false, "command-palette:打开命令面板": false, "obsidian-excalidraw-plugin:新建绘图文件": false, "remotely-save:Remotely Save": false, "obsidian-spaced-repetition:复习卡片": false, "copilot:Open Copilot Chat": false}}, "active": "a676ae6ce6f8b2e7", "lastOpenFiles": ["copilot-conversations/hi@20250725_214922.md", "copilot-conversations", "copilot-custom-prompts/Rewrite as tweet thread.md", "copilot-custom-prompts/Rewrite as tweet.md", "copilot-custom-prompts/Remove URLs.md", "copilot-custom-prompts/Generate glossary.md", "copilot-custom-prompts/Generate table of contents.md", "copilot-custom-prompts/Make longer.md", "copilot-custom-prompts/Make shorter.md", "copilot-custom-prompts/Emojify.md", "copilot-custom-prompts/Explain like I am 5.md", "copilot-custom-prompts/Simplify.md", "copilot-custom-prompts/Summarize.md", "copilot-custom-prompts/Translate to Chinese.md", "copilot-custom-prompts/Fix grammar and spelling.md", "copilot-custom-prompts", "未命名.md", "杨博中周报2025-07-25.md", "未命名 1.md", "没有探究的问题.md", "transformer计算推导.md", "深思考答题 杨博中.md", "2123.md", "技术栈/docker/基本用法.md", "技术栈/rag/RAG优化方法.md", "技术栈/linux command/screen.md", "Excalidraw/Drawing 2025-07-23 17.25.37.excalidraw.md", "技术栈/python/优秀实践tips.md", "项目/agent工作流框架", "项目", "transformer-canvas.canvas", "LLM", "技术栈/rag", "image/Pasted image 20250718223423.png", "image/Pasted image 20250714083606.png", "esg数据处理流程.svg", "启发/diary/Untitled", "image/Pasted image 20250705142237.png", "image/Pasted image 20250713161150.png", "image/Pasted image 20250713161110.png", "content/vscode插件推荐/images", "content/vscode插件推荐", "image/Pasted Image 20250705142551_966.png", "<svg aria-roledescription=\"class\" role=\".svg", "image/Pasted image 20250623082257.png", "技术栈/llm", "image/Pasted image 20250630081717.png", "Untitled 1.canvas", "myapps/Untitled 1.canvas", "未命名 1.canvas", "未命名.canvas"]}