
# 1. 用法

这里只考虑init index和query. 可能有的没写.
## 1.1 使用pip安装的用法

之间看官网文档

[Microsoft Research Blog - GraphRAG](https://microsoft.github.io/graphrag/blog_posts/)
## 1.2 使用源码安装的用法

graphrag在最新版本使用了poetry来管理. 无法直接命令行运行graphrag进行init index等操作.

### 方法1：使用Poetry运行命令

对于初始化：

```
poetry run python -m graphrag init --root ./your_project
```

对于索引：

```
poetry run python -m graphrag index --root ./your_project
```

### 方法2：使用Poetry的poe任务

GraphRAG在pyproject.toml中定义了便捷的poe任务： pyproject.toml:139-143

```
# 初始化  
poetry run poe init --root ./your_project  
  
# 索引  
poetry run poe index --root ./your_project  
  
# 查询  
poetry run poe query --root ./your_project
```





# 2. 代码记录

## 2.1 workflow相关

- 普通的index的workflow如下:/home/<USER>/debug/graphrag/graphrag/index/workflows/factory.py
![[Pasted image 20250713161150.png]]

# 3. Neo4j用法

查看所有节点（带限制）
MATCH (n) RETURN n LIMIT 20

查看节点标签和属性
MATCH (n) RETURN labels(n), properties(n), id(n) LIMIT 20

查看节点 + 关系 + 权重
MATCH (a)-[r]->(b) RETURN a.id, r.weight, b.id LIMIT 20

显示图形界面中的节点名
MATCH (n) SET n.name = n.id RETURN n LIMIT 20

查看所有节点数量
MATCH (n) RETURN count(n)

查看所有关系数量
MATCH ()-[r]->() RETURN count(r)

查看所有节点标签
CALL db.labels()

查看所有关系类型
CALL db.relationshipTypes()

. 删除所有数据（慎用）
MATCH (n)
DETACH DELETE n