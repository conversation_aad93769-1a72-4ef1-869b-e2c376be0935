[全网首发！小白也能读懂的GraphRAG知识图谱全流程解析，多图预警！ - 53AI-AI知识库\|大模型知识库\|大模型训练\|智能体开发](https://www.53ai.com/news/knowledgegraph/2024081336042.html)

![[Pasted image 20250331092401.png]]

---

## 流程解析

### 1. create_base_text_units 

chunking

### 2. create_base_extracted_entities

extract entities

- entities有类型设置,比如` ['organization', 'person', 'geo', 'event']`

> GraphRAG会对LLM的输出结果进行后处理post_processing，最终形成Graph对象的。我们先看一下实体（entities），每一个实体都有四个主要的属性：name、description、source_id 和 type。
> 
>1. Name：这是实体的名称。
>2. Description：对实体的描述。
>3. Source_id：在此情况下，source_id是指那些生成这个特定实体的数据块(chunk)的识别号。
>4. Type：实体的类型。



