

### **列式、结构化、高效压缩**

| 维度            | Parquet                           | CSV               |
| ------------- | --------------------------------- | ----------------- |
| **Schema 支持** | ✅ 强类型 Schema（字段名 + 类型）            | ❌ 只能通过第一行猜 Schema |
| **存储格式**      | ✅ 列式存储（Columnar）                  | ❌ 行式存储（Row-based） |
| **压缩效率**      | ✅ 高效编码 + 按列压缩（如 Snappy, Gzip）     | ❌ 文本无压缩优势         |
| **读取性能**      | ✅ 支持投影优化（只读需要的列）                  | ❌ 必须解析整行          |
| **嵌套结构**      | ✅ 支持 Map/Array 等复杂类型              | ❌ 无法原生表示          |
| **跨平台兼容性**    | ✅ 广泛支持 Spark/Hive/Presto/DuckDB 等 | ✅ 通用但功能有限         |

---

- **Parquet 是自描述的**：它把数据和元数据（Schema）一起写入文件，无需额外定义。
- **适合列式计算引擎**：如 Spark SQL、Presto、DuckDB 等，天然对列操作友好。
- **CSV 是“人类可读”但不适合机器处理**：解析慢、容易出错、难以处理缺失值或特殊字符。

---
- **Parquet** 更适合：
  - 多阶段流水线处理
  - 不同语言/系统间的数据交换（Python → Java → SQL）
  - 数据湖架构中的统一存储层

---


> **Parquet 提供了结构化、高效压缩和列式访问能力，是比 CSV 更适合大数据处理和系统间通信的中间格式。**
