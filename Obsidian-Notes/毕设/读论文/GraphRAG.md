微软的文章,主要作者都是微软研究员

![[Pasted image 20250511230928.png]]

----

## 概述

graphrag解决了什么问题? 对于概述性问题, graphrag通过 1. 建立图 2.建立社区 回答了他的问题.

关于graph的建立过程:

1. 提取节点和边
2. 构建图
3. 划分层次化社区
4. 生成社区报告

关于graph检索过程

1. 后面详细说

## Methods

### 构建过程

#### 1. chunk->node & edge

这里说说细节...

1. llm会提取entity,relationship,除此之外,还会给每个实体和relationship都加上一段description.
2. 每个实体还会附加claims,类似属性图,就是给实体增加一些描述

e.g.

这段话:

>NeoChip公司（NC）的股票在新科技交易所上市交易的第一周大幅上涨。然而，市场分析师警告称，这家芯片制造商的公开亮相可能并不反映其他科技公司首次公开募股的趋势。NeoChip公司此前是一家私人企业，于2016年被量子系统公司收购。这家创新型半导体公司专注于为可穿戴设备和物联网设备生产低功耗处理器。

提取出实体和关系:

- 实体“NeoChip”，描述为“NeoChip是一家上市公司，专注于为可穿戴设备和物联网设备生产低功耗处理器”。
- 实体“Quantum Systems”，描述为“Quantum Systems是一家曾拥有NeoChip的公司”。
- NeoChip和Quantum Systems之间的关系，描述为“Quantum Systems在2016年至NeoChip成为上市公司期间拥有NeoChip”。

提取claims:

- NeoChip的股票在新科技交易所上市交易的第一周大幅上涨。
- NeoChip在新科技交易所首次公开上市。
- 量子系统公司在2016年收购了NeoChip，并一直持有其所有权，直到NeoChip上市。

**关于实体的提取**

实体的提取可以在prompt中指定类型,比如人员,组织,地点,等等.
#### 2. merge nodes & edges

- 如何聚合?

点直接聚合, 点的描述也聚合在一起. 边也是一样,但是重复的边成为边的权重. claims也是一样.

- 相同实体如何匹配? ^0d560f

精确字符串匹配. 即使相同实体不是一个字符串,也就是说没有聚合到一起,也没事. graphrag声称可以抵抗这种错误.

### 3. community detect

迭代划分社区,直到 leaf community.

### 4. community report generation

两种社区:

1. 叶子社区
	对社区内边,赋予优先级,然后降序排列对应节点,边,声明.优先级通过度确定.
2. 非叶子社区
	先当作叶子社区看待.如果内容过多超出context,则对子社区摘要令牌数量排序,迭代用摘要替换其对应元素
### 回答过程

由于摘要是多层次的,所以每个层次的摘要的都能用来回答,并且理论上会有一个层次,既能提供最多的细节也能充分涵盖提问范围.

对于一个用户查询,针对于某一层的提问:

	1. 将社会摘要切分成块,打乱
	2. (貌似是对于每个块)生成摘要的答案,以及伴随一个分数. 分数为0的将会被筛选掉.
	3. 把所有答案按照分数降序排列,送给大模型,生成最终答案

### 实验
#### 实验1

对于每个数据集, 生成k个角色,每个角色n个task,每个task m个问题. 他使用了k=5,n=5,m=5. 并且每个问题也提问多次.

使用llm-as-a-judge做评测.每次评测将问题本身以及两个系统的答案送给llm,要求其在四个维度上判断胜负(或者平局)

作者使用六个rag系统做比较, 四个层级不同的graphrag以及直接用source text做mapreduce,以及普通rag方法.

> [!NOTE] 
> 普通rag方法肯定不行,效果很差. sgs一开始用的就是这样.

#### 实验2

实验二类似一个补充分析, 主要是验证 comprehensiveness and diversity. 作者定义这两个指标,然后在答案上做验证. 具体来说

>1. 全面性：通过计算在每种条件下生成的答案中提取的声明的平均数量来衡量。
>2. 多样性：通过对每个答案的声明进行聚类并计算聚类的平均数量来衡量。

### 实验结果

实验1

在 综合性,多样性上,graphrag效果好,在mpowerment上,效果大差不差,在简洁性上,naive rag最好

实验2类似补充验证, 这里不在多说.

此外,作者指出graphrag消耗的token比source text少,指出了其扩展性上的优势.

## 问题

1. 实体怎么去重?[[#^0d560f]]
