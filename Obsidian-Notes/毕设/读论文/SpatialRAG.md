[Yu: Spatial-rag: Spatial retrieval augmented generation... - Google Scholar](https://scholar.google.com/scholar?cluster=14543807454615667313&hl=en&as_sdt=2005&as_ylo=2025&as_yhi=2025)

Da<PERSON> Yu PhD student, [Emory University](https://scholar.google.com/citations?view_op=view_org&hl=en&org=7693822459085397235)
[‪Gengchen Mai‬ - ‪Google Scholar‬](https://scholar.google.com/citations?user=X2Wfl1UAAAAJ&hl=en&oi=sra) [University of Texas at Austin](https://scholar.google.com/citations?view_op=view_org&hl=en&org=14823011757688503605)
<PERSON> [‪Liang Zhao‬ - ‪Google Scholar‬](https://scholar.google.com/citations?hl=en&user=qnvyqtwAAAAJ&view_op=list_works&sortby=pubdate) 应该是导师, 多个a会paper以及b bestpaper best poster等

----


![[spatialrag流程图|601]]



“我想在从A点到B点的路上，找一家适合家庭聚餐的意大利餐厅，最好不要太吵。”

1. **阶段一：找到所有在路上的餐厅 (稀疏空间检索)**
    - **LLM 理解问题 
        - **识别关键地点和物体 ：**
            - “A点” (时代广场) -> 识别为 Point。
            - “B点” (中央公园南门) -> 识别为 Point。
            - “路 ” -> 识别为需要构建 Polyline (路线)。
            - “餐厅 ” -> 识别为目标物体类型，通常是 Point。
        - **判断空间操作 
            - “在...路上” 暗示需要先确定 A到B的路线，然后找路线附近的点。这对应空间数据库中的操作：
                1. ST_MakeLine(PointA_geom, PointB_geom) -> 创建路线几何。
                2. ST_Buffer(route_geom, distance) -> 在路线周围创建一个缓冲区（比如，路线两侧各扩展 500 米形成一个带状区域）。
                3. ST_Intersects(restaurant_geom, buffer_geom) -> 判断餐厅是否落在这个缓冲区。
        - **估算参数 ：**
            - “路上”  -> LLM 推断缓冲区距离可能是 500米 (适合步行)。
    - **执行数据库查询 (Spatial SQL Execution)：**
        - 系统根据上面，生成类似这样的 SQL
		
            ```
            SELECT restaurant.*
            FROM restaurants_table restaurant,
                 (SELECT ST_Buffer(ST_MakeLine(
                                      (SELECT geom FROM locations WHERE name = '时代广场'),
                                      (SELECT geom FROM locations WHERE name = '中央公园南门')
                                  ), 0.005) AS route_buffer -- 假设0.005度约等于500米
                 ) AS buffered_route
            WHERE ST_Intersects(restaurant.geom, buffered_route.geom)
              AND restaurant.category = '意大利餐厅';
            ```
			
        - 这个查询会在**空间数据库**（ PostGIS ）中执行。
        - **输出：** 一个列表，包含了所有在从时代广场到中央公园南门路线上500米范围内的意大利餐厅。比如得到10家符合条件的餐厅。
2. **阶段二：给这些路上的餐厅打分 (混合空间对象排序)**
    - **目标：** 对找到的10家餐厅，评估它们与“在A到B路上”这个空间需求的契合度。
    - **打分方式：**
        1. **几何精确度打分 ：**
            - 餐厅如果正好在路线的缓冲区内，得分就很高 (比如 1.0)。
            - 如果因为某些原因（比如缓冲区设置得非常小），一个餐厅虽然在附近但严格来说在缓冲区外一点点，它的得分会根据距离远近而降低。比如，非常接近路线但刚好在500米缓冲区外的，得分可能是0.8；远一点的可能是0.5。
        2. **文本描述空间相关性打分 ：**
            - **LLM 提取空间语义：**
                - 从用户问题中提取：“从A点到B点的路上”。
                - 对每家候选餐厅，查看其描述文本,这个文本是空间描述文本,而非评论等.
            - **语义相似度计算：**这个就是老生常谈了，就是向量相似度
    - **综合空间得分：** 将上面的几何分和文本语义分加权平均，得到一个综合的空间相关性分数。例如，餐厅X几何上完美符合，文本也提到方便，总分高；餐厅Y几何上符合，但文本没提路径相关信息，总分略低。
3. **阶段三：结合用户其他喜好，选出最佳餐厅并生成回答 (多目标生成)**
    - 这一步目标就是从空间上合适的餐厅中，选出最满足用户“适合家庭聚餐”、“不要太吵”这些非空间偏好的，并给出人性化回答。
    - **步骤：**
        1. **计算语义偏好得分  (类似稠密空间相关性打分，但关注点不同)：**
            - **LLM 提取语义偏好：**
                - 从用户问题中提取：“适合家庭聚餐”、“不要太吵”。
                - 对每家候选餐厅，查看其描述/评论文本（比如：“我们全家都很喜欢这里的氛围，虽然人多但隔音不错，不觉得吵闹。” 或者 “这家店音乐声很大，不太适合聊天。”）。
                - 将用户的“适合家庭”、“不吵”和餐厅评论中的相关描述转为向量，计算相似度。
                - 得到每家餐厅的语义偏好得分。
        2. **找到“两全其美”的候选 (帕累托前沿计算)：**
            - 现在每家餐厅都有两个分数：空间相关性得分 (fs) 和语义偏好得分 (fk)。
            - 系统会筛选出一组餐厅，它们都是“无法被轻易取代的”。比如，餐厅P空间分90，语义分80；餐厅Q空间分85，语义分85。这两者都可能是不错的选择。但如果餐厅R空间分70，语义分70，它就会被P和Q“压制”。
            - **输出：** 一个“帕累托最优”的餐厅列表。这些餐厅在空间和语义上做到了较好的平衡，没有一个餐厅能同时在两方面都比它们更好。
        3. **LLM 做最后决定：**
            - LLM 拿到这个帕累托最优列表，以及原始的用户问题。
            - LLM 会根据问题的整体语境来判断：用户是更看重“一定在路上”，还是“家庭友好”更重要？
            - LLM 从帕累托列表中选出它认为最合适的1-2家。
        4. **生成回答：**
            - LLM：“根据您的路线和偏好，我推荐‘Mama Mia意大利餐厅’。它就在您从时代广场去中央公园的路上，步行大约10分钟就能绕过去。很多家庭都喜欢那里，而且评论说虽然生意好但不会太吵。您觉得怎么样？”