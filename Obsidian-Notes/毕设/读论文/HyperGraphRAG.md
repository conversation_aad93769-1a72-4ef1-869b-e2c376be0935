作者 [<PERSON><PERSON> Luo - Homepage](https://lhrlab.github.io/) 老师[北京邮电大学主页平台管理系统 鄂海红--中文主页--首页](https://teacher.bupt.edu.cn/ehaihong/zh_CN/index.htm)

![[Pasted image 20250501163306.png|675]]

---

## 概述

使用超图储存文档结构,并且给出了配套的索引方法和检索方法.

### 创新点

1. 用llm抽取多元关系,将超图存放到二元数据库中. 一半存超图,一半存节点
2. 超图检索策略
3. hypergraph-guided generation mechanism. 后面说

### ==检索==


``
![[hypergraphrag检索|700]]

### ==实验==

![[Pasted image 20250501171914.png]]

回答四个问题:

研究问题1：超图关系聚合图（HyperGraphRAG）是否优于其他方法？ 
研究问题2：超图关系聚合图的主要组件是否起作用？
研究问题3：超图关系聚合图在不同领域构建的知识超图质量如何？
研究问题4：超图关系聚合图的效率和成本是多少？

**数据集**使用ultra domain(lightrag也用了)和一个international hypertension guidelines(国际高血压指南)做 medicine, agriculture, computer science, and law这几个方向的测试.

**Baselines**使用直接生成,传统rag,graphrag,lightrag

**评测指标**使用RAGAS[^1]和OG-RAG[^2]论文提出的Context Recall (C-Rec), Context Entity Recall (C-ERec), and Answer Relevance (A-Rel).**这三个指标有点神奇**

相关模型使用4o-mini和text-embedding-small

消融实验论证了 entity retrival, hyperedge retrival, chunk retrival的有效性

---

[^1]: Shahul Es, Jithin James, Luis Espinosa Anke, and Steven Schockaert. 2024. RAGAs: Automated evaluation of retrieval augmented generation. In Proceedings of the 18th Conference of the European Chapter of the Association for Computational Linguistics: System Demonstrations, pages 150–158, St. Julians, Malta. Association for Computational Linguistics.

[^2]: Kartik Sharma, Peeyush Kumar, and Yunqing Li. 2024. Og-rag: Ontology-grounded retrieval-augmented generation for large language models. Preprint, arXiv:2412.15235.
