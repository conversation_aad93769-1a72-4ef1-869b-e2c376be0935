# 文章
## langchain团队

Oct 19, 2024 |  [Memory for agents](https://blog.langchain.dev/memory-for-agents/)

### 关键观点

- 内存是特定于应用程序的
![[Pasted image 20250406193417.png]]

#### 记忆类型

##### Procedural Memory  程序记忆

	Procedural memory in Agents: the CoALA paper describes procedural memory as the combination of LLM weights and agent code, which fundamentally determine how the agent works.

	在实践中，我们很少看到（任何？）代理系统会自动更新其权重或重写其代码。但是，我们确实看到了一些代理更新其自身系统提示的示例。虽然这是最接近的实际示例，但它仍然相对不常见。

#### Semantic Memory  语义记忆



	Practically, we see this being done by using an LLM to extract information from the conversation or interactions the agent had. The exact shape of this information is usually application-specific. This information is then retrieved in future conversations and inserted into the system prompt to influence the agent’s responses.

	实际上，我们看到这是通过使用 LLM 从代理的对话或互动中提取信息来实现的。此信息的确切形式通常特定于应用程序。然后，此信息将在未来的对话中检索并插入到系统提示中，以影响代理的响应。

#####  Episodic Memory  情景记忆

	In practice, episodic memory is implemented as few-shot example prompting. If you collect enough of these sequences, then this can be done via dynamic few-shot prompting. This is usually great for guiding the agent if there is a **correct** way to perform specific actions that have been done before. In contrast, semantic memory is more relevant if there isn’t necessarily a correct way to do things, or if the agent is constantly doing new things so the previous examples don’t help much.

这个的意思感觉就是类似的事情的记忆.

#### 如何更新

1. in the hot path

	One way to update agent memory is [“in the hot path”](https://langchain-ai.github.io/langgraph/concepts/memory/?ref=blog.langchain.dev#writing-memories-in-the-hot-path). This is where the agent system explicitly decides to remember facts (usually via tool calling) before responding. This is the approach taken by ChatGPT.


2. in the background
	![[Pasted image 20250406215422.png]]
3. 借助用户反馈 (我感觉没啥用..)Another way to updating memory involves user feedback, which is particularly relevant to episodic memory. For example, If the user marks an interaction as a positive one, you can save that feedback to recall in the future.

如何评价这几种方式? 响应延迟和更新实时性

#### ref

[Cognitive Architectures for Language Agents](https://arxiv.org/html/2309.02427?_immersive_translate_auto_translate=1)
[# 如何看待Transactions on Machine Learning Research?](https://www.zhihu.com/question/505972792)


#### my thoughts

langchain借用了coala的观点定义记忆,并且指出memory是app specific的.他们团队已经在Feb 18, 2025基于此发布了memory相关 sdk,也就是说他们做的memory相关的内容,都是在这个抽象的基础上开发的.那岂不是也不能变通了? [LangMem SDK for agent long-term memory](https://blog.langchain.dev/langmem-sdk-launch/).

我的问题是,coala真的就是合适的mem建模吗,感觉并不是吧...?langmem有没有行之有效的实现?

#todo 学习langgraph & coala

## IBM

[What Is AI Agent Memory? \| IBM](https://www.ibm.com/think/topics/ai-agent-memory) 18 March 2025

这篇blog也是引用了coloa的工作,与langchain团队的略有不同的是,他记忆划分的稍微详细一点

##### 短期记忆

	STM 通常使用滚动缓冲区或[上下文窗口](https://www.ibm.com/think/topics/context-window)来实现，它们在被覆盖之前会保存有限量的最新数据。虽然这种方法可以提高短时间交互的连续性，但它不会保留会话之外的信息，因此不适合长期个性化或学习。

##### 长期记忆

	与短期记忆不同，LTM 旨在用于永久存储，通常使用数据库、 [知识图谱](https://www.ibm.com/think/topics/knowledge-graph)或[向量嵌入](https://www.ibm.com/think/topics/vector-embedding)来实现。这种类型的记忆对于需要历史知识的 AI 应用（例如个性化助理和推荐系统）至关重要。实现 LTM 的最有效技术之一是[检索增强生成](https://www.ibm.com/think/topics/retrieval-augmented-generation) (RAG)，其中代理从存储的知识库中获取相关信息以增强其响应。

##### Episodic memory  情景记忆

	情景记忆通常通过以结构化格式记录关键事件、动作及其结果来实现，以便代理在做出决策时可以访问。
##### Semantic memory  语义记忆

语义记忆负责存储结构化的事实知识，以便 AI 代理可以检索并用于推理。与处理特定事件的情景记忆不同，语义记忆包含事实、定义和规则等广义信息
  
AI 代理通常使用知识库、符号 AI 或[向量嵌入](https://www.ibm.com/think/topics/vector-embedding)来实现语义记忆，从而使它们能够高效地处理和检索相关信息。这种类型的记忆用于需要领域专业知识的实际应用中，例如法律 AI 助手、医疗诊断工具和企业知识管理系统。

#####  Procedural memory  程序记忆

AI agents learn sequences of actions through training, often **using reinforcement learning to optimize performance over time**. By **storing task-related procedures**, AI agents can reduce computation time and respond faster to specific tasks without reprocessing data from scratch.

#### ref

引用了langchain,langgraph,CoALA

# agent 框架的记忆实现

## langchain

[memory — 🦜🔗 LangChain documentation](https://python.langchain.com/api_reference/langchain/memory.html)

langchain内置了多种格式的记忆的支持:

`ConversationSummaryMemory` 提供了记忆的摘要

`ConversationEntityMemory`、`ConversationKGMemory` 提供了kg格式存储的支持

`VectorStoreRetrieverMemory` 向量类型的记忆支持

除此之外,还有:

`memory.entity.InMemoryEntityStore`

`memory.entity.RedisEntityStore`

`memory.entity.SQLiteEntityStore`

提供了对多种开发工具的支持.除此之外还有更多比如token级别记忆缓冲控制.但是总的来说,langchain提供了管理记忆的工具,但是对于其团队提到的memory的管理,暂时没有直接实现.而是放到了单独的langmem sdk中.[LangMem SDK for agent long-term memory](https://blog.langchain.dev/langmem-sdk-launch/)

*最新的文档中,其显示以上几种都要被废弃.留下这几种:*


[`memory.combined.CombinedMemory`](https://python.langchain.com/api_reference/langchain/memory/langchain.memory.combined.CombinedMemory.html#langchain.memory.combined.CombinedMemory "langchain.memory.combined.CombinedMemory")|Combining multiple memories' data together.
[`memory.readonly.ReadOnlySharedMemory`](https://python.langchain.com/api_reference/langchain/memory/langchain.memory.readonly.ReadOnlySharedMemory.html#langchain.memory.readonly.ReadOnlySharedMemory "langchain.memory.readonly.ReadOnlySharedMemory")|Memory wrapper that is read-only and cannot be changed.
[`memory.simple.SimpleMemory`](https://python.langchain.com/api_reference/langchain/memory/langchain.memory.simple.SimpleMemory.html#langchain.memory.simple.SimpleMemory "langchain.memory.simple.SimpleMemory")|Simple memory for storing context or other information that shouldn't ever change between prompts.
[`memory.vectorstore_token_buffer_memory.ConversationVectorStoreTokenBufferMemory`](https://python.langchain.com/api_reference/langchain/memory/langchain.memory.vectorstore_token_buffer_memory.ConversationVectorStoreTokenBufferMemory.html#langchain.memory.vectorstore_token_buffer_memory.ConversationVectorStoreTokenBufferMemory "langchain.memory.vectorstore_token_buffer_memory.ConversationVectorStoreTokenBufferMemory")Conversation chat memory with token limit and vectordb backing.

langchain sdk更新速度很快,可能后面上面部分会有新版本,或者对接langmem.

## LangMem

[LangMem SDK for agent long-term memory](https://blog.langchain.dev/langmem-sdk-launch/)

这个sdk就是之前提到的memory机制的实现.

### key question

langchain团队认为:

	Before adding memory, we think you should consider:

	- **What behavior should be learned (user-informed) vs. pre-defined?**
	- **What types of knowledge or facts should be tracked?**
	- **What conditions should trigger a memory to be recalled?**

哪些行为应该学习（用户知情）而不是预定义？
应该追踪哪些类型的知识或事实？
什么条件会触发记忆的回忆？

## LangGraph

[Memory](https://langchain-ai.github.io/langgraphjs/concepts/memory/#managing-long-conversation-history)

langgraph对记忆的支持基本上可以分为两部分:shorterm和longterm

LangGraph 将短期记忆作为代理状态的一部分进行管理,此状态通常可以包括对话历史记录以及其他状态数据，例如上传的文件、检索到的文档或生成的工件。

LangGraph 将长期记忆作为 JSON 文档存储在[存储库](https://langchain-ai.github.io/langgraphjs/concepts/persistence/#memory-store)中（ [参考文档](https://langchain-ai.github.io/langgraphjs/reference/classes/checkpoint.BaseStore.html) ）。每个记忆都组织在自定义 `namespace` （类似于文件夹）和独特的 `key` （如文件名）下。

langgraph也没有提供即插即用的memory接口.他提供了基本的长短期记忆的接口和操作支持,包括短期记忆的内置记忆更新,裁剪,以及低级别的(用什么数据库存储)的长期记忆支持.

# 论文工作
## MemGPT(Letta)

[\[2310.08560\] MemGPT: Towards LLMs as Operating Systems](https://arxiv.org/abs/2310.08560) 12 Oct 2023 
[MemGPT is now part of Letta \| Letta](https://www.letta.com/blog/memgpt-and-letta)  September 23, 2024
[GitHub - letta-ai/letta: Letta (formerly MemGPT) is the stateful agents framework with memory, reasoning, and context management.](https://github.com/letta-ai/letta)

- github日榜第一
- 转化为创业项目
- 比较老的工作,后面有新的


MemGPT直接将记忆分为两部分,in context和out of context.主上下文（短期记忆）/外部上下文（长期记忆）

Mem尝试直接把记忆分为两部分,然后由一个独立的LLM决定什么信息放进context中.借鉴了操作系统管理内存的方法--什么内容该放进虚拟内存,什么时候该移出.


![[Pasted image 20250407090225.png]]

#todo memgpt论文
## A-MEM
 [\[2502.12110\] A-MEM: Agentic Memory for LLM Agents](https://arxiv.org/abs/2502.12110) Mon, 17 Feb 2025 18:36:14 UTC (603 KB) | 还没发表
 
- 卡片式记忆构建,自动的记忆单元间连接
- 罗格斯大学 蚂蚁集团


- **整体思想：** A-MEM 从 Zettelkasten 方法中汲取灵感，实现动态和自我演化的记忆系统，使 LLM Agent 能够在没有预定操作的情况下保持长期记忆。
    
- **笔记构建 (Note Construction)：** 使用 LLM 驱动的方法构建结构化的记忆笔记，每个笔记包含原始交互内容、时间戳、LLM 生成的关键词、标签和上下文描述，以及链接的记忆集合。
    
- **链接生成 (Link Generation)：** 实现自主链接生成机制，使新的记忆笔记能够在没有预定义规则的情况下形成有意义的连接。通过计算相似度得分和利用 LLM 分析潜在连接，A-MEM 能够高效地识别和建立记忆之间的关系。
    
- **记忆演化 (Memory Evolution)：** 在为新记忆创建链接之后，A-MEM 基于其文本信息以及与新记忆的关系来演化检索到的记忆，从而实现持续的更新和新的连接，模仿人类的学习过程。
    
- **检索相关记忆 (Retrieve Relative Memory)：** 在每次交互中，A-MEM 执行上下文感知的记忆检索，以便为 Agent 提供相关的历史信息。通过计算查询嵌入和记忆笔记之间的相似度得分，A-MEM 能够检索出 k 个最相关的记忆，从而丰富 Agent 的推理过程。

## Zep
[\[2501.13956\] Zep: A Temporal Knowledge Graph Architecture for Agent Memory](https://arxiv.org/abs/2501.13956) Mon, 20 Jan 2025 16:52:48 UTC (22 KB)  | 还没发表

- 借助kg的memory
- 称memgpt为sota
- 转化为创业项目,github界面[Zep · GitHub](https://github.com/getzep)稍显冷清
- 用了自家另一个产品Graphiti


## MemoryScope