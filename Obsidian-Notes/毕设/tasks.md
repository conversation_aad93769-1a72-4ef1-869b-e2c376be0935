###  读论文

1. [x] [[HyperGraphRAG]]
2. [x] [[GraphRAG]]
3. [ ] [[Question Answering Over Spatio-Temporal Knowledge Graph]]

###  项目进度

1. graphrag复现[[graphrag 流程]]
	- [x] 跑通
	- [ ] 学源码 ->毕竟最后都得动手写, 尤其是还得学习一下这一套提取过程之类的.
		- [ ] 学习graphrag构建方式,以及datashaper用法
		- [ ] 在这个基础上,构建能提取空间,以及查询sql的rag
### 时空数据库调研

1. 地理空间作为基本的空间,是否要学习其怎么存储,搜索? 是

在1的基础上,还可以干什么?

	1. 如何建模非地理空间? 寻找抽象表示
	2. 代码实现. 如果能实现一个,基本的rag实现,貌似也挺好的. 比如,给一本小说,能把时间和空间索引好,甚至可以通过引入提示动态改进空间索引方式.比如通过提示某个地点的位置等.