帮我实现一个知识管理功能核心结构,其本质上是一个由知识点节点组成的有向无环图结构.

1. 每个知识点由知识内容, 课后复习, 用户总结等部分组成.
2. 所有的知识点就像包管理器一样, 要标明这个知识点依赖于哪个知识点,也就是要知道每个知识点的前置知识.
3. 知识点要具有分类能力,也就是给知识点打上tag, 实现知识点分类能力. 有可能一个知识点既属于a类也属于b类, 比如docker用法, 既属于a计算机技术也属于b运维技术

- 知识内容本身是一个markdown文件. 课后复习, 用户总结也是,新创建的知识点后面两项可以设置为空.

帮我实现:

增: 添加知识点功能
删: 删除知识点功能
改: 改动知识点内容
查:查询知识点功能
标记掌握程度: 标记知识点掌握程度, 1-10
等等, 此处保持可扩展性.


后面会要求你实现:

1. 知识点学习功能, 在浏览器中预览,学习知识点功能, 要拥有标注知识点的能力
2. 自动依赖知识生成功能, 



## 可视化

现在在前端可视化展示一下笔记内容.  技术栈你定, 我只负责界面以及功能. 我希望是这样:

1. 一个展示知识之间依赖关系的界面, 用图谱的方式展示知识间点之间依赖关系. 可以用类似obsidian那种方式. 左侧是图谱,右侧是知识点内容展示. 一开始是整个页面都展示图谱, 当我点击某个知识点后,右侧出现一栏展示知识点内容的界面,分为三栏, 分别是知识点内容, 两习题,以及用户笔记